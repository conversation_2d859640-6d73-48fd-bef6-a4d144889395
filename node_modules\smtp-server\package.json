{"name": "smtp-server", "version": "3.14.0", "description": "Create custom SMTP servers on the fly", "main": "lib/smtp-server.js", "scripts": {"test": "grunt", "update": "rm -rf node_modules package-lock.json && ncu -u && npm install"}, "author": "<PERSON><PERSON>", "license": "MIT-0", "dependencies": {"base32.js": "0.1.0", "ipv6-normalize": "1.0.1", "nodemailer": "7.0.3", "punycode.js": "2.3.1"}, "devDependencies": {"chai": "4.4.1", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "9.1.0", "grunt": "1.6.1", "grunt-cli": "1.5.0", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "mocha": "10.7.3", "pem": "1.14.8"}, "repository": {"type": "git", "url": "git://github.com/nodemailer/smtp-server.git"}, "bugs": {"url": "https://github.com/nodemailer/smtp-server/issues"}, "keywords": ["SMTP"], "engines": {"node": ">=12.0.0"}}