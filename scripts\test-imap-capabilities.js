// IMAP服务器能力检测工具
const net = require('net')
const tls = require('tls')

async function testImapCapabilities(host, port = 993, useSSL = true) {
  console.log(`🔍 检测IMAP服务器能力: ${host}:${port} (${useSSL ? 'SSL' : 'PLAIN'})`)
  console.log('='.repeat(60))

  return new Promise((resolve, reject) => {
    let socket
    let capabilities = []
    let authMethods = []
    let buffer = ''

    const cleanup = () => {
      if (socket) {
        socket.destroy()
      }
    }

    const timeout = setTimeout(() => {
      cleanup()
      reject(new Error('连接超时'))
    }, 10000)

    const parseCapabilities = (data) => {
      const lines = data.split('\r\n')
      for (const line of lines) {
        if (line.startsWith('* CAPABILITY')) {
          const caps = line.substring(12).trim().split(' ')
          capabilities = caps
          
          // 提取认证方法
          authMethods = caps.filter(cap => cap.startsWith('AUTH=')).map(cap => cap.substring(5))
          
          console.log('✅ 服务器能力:')
          console.log(`   支持的功能: ${caps.join(', ')}`)
          console.log(`   认证方法: ${authMethods.length > 0 ? authMethods.join(', ') : '未明确指定'}`)
          
          clearTimeout(timeout)
          cleanup()
          resolve({
            capabilities,
            authMethods,
            supportsSSL: caps.includes('STARTTLS') || useSSL,
            supportsPlain: authMethods.includes('PLAIN') || authMethods.length === 0,
            supportsLogin: authMethods.includes('LOGIN') || authMethods.length === 0
          })
        }
      }
    }

    const handleData = (data) => {
      buffer += data.toString()
      
      if (buffer.includes('* OK') || buffer.includes('* PREAUTH')) {
        console.log('✅ 服务器响应正常')
        // 发送CAPABILITY命令
        socket.write('A001 CAPABILITY\r\n')
      } else if (buffer.includes('* CAPABILITY')) {
        parseCapabilities(buffer)
      }
    }

    const handleError = (err) => {
      clearTimeout(timeout)
      cleanup()
      reject(err)
    }

    if (useSSL) {
      // SSL连接
      socket = tls.connect(port, host, {
        rejectUnauthorized: false
      }, () => {
        console.log('🔒 SSL连接已建立')
      })
    } else {
      // 普通TCP连接
      socket = net.connect(port, host, () => {
        console.log('🔗 TCP连接已建立')
      })
    }

    socket.on('data', handleData)
    socket.on('error', handleError)
    socket.on('end', () => {
      console.log('🔌 连接已断开')
    })
  })
}

async function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    console.log('用法: node test-imap-capabilities.js <服务器地址> [端口] [ssl]')
    console.log('例如: node test-imap-capabilities.js mail2.serv00.com 993 true')
    console.log('或者: node test-imap-capabilities.js mail2.serv00.com 143 false')
    return
  }

  const host = args[0]
  const port = args[1] ? parseInt(args[1]) : 993
  const useSSL = args[2] !== 'false'

  try {
    const result = await testImapCapabilities(host, port, useSSL)
    
    console.log('\n📋 检测结果:')
    console.log(`   SSL支持: ${result.supportsSSL ? '✅' : '❌'}`)
    console.log(`   PLAIN认证: ${result.supportsPlain ? '✅' : '❌'}`)
    console.log(`   LOGIN认证: ${result.supportsLogin ? '✅' : '❌'}`)
    
    console.log('\n💡 建议配置:')
    if (result.authMethods.length > 0) {
      console.log(`   推荐认证方法: ${result.authMethods[0]}`)
    } else {
      console.log('   尝试 PLAIN 或 LOGIN 认证')
    }
    
    if (result.supportsSSL && port === 993) {
      console.log('   使用 SSL/TLS 连接')
    } else if (result.capabilities.includes('STARTTLS')) {
      console.log('   使用 STARTTLS 连接')
    } else {
      console.log('   使用明文连接（不推荐）')
    }

  } catch (error) {
    console.log(`❌ 检测失败: ${error.message}`)
    
    console.log('\n🔧 故障排除建议:')
    console.log('1. 检查服务器地址和端口是否正确')
    console.log('2. 确认网络连接正常')
    console.log('3. 检查防火墙设置')
    console.log('4. 尝试不同的端口 (993, 143)')
    console.log('5. 尝试不同的加密方式 (SSL, STARTTLS, 明文)')
  }
}

if (require.main === module) {
  main().catch(console.error)
}

module.exports = { testImapCapabilities }
