// 获取网络信息脚本
const os = require('os')

function getNetworkInfo() {
  const networkInterfaces = os.networkInterfaces()
  const networkInfo = {
    hostname: os.hostname(),
    platform: os.platform(),
    interfaces: []
  }

  console.log('🌐 网络配置信息')
  console.log('================')
  console.log(`主机名: ${networkInfo.hostname}`)
  console.log(`系统: ${networkInfo.platform}`)
  console.log('')

  for (const interfaceName in networkInterfaces) {
    const interfaces = networkInterfaces[interfaceName]
    
    for (const iface of interfaces) {
      if (iface.family === 'IPv4') {
        const info = {
          name: interfaceName,
          address: iface.address,
          internal: iface.internal,
          mac: iface.mac
        }
        
        networkInfo.interfaces.push(info)
        
        if (!iface.internal) {
          console.log(`📡 ${interfaceName}:`)
          console.log(`   IP地址: ${iface.address}`)
          console.log(`   MAC地址: ${iface.mac}`)
          console.log(`   局域网访问: http://${iface.address}:3000`)
          console.log('')
        }
      }
    }
  }

  // 生成二维码访问链接（如果有qrcode库）
  try {
    const qrcode = require('qrcode-terminal')
    const mainIP = networkInfo.interfaces.find(iface => !iface.internal)?.address
    if (mainIP) {
      console.log('📱 手机扫码访问:')
      qrcode.generate(`http://${mainIP}:3000`, { small: true })
    }
  } catch (error) {
    // qrcode-terminal 未安装，跳过
  }

  return networkInfo
}

if (require.main === module) {
  getNetworkInfo()
}

module.exports = getNetworkInfo
