{"name": "libmime", "description": "Encode and decode quoted printable and base64 strings", "version": "5.3.7", "main": "lib/libmime.js", "files": ["lib", "CHANGELOG.md"], "homepage": "https://github.com/nodemailer/libmime", "repository": {"type": "git", "url": "git+https://github.com/nodemailer/libmime.git"}, "license": "MIT", "keywords": ["MIME", "Base64", "Quoted-Printable"], "author": "<PERSON><PERSON> <<EMAIL>>", "scripts": {"test": "grunt", "update": "rm -rf node_modules package-lock.json && ncu -u && npm install"}, "dependencies": {"encoding-japanese": "2.2.0", "iconv-lite": "0.6.3", "libbase64": "1.3.0", "libqp": "2.1.1"}, "devDependencies": {"chai": "4.4.1", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "10.1.5", "grunt": "1.6.1", "grunt-cli": "1.5.0", "grunt-eslint": "24.3.0", "grunt-mocha-test": "0.13.3", "mocha": "11.7.1"}}