// 实时邮件服务 - 支持持续监听和实时推送
import { io, Socket } from 'socket.io-client'

export interface RealtimeEmail {
  id: string
  from: string
  to: string
  subject: string
  date: string
  body: string
  snippet: string
  isUnread: boolean
  isStarred: boolean
  accountId: string
  attachments?: Array<{
    filename: string
    size: number
    contentType: string
  }>
}

export interface EmailAccount {
  id: string
  email: string
  password: string
  provider: 'gmail' | 'outlook' | '163' | 'qq' | '126' | 'sina' | 'other'
  name: string
  imapConfig?: {
    host: string
    port: number
    secure: boolean
  }
  smtpConfig?: {
    host: string
    port: number
    secure: boolean
  }
  isConnected: boolean
  lastSync?: string
  unreadCount: number
}

export interface EmailNotification {
  type: 'new_email' | 'email_read' | 'email_deleted' | 'connection_status'
  accountId: string
  email?: RealtimeEmail
  status?: 'connected' | 'disconnected' | 'error'
  message?: string
}

export class RealtimeEmailService {
  private socket: Socket | null = null
  private accounts: Map<string, EmailAccount> = new Map()
  private listeners: Map<string, Function[]> = new Map()
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5

  constructor() {
    this.initializeSocket()
  }

  // 初始化WebSocket连接
  private initializeSocket() {
    try {
      this.socket = io('ws://localhost:3000', {
        transports: ['websocket'],
        autoConnect: false
      })

      this.socket.on('connect', () => {
        console.log('✅ 实时邮件服务已连接')
        this.reconnectAttempts = 0
        this.emit('service_status', { status: 'connected' })
      })

      this.socket.on('disconnect', () => {
        console.log('❌ 实时邮件服务已断开')
        this.emit('service_status', { status: 'disconnected' })
        this.handleReconnect()
      })

      this.socket.on('new_email', (data: RealtimeEmail) => {
        console.log('📧 收到新邮件:', data.subject)
        this.handleNewEmail(data)
      })

      this.socket.on('email_status_change', (data: any) => {
        this.emit('email_status_change', data)
      })

      this.socket.on('account_status', (data: any) => {
        this.updateAccountStatus(data.accountId, data.status)
      })

      this.socket.on('error', (error: any) => {
        console.error('Socket错误:', error)
        this.emit('service_error', { error: error.message })
      })

    } catch (error) {
      console.error('初始化Socket失败:', error)
    }
  }

  // 连接到实时服务
  async connect(): Promise<boolean> {
    try {
      if (!this.socket) {
        this.initializeSocket()
      }
      
      this.socket?.connect()
      return true
    } catch (error) {
      console.error('连接失败:', error)
      return false
    }
  }

  // 断开连接
  disconnect() {
    this.socket?.disconnect()
  }

  // 测试邮箱连接
  async testEmailConnection(email: string, password: string, provider: string): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        resolve({ success: false, error: '服务未连接' })
        return
      }

      const timeout = setTimeout(() => {
        resolve({ success: false, error: '测试连接超时' })
      }, 15000)

      this.socket.emit('test_email_connection', { email, password, provider })

      this.socket.once('test_connection_result', (data: any) => {
        clearTimeout(timeout)
        resolve(data)
      })
    })
  }

  // 添加邮箱账户并开始监听
  async addAccount(account: EmailAccount): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        resolve({ success: false, error: '服务未连接' })
        return
      }

      // 保存账户信息
      this.accounts.set(account.id, { ...account, isConnected: false })

      const timeout = setTimeout(() => {
        resolve({ success: false, error: '添加账户超时' })
      }, 20000)

      this.socket.emit('add_email_account', {
        accountId: account.id,
        email: account.email,
        password: account.password,
        provider: account.provider,
        name: account.name
      })

      this.socket.once('add_account_result', (data: any) => {
        clearTimeout(timeout)
        resolve(data)
      })
    })
  }

  // 移除邮箱账户
  removeAccount(accountId: string) {
    this.accounts.delete(accountId)
    this.socket?.emit('remove_account', { accountId })
  }

  // 获取邮件列表
  async getEmails(accountId: string, limit: number = 50): Promise<RealtimeEmail[]> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        reject(new Error('服务未连接'))
        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('获取邮件超时'))
      }, 10000)

      this.socket.emit('get_emails', { accountId, limit })
      
      this.socket.once('emails_response', (data: any) => {
        clearTimeout(timeout)
        if (data.success) {
          resolve(data.emails)
        } else {
          reject(new Error(data.error))
        }
      })
    })
  }

  // 发送邮件
  async sendEmail(accountId: string, to: string, subject: string, body: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        reject(new Error('服务未连接'))
        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('发送邮件超时'))
      }, 30000)

      this.socket.emit('send_email', {
        accountId,
        to,
        subject,
        body
      })

      this.socket.once('send_email_response', (data: any) => {
        clearTimeout(timeout)
        if (data.success) {
          resolve(true)
        } else {
          reject(new Error(data.error))
        }
      })
    })
  }

  // 标记邮件为已读
  async markAsRead(accountId: string, emailId: string): Promise<boolean> {
    this.socket?.emit('mark_as_read', { accountId, emailId })
    return true
  }

  // 切换星标
  async toggleStar(accountId: string, emailId: string, starred: boolean): Promise<boolean> {
    this.socket?.emit('toggle_star', { accountId, emailId, starred })
    return true
  }

  // 搜索邮件
  async searchEmails(accountId: string, query: string): Promise<RealtimeEmail[]> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        reject(new Error('服务未连接'))
        return
      }

      const timeout = setTimeout(() => {
        reject(new Error('搜索超时'))
      }, 15000)

      this.socket.emit('search_emails', { accountId, query })
      
      this.socket.once('search_response', (data: any) => {
        clearTimeout(timeout)
        if (data.success) {
          resolve(data.emails)
        } else {
          reject(new Error(data.error))
        }
      })
    })
  }

  // 事件监听
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event)?.push(callback)
  }

  // 移除事件监听
  off(event: string, callback: Function) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  // 触发事件
  private emit(event: string, data: any) {
    const callbacks = this.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(data))
    }
  }

  // 处理新邮件
  private handleNewEmail(email: RealtimeEmail) {
    // 更新未读计数
    const account = this.accounts.get(email.accountId)
    if (account) {
      account.unreadCount += 1
      this.accounts.set(email.accountId, account)
    }

    // 显示桌面通知
    this.showDesktopNotification(email)

    // 触发新邮件事件
    this.emit('new_email', email)
  }

  // 显示桌面通知
  private showDesktopNotification(email: RealtimeEmail) {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`新邮件 - ${email.from}`, {
        body: email.subject,
        icon: '/favicon.ico',
        tag: email.id
      })
    }
  }

  // 请求通知权限
  async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }

  // 处理重连
  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      setTimeout(() => {
        this.socket?.connect()
      }, Math.pow(2, this.reconnectAttempts) * 1000) // 指数退避
    } else {
      console.error('重连失败，已达到最大尝试次数')
      this.emit('service_error', { error: '连接失败，请刷新页面重试' })
    }
  }

  // 验证账户
  private async validateAccount(account: EmailAccount): Promise<{ success: boolean; error?: string }> {
    // 基本验证
    if (!account.email || !account.password) {
      return { success: false, error: '邮箱地址和密码不能为空' }
    }

    if (!account.email.includes('@')) {
      return { success: false, error: '邮箱地址格式不正确' }
    }

    return { success: true }
  }

  // 获取IMAP配置
  private getImapConfig(provider: string) {
    const configs = {
      gmail: { host: 'imap.gmail.com', port: 993, secure: true },
      outlook: { host: 'outlook.office365.com', port: 993, secure: true },
      '163': { host: 'imap.163.com', port: 993, secure: true },
      qq: { host: 'imap.qq.com', port: 993, secure: true },
      '126': { host: 'imap.126.com', port: 993, secure: true },
      sina: { host: 'imap.sina.com', port: 993, secure: true }
    }
    return configs[provider as keyof typeof configs] || null
  }

  // 更新账户状态
  private updateAccountStatus(accountId: string, status: string) {
    const account = this.accounts.get(accountId)
    if (account) {
      account.isConnected = status === 'connected'
      account.lastSync = new Date().toISOString()
      this.accounts.set(accountId, account)
      this.emit('account_status_change', { accountId, status })
    }
  }

  // 获取账户状态
  getAccountStatus(accountId: string): EmailAccount | undefined {
    return this.accounts.get(accountId)
  }

  // 获取所有账户
  getAllAccounts(): EmailAccount[] {
    return Array.from(this.accounts.values())
  }
}

export default RealtimeEmailService
