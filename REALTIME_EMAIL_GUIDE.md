# 实时邮箱客户端使用指南

## 🚀 功能特性

### ✨ 实时邮件接收
- **持续在线监听** - 24/7监听您的邮箱，实时接收新邮件
- **即时推送通知** - 新邮件到达时立即显示桌面通知
- **多账户支持** - 同时监听多个邮箱账户
- **WebSocket连接** - 低延迟的实时通信

### 📧 支持的邮箱服务
- **Gmail** - 完整支持IMAP/SMTP
- **Outlook/Hotmail** - Microsoft邮箱服务
- **163邮箱** - 网易邮箱
- **QQ邮箱** - 腾讯邮箱
- **126邮箱** - 网易126邮箱
- **新浪邮箱** - 新浪邮箱服务
- **其他IMAP服务** - 支持标准IMAP/SMTP协议的邮箱

## 🔧 快速开始

### 1. 启动完整服务

```bash
# 同时启动邮件服务器和Web界面
npm run dev:full

# 或者分别启动
npm run email-server  # 启动邮件服务器 (端口3001)
npm run dev           # 启动Web界面 (端口3000)
```

### 2. 添加邮箱账户

1. 点击左侧边栏的"添加邮箱"按钮
2. 选择邮箱服务商
3. 输入邮箱地址和密码
4. 系统会自动测试连接并开始监听

### 3. 邮箱密码设置

#### Gmail用户
- 需要开启"两步验证"
- 生成"应用专用密码"
- 使用应用专用密码而不是账户密码

#### 其他邮箱
- 大多数邮箱需要开启IMAP/SMTP服务
- 可能需要使用"授权码"而不是登录密码

## 📱 界面功能

### 实时状态指示器
- **绿色在线图标** - 实时服务已连接
- **红色离线图标** - 服务断开连接
- **通知铃铛** - 桌面通知开启状态

### 邮件操作
- **实时接收** - 新邮件自动出现在列表顶部
- **即时通知** - 桌面弹窗通知新邮件
- **标记操作** - 已读、星标等状态同步
- **发送邮件** - 通过SMTP发送邮件

## 🔐 安全说明

### 密码安全
- 所有密码仅在本地存储
- 不会上传到任何服务器
- 建议使用应用专用密码

### 连接安全
- 使用SSL/TLS加密连接
- 支持安全的IMAP/SMTP协议
- WebSocket连接仅限本地

## ⚙️ 配置说明

### 邮箱服务器配置

系统已预配置主流邮箱服务商的服务器设置：

```javascript
// Gmail
IMAP: imap.gmail.com:993 (SSL)
SMTP: smtp.gmail.com:587 (STARTTLS)

// Outlook
IMAP: outlook.office365.com:993 (SSL)
SMTP: smtp-mail.outlook.com:587 (STARTTLS)

// 163邮箱
IMAP: imap.163.com:993 (SSL)
SMTP: smtp.163.com:465 (SSL)

// QQ邮箱
IMAP: imap.qq.com:993 (SSL)
SMTP: smtp.qq.com:587 (STARTTLS)
```

### 自定义配置
如需添加其他邮箱服务，可在 `src/lib/realtime-email-service.ts` 中添加配置。

## 🛠️ 故障排除

### 连接问题
1. **检查网络连接** - 确保能访问邮箱服务器
2. **验证账户信息** - 确认邮箱地址和密码正确
3. **检查服务器状态** - 确认邮件服务器(端口3001)正在运行

### 认证失败
1. **Gmail用户** - 确保使用应用专用密码
2. **其他邮箱** - 检查是否开启了IMAP/SMTP服务
3. **两步验证** - 某些邮箱需要特殊的授权码

### 通知问题
1. **浏览器权限** - 确保允许网站发送通知
2. **系统设置** - 检查操作系统的通知设置
3. **浏览器支持** - 确保浏览器支持Web通知API

## 📊 性能优化

### 邮件缓存
- 系统会缓存已加载的邮件
- 减少重复的服务器请求
- 提高界面响应速度

### 连接管理
- 自动重连机制
- 指数退避重试策略
- 连接状态实时监控

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 实时邮件接收
- ✅ 多账户支持
- ✅ 桌面通知
- ✅ WebSocket通信
- ✅ IMAP/SMTP支持

### 计划功能
- 📋 邮件分类和标签
- 📎 附件下载和预览
- 🔍 高级搜索功能
- 📱 移动端适配
- 🌙 暗色主题

## 💡 使用技巧

1. **保持连接** - 不要关闭浏览器标签页以保持实时接收
2. **通知权限** - 首次使用时允许通知权限
3. **多账户** - 可以同时监听多个邮箱账户
4. **安全密码** - 使用应用专用密码提高安全性

## 🆘 获取帮助

如果遇到问题：
1. 检查浏览器控制台的错误信息
2. 确认邮件服务器正在运行
3. 验证邮箱账户设置
4. 查看网络连接状态

---

**享受您的实时邮箱体验！** 📧✨
