'use client'

import { useState, useEffect } from 'react'
import { Check, X, AlertCircle, ExternalLink, Copy, Eye, EyeOff } from 'lucide-react'

export default function SetupPage() {
  const [config, setConfig] = useState({
    clientId: '',
    clientSecret: '',
    redirectUri: '',
    appUrl: ''
  })
  const [showSecret, setShowSecret] = useState(false)
  const [copied, setCopied] = useState('')

  useEffect(() => {
    // 从环境变量加载配置
    setConfig({
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectUri: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || '',
      appUrl: process.env.NEXT_PUBLIC_APP_URL || ''
    })
  }, [])

  const copyToClipboard = (text: string, key: string) => {
    navigator.clipboard.writeText(text)
    setCopied(key)
    setTimeout(() => setCopied(''), 2000)
  }

  const isConfigured = (value: string) => {
    return value && !value.includes('请替换') && !value.includes('your_')
  }

  const allConfigured = Object.values(config).every(isConfigured)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Gmail OAuth 设置助手
            </h1>
            <p className="text-gray-600">
              按照以下步骤配置Google OAuth凭据，启用Gmail功能
            </p>
          </div>

          {/* 配置状态 */}
          <div className="mb-8 p-4 rounded-lg border-2 border-dashed border-gray-300">
            <div className="flex items-center gap-2 mb-4">
              {allConfigured ? (
                <Check className="w-6 h-6 text-green-600" />
              ) : (
                <AlertCircle className="w-6 h-6 text-orange-500" />
              )}
              <h2 className="text-xl font-semibold">
                配置状态: {allConfigured ? '已完成' : '需要配置'}
              </h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[
                { key: 'clientId', label: '客户端ID', value: config.clientId },
                { key: 'clientSecret', label: '客户端密钥', value: config.clientSecret },
                { key: 'redirectUri', label: '重定向URI', value: config.redirectUri },
                { key: 'appUrl', label: '应用URL', value: config.appUrl }
              ].map(({ key, label, value }) => (
                <div key={key} className="flex items-center gap-2">
                  {isConfigured(value) ? (
                    <Check className="w-4 h-4 text-green-600" />
                  ) : (
                    <X className="w-4 h-4 text-red-500" />
                  )}
                  <span className="text-sm font-medium">{label}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 设置步骤 */}
          <div className="space-y-6">
            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <span className="bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm">1</span>
                创建Google Cloud项目
              </h3>
              <p className="text-gray-600 mb-4">
                访问Google Cloud Console创建新项目并启用Gmail API
              </p>
              <a
                href="https://console.cloud.google.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <ExternalLink className="w-4 h-4" />
                打开Google Cloud Console
              </a>
            </div>

            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <span className="bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm">2</span>
                配置重定向URI
              </h3>
              <p className="text-gray-600 mb-4">
                在OAuth2客户端配置中添加以下重定向URI：
              </p>
              <div className="bg-gray-100 p-3 rounded-lg flex items-center justify-between">
                <code className="text-sm">{config.redirectUri}</code>
                <button
                  onClick={() => copyToClipboard(config.redirectUri, 'redirectUri')}
                  className="p-2 hover:bg-gray-200 rounded"
                  title="复制到剪贴板"
                >
                  {copied === 'redirectUri' ? (
                    <Check className="w-4 h-4 text-green-600" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>

            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <span className="bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm">3</span>
                更新环境变量
              </h3>
              <p className="text-gray-600 mb-4">
                将获取的凭据添加到 <code className="bg-gray-100 px-2 py-1 rounded">.env.local</code> 文件中：
              </p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    客户端ID
                  </label>
                  <div className="bg-gray-100 p-3 rounded-lg">
                    <code className="text-sm break-all">
                      NEXT_PUBLIC_GOOGLE_CLIENT_ID={config.clientId || '你的客户端ID.apps.googleusercontent.com'}
                    </code>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    客户端密钥
                  </label>
                  <div className="bg-gray-100 p-3 rounded-lg flex items-center justify-between">
                    <code className="text-sm break-all">
                      GOOGLE_CLIENT_SECRET={showSecret ? config.clientSecret || '你的客户端密钥' : '••••••••••••••••'}
                    </code>
                    <button
                      onClick={() => setShowSecret(!showSecret)}
                      className="p-2 hover:bg-gray-200 rounded"
                    >
                      {showSecret ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <span className="bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm">4</span>
                重启开发服务器
              </h3>
              <p className="text-gray-600 mb-4">
                更新环境变量后，需要重启开发服务器：
              </p>
              <div className="bg-gray-900 text-green-400 p-3 rounded-lg">
                <code className="text-sm">
                  # 停止当前服务器 (Ctrl+C)<br/>
                  # 然后重新启动<br/>
                  npm run dev
                </code>
              </div>
            </div>

            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <span className="bg-green-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm">5</span>
                测试配置
              </h3>
              <p className="text-gray-600 mb-4">
                配置完成后，返回主页面测试Gmail授权功能
              </p>
              <div className="flex gap-4">
                <a
                  href="/"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  返回主页面
                </a>
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  刷新配置状态
                </button>
              </div>
            </div>
          </div>

          {/* 帮助信息 */}
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">需要帮助？</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 确保在Google Cloud Console中启用了Gmail API</li>
              <li>• 检查OAuth同意屏幕是否正确配置</li>
              <li>• 确保重定向URI完全匹配（包括协议和端口）</li>
              <li>• 在测试模式下，需要将用户添加到测试用户列表</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
