{"name": "base32.js", "version": "0.1.0", "author": "<PERSON>-<PERSON> <<EMAIL>>", "description": "Base 32 encodings for JavaScript", "keywords": ["base32", "base32hex", "crockford", "rfc2938", "rfc4648", "encoding", "decoding"], "license": "MIT", "main": "index.js", "browser": "base32.js", "engines": {"node": ">=0.12.0"}, "repository": {"type": "git", "url": "git://github.com/mikepb/base32.js.git"}, "scripts": {"test": "mocha --reporter dot", "karma": "karma start --single-run", "dist": "webpack base32.js dist/base32.js && webpack --optimize-minimize base32.js dist/base32.min.js", "doc": "jsdoc -c jsdoc.json"}, "dependencies": {}, "devDependencies": {"jsdoc": "*", "karma": "*", "karma-chrome-launcher": "*", "karma-mocha": "*", "karma-webpack": "*", "mocha": "*", "webpack": "*"}}