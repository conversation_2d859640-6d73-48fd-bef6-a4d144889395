"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gcp-metadata";
exports.ids = ["vendor-chunks/gcp-metadata"];
exports.modules = {

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js":
/*!**************************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/gcp-residency.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */ Object.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GCE_LINUX_BIOS_PATHS = void 0;\nexports.isGoogleCloudServerless = isGoogleCloudServerless;\nexports.isGoogleComputeEngineLinux = isGoogleComputeEngineLinux;\nexports.isGoogleComputeEngineMACAddress = isGoogleComputeEngineMACAddress;\nexports.isGoogleComputeEngine = isGoogleComputeEngine;\nexports.detectGCPResidency = detectGCPResidency;\nconst fs_1 = __webpack_require__(/*! fs */ \"fs\");\nconst os_1 = __webpack_require__(/*! os */ \"os\");\n/**\n * Known paths unique to Google Compute Engine Linux instances\n */ exports.GCE_LINUX_BIOS_PATHS = {\n    BIOS_DATE: \"/sys/class/dmi/id/bios_date\",\n    BIOS_VENDOR: \"/sys/class/dmi/id/bios_vendor\"\n};\nconst GCE_MAC_ADDRESS_REGEX = /^42:01/;\n/**\n * Determines if the process is running on a Google Cloud Serverless environment (Cloud Run or Cloud Functions instance).\n *\n * Uses the:\n * - {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n * - {@link https://cloud.google.com/functions/docs/env-var Cloud Functions environment variables}.\n *\n * @returns {boolean} `true` if the process is running on GCP serverless, `false` otherwise.\n */ function isGoogleCloudServerless() {\n    /**\n     * `CLOUD_RUN_JOB` is used for Cloud Run Jobs\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     *\n     * `FUNCTION_NAME` is used in older Cloud Functions environments:\n     * - See {@link https://cloud.google.com/functions/docs/env-var Python 3.7 and Go 1.11}.\n     *\n     * `K_SERVICE` is used in Cloud Run and newer Cloud Functions environments:\n     * - See {@link https://cloud.google.com/run/docs/container-contract#env-vars Cloud Run environment variables}.\n     * - See {@link https://cloud.google.com/functions/docs/env-var Cloud Functions newer runtimes}.\n     */ const isGFEnvironment = process.env.CLOUD_RUN_JOB || process.env.FUNCTION_NAME || process.env.K_SERVICE;\n    return !!isGFEnvironment;\n}\n/**\n * Determines if the process is running on a Linux Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on Linux GCE, `false` otherwise.\n */ function isGoogleComputeEngineLinux() {\n    if ((0, os_1.platform)() !== \"linux\") return false;\n    try {\n        // ensure this file exist\n        (0, fs_1.statSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_DATE);\n        // ensure this file exist and matches\n        const biosVendor = (0, fs_1.readFileSync)(exports.GCE_LINUX_BIOS_PATHS.BIOS_VENDOR, \"utf8\");\n        return /Google/.test(biosVendor);\n    } catch  {\n        return false;\n    }\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance with a known\n * MAC address.\n *\n * @returns {boolean} `true` if the process is running on GCE (as determined by MAC address), `false` otherwise.\n */ function isGoogleComputeEngineMACAddress() {\n    const interfaces = (0, os_1.networkInterfaces)();\n    for (const item of Object.values(interfaces)){\n        if (!item) continue;\n        for (const { mac } of item){\n            if (GCE_MAC_ADDRESS_REGEX.test(mac)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n/**\n * Determines if the process is running on a Google Compute Engine instance.\n *\n * @returns {boolean} `true` if the process is running on GCE, `false` otherwise.\n */ function isGoogleComputeEngine() {\n    return isGoogleComputeEngineLinux() || isGoogleComputeEngineMACAddress();\n}\n/**\n * Determines if the process is running on Google Cloud Platform.\n *\n * @returns {boolean} `true` if the process is running on GCP, `false` otherwise.\n */ function detectGCPResidency() {\n    return isGoogleCloudServerless() || isGoogleComputeEngine();\n} //# sourceMappingURL=gcp-residency.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gcp-metadata/build/src/index.js":
/*!******************************************************!*\
  !*** ./node_modules/gcp-metadata/build/src/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */ var __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function() {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function(o) {\n            var ar = [];\n            for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function(mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) {\n            for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        }\n        __setModuleDefault(result, mod);\n        return result;\n    };\n}();\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.gcpResidencyCache = exports.METADATA_SERVER_DETECTION = exports.HEADERS = exports.HEADER_VALUE = exports.HEADER_NAME = exports.SECONDARY_HOST_ADDRESS = exports.HOST_ADDRESS = exports.BASE_PATH = void 0;\nexports.instance = instance;\nexports.project = project;\nexports.universe = universe;\nexports.bulk = bulk;\nexports.isAvailable = isAvailable;\nexports.resetIsAvailableCache = resetIsAvailableCache;\nexports.getGCPResidency = getGCPResidency;\nexports.setGCPResidency = setGCPResidency;\nexports.requestTimeout = requestTimeout;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nconst jsonBigint = __webpack_require__(/*! json-bigint */ \"(rsc)/./node_modules/json-bigint/index.js\");\nconst gcp_residency_1 = __webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\");\nconst logger = __importStar(__webpack_require__(/*! google-logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/index.js\"));\nexports.BASE_PATH = \"/computeMetadata/v1\";\nexports.HOST_ADDRESS = \"http://***************\";\nexports.SECONDARY_HOST_ADDRESS = \"http://metadata.google.internal.\";\nexports.HEADER_NAME = \"Metadata-Flavor\";\nexports.HEADER_VALUE = \"Google\";\nexports.HEADERS = Object.freeze({\n    [exports.HEADER_NAME]: exports.HEADER_VALUE\n});\nconst log = logger.log(\"gcp-metadata\");\n/**\n * Metadata server detection override options.\n *\n * Available via `process.env.METADATA_SERVER_DETECTION`.\n */ exports.METADATA_SERVER_DETECTION = Object.freeze({\n    \"assume-present\": \"don't try to ping the metadata server, but assume it's present\",\n    none: \"don't try to ping the metadata server, but don't try to use it either\",\n    \"bios-only\": \"treat the result of a BIOS probe as canonical (don't fall back to pinging)\",\n    \"ping-only\": \"skip the BIOS probe, and go straight to pinging\"\n});\n/**\n * Returns the base URL while taking into account the GCE_METADATA_HOST\n * environment variable if it exists.\n *\n * @returns The base URL, e.g., http://***************/computeMetadata/v1.\n */ function getBaseUrl(baseUrl) {\n    if (!baseUrl) {\n        baseUrl = process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST || exports.HOST_ADDRESS;\n    }\n    // If no scheme is provided default to HTTP:\n    if (!/^https?:\\/\\//.test(baseUrl)) {\n        baseUrl = `http://${baseUrl}`;\n    }\n    return new URL(exports.BASE_PATH, baseUrl).href;\n}\n// Accepts an options object passed from the user to the API. In previous\n// versions of the API, it referred to a `Request` or an `Axios` request\n// options object.  Now it refers to an object with very limited property\n// names. This is here to help ensure users don't pass invalid options when\n// they  upgrade from 0.4 to 0.5 to 0.8.\nfunction validate(options) {\n    Object.keys(options).forEach((key)=>{\n        switch(key){\n            case \"params\":\n            case \"property\":\n            case \"headers\":\n                break;\n            case \"qs\":\n                throw new Error(\"'qs' is not a valid configuration option. Please use 'params' instead.\");\n            default:\n                throw new Error(`'${key}' is not a valid configuration option.`);\n        }\n    });\n}\nasync function metadataAccessor(type, options = {}, noResponseRetries = 3, fastFail = false) {\n    const headers = new Headers(exports.HEADERS);\n    let metadataKey = \"\";\n    let params = {};\n    if (typeof type === \"object\") {\n        const metadataAccessor = type;\n        new Headers(metadataAccessor.headers).forEach((value, key)=>headers.set(key, value));\n        metadataKey = metadataAccessor.metadataKey;\n        params = metadataAccessor.params || params;\n        noResponseRetries = metadataAccessor.noResponseRetries || noResponseRetries;\n        fastFail = metadataAccessor.fastFail || fastFail;\n    } else {\n        metadataKey = type;\n    }\n    if (typeof options === \"string\") {\n        metadataKey += `/${options}`;\n    } else {\n        validate(options);\n        if (options.property) {\n            metadataKey += `/${options.property}`;\n        }\n        new Headers(options.headers).forEach((value, key)=>headers.set(key, value));\n        params = options.params || params;\n    }\n    const requestMethod = fastFail ? fastFailMetadataRequest : gaxios_1.request;\n    const req = {\n        url: `${getBaseUrl()}/${metadataKey}`,\n        headers,\n        retryConfig: {\n            noResponseRetries\n        },\n        params,\n        responseType: \"text\",\n        timeout: requestTimeout()\n    };\n    log.info(\"instance request %j\", req);\n    const res = await requestMethod(req);\n    log.info(\"instance metadata is %s\", res.data);\n    const metadataFlavor = res.headers.get(exports.HEADER_NAME);\n    if (metadataFlavor !== exports.HEADER_VALUE) {\n        throw new RangeError(`Invalid response from metadata service: incorrect ${exports.HEADER_NAME} header. Expected '${exports.HEADER_VALUE}', got ${metadataFlavor ? `'${metadataFlavor}'` : \"no header\"}`);\n    }\n    if (typeof res.data === \"string\") {\n        try {\n            return jsonBigint.parse(res.data);\n        } catch  {\n        /* ignore */ }\n    }\n    return res.data;\n}\nasync function fastFailMetadataRequest(options) {\n    const secondaryOptions = {\n        ...options,\n        url: options.url?.toString().replace(getBaseUrl(), getBaseUrl(exports.SECONDARY_HOST_ADDRESS))\n    };\n    // We race a connection between DNS/IP to metadata server. There are a couple\n    // reasons for this:\n    //\n    // 1. the DNS is slow in some GCP environments; by checking both, we might\n    //    detect the runtime environment significantly faster.\n    // 2. we can't just check the IP, which is tarpitted and slow to respond\n    //    on a user's local machine.\n    //\n    // Returns first resolved promise or if all promises get rejected we return an AggregateError.\n    //\n    // Note, however, if a failure happens prior to a success, a rejection should\n    // occur, this is for folks running locally.\n    //\n    const r1 = (0, gaxios_1.request)(options);\n    const r2 = (0, gaxios_1.request)(secondaryOptions);\n    return Promise.any([\n        r1,\n        r2\n    ]);\n}\n/**\n * Obtain metadata for the current GCE instance.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const serviceAccount: {} = await instance('service-accounts/');\n * const serviceAccountEmail: string = await instance('service-accounts/default/email');\n * ```\n */ // eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction instance(options) {\n    return metadataAccessor(\"instance\", options);\n}\n/**\n * Obtain metadata for the current GCP project.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const projectId: string = await project('project-id');\n * const numericProjectId: number = await project('numeric-project-id');\n * ```\n */ // eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction project(options) {\n    return metadataAccessor(\"project\", options);\n}\n/**\n * Obtain metadata for the current universe.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const universeDomain: string = await universe('universe-domain');\n * ```\n */ function universe(options) {\n    return metadataAccessor(\"universe\", options);\n}\n/**\n * Retrieve metadata items in parallel.\n *\n * @see {@link https://cloud.google.com/compute/docs/metadata/predefined-metadata-keys}\n *\n * @example\n * ```\n * const data = await bulk([\n *   {\n *     metadataKey: 'instance',\n *   },\n *   {\n *     metadataKey: 'project/project-id',\n *   },\n * ] as const);\n *\n * // data.instance;\n * // data['project/project-id'];\n * ```\n *\n * @param properties The metadata properties to retrieve\n * @returns The metadata in `metadatakey:value` format\n */ async function bulk(properties) {\n    const r = {};\n    await Promise.all(properties.map((item)=>{\n        return (async ()=>{\n            const res = await metadataAccessor(item);\n            const key = item.metadataKey;\n            r[key] = res;\n        })();\n    }));\n    return r;\n}\n/*\n * How many times should we retry detecting GCP environment.\n */ function detectGCPAvailableRetries() {\n    return process.env.DETECT_GCP_RETRIES ? Number(process.env.DETECT_GCP_RETRIES) : 0;\n}\nlet cachedIsAvailableResponse;\n/**\n * Determine if the metadata server is currently available.\n */ async function isAvailable() {\n    if (process.env.METADATA_SERVER_DETECTION) {\n        const value = process.env.METADATA_SERVER_DETECTION.trim().toLocaleLowerCase();\n        if (!(value in exports.METADATA_SERVER_DETECTION)) {\n            throw new RangeError(`Unknown \\`METADATA_SERVER_DETECTION\\` env variable. Got \\`${value}\\`, but it should be \\`${Object.keys(exports.METADATA_SERVER_DETECTION).join(\"`, `\")}\\`, or unset`);\n        }\n        switch(value){\n            case \"assume-present\":\n                return true;\n            case \"none\":\n                return false;\n            case \"bios-only\":\n                return getGCPResidency();\n            case \"ping-only\":\n        }\n    }\n    try {\n        // If a user is instantiating several GCP libraries at the same time,\n        // this may result in multiple calls to isAvailable(), to detect the\n        // runtime environment. We use the same promise for each of these calls\n        // to reduce the network load.\n        if (cachedIsAvailableResponse === undefined) {\n            cachedIsAvailableResponse = metadataAccessor(\"instance\", undefined, detectGCPAvailableRetries(), // If the default HOST_ADDRESS has been overridden, we should not\n            // make an effort to try SECONDARY_HOST_ADDRESS (as we are likely in\n            // a non-GCP environment):\n            !(process.env.GCE_METADATA_IP || process.env.GCE_METADATA_HOST));\n        }\n        await cachedIsAvailableResponse;\n        return true;\n    } catch (e) {\n        const err = e;\n        if (process.env.DEBUG_AUTH) {\n            console.info(err);\n        }\n        if (err.type === \"request-timeout\") {\n            // If running in a GCP environment, metadata endpoint should return\n            // within ms.\n            return false;\n        }\n        if (err.response && err.response.status === 404) {\n            return false;\n        } else {\n            if (!(err.response && err.response.status === 404) && // A warning is emitted if we see an unexpected err.code, or err.code\n            // is not populated:\n            (!err.code || ![\n                \"EHOSTDOWN\",\n                \"EHOSTUNREACH\",\n                \"ENETUNREACH\",\n                \"ENOENT\",\n                \"ENOTFOUND\",\n                \"ECONNREFUSED\"\n            ].includes(err.code.toString()))) {\n                let code = \"UNKNOWN\";\n                if (err.code) code = err.code.toString();\n                process.emitWarning(`received unexpected error = ${err.message} code = ${code}`, \"MetadataLookupWarning\");\n            }\n            // Failure to resolve the metadata service means that it is not available.\n            return false;\n        }\n    }\n}\n/**\n * reset the memoized isAvailable() lookup.\n */ function resetIsAvailableCache() {\n    cachedIsAvailableResponse = undefined;\n}\n/**\n * A cache for the detected GCP Residency.\n */ exports.gcpResidencyCache = null;\n/**\n * Detects GCP Residency.\n * Caches results to reduce costs for subsequent calls.\n *\n * @see setGCPResidency for setting\n */ function getGCPResidency() {\n    if (exports.gcpResidencyCache === null) {\n        setGCPResidency();\n    }\n    return exports.gcpResidencyCache;\n}\n/**\n * Sets the detected GCP Residency.\n * Useful for forcing metadata server detection behavior.\n *\n * Set `null` to autodetect the environment (default behavior).\n * @see getGCPResidency for getting\n */ function setGCPResidency(value = null) {\n    exports.gcpResidencyCache = value !== null ? value : (0, gcp_residency_1.detectGCPResidency)();\n}\n/**\n * Obtain the timeout for requests to the metadata server.\n *\n * In certain environments and conditions requests can take longer than\n * the default timeout to complete. This function will determine the\n * appropriate timeout based on the environment.\n *\n * @returns {number} a request timeout duration in milliseconds.\n */ function requestTimeout() {\n    return getGCPResidency() ? 0 : 3000;\n}\n__exportStar(__webpack_require__(/*! ./gcp-residency */ \"(rsc)/./node_modules/gcp-metadata/build/src/gcp-residency.js\"), exports); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gcp-metadata/build/src/index.js\n");

/***/ })

};
;