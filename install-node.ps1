# PowerShell脚本：安装Node.js
# 需要管理员权限运行

Write-Host "正在下载Node.js LTS版本..." -ForegroundColor Green

# Node.js LTS下载链接
$nodeUrl = "https://nodejs.org/dist/v20.11.0/node-v20.11.0-x64.msi"
$outputPath = "$env:TEMP\nodejs-installer.msi"

try {
    # 下载Node.js安装包
    Write-Host "下载中..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $nodeUrl -OutFile $outputPath
    
    Write-Host "下载完成！开始安装..." -ForegroundColor Green
    
    # 静默安装Node.js
    Start-Process msiexec.exe -Wait -ArgumentList "/i $outputPath /quiet /norestart"
    
    Write-Host "安装完成！" -ForegroundColor Green
    Write-Host "请重新打开命令行窗口，然后运行 check-node.bat 验证安装" -ForegroundColor Cyan
    
    # 清理临时文件
    Remove-Item $outputPath -Force
    
} catch {
    Write-Host "安装失败：$($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动访问 https://nodejs.org 下载安装" -ForegroundColor Yellow
}

Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
