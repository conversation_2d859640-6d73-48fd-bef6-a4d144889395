"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/data-uri-to-buffer";
exports.ids = ["vendor-chunks/data-uri-to-buffer"];
exports.modules = {

/***/ "(rsc)/./node_modules/data-uri-to-buffer/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/data-uri-to-buffer/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dataUriToBuffer: () => (/* binding */ dataUriToBuffer),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/**\n * Returns a `Buffer` instance from the given data URI `uri`.\n *\n * @param {String} uri Data URI to turn into a Buffer instance\n * @returns {Buffer} Buffer instance from Data URI\n * @api public\n */ function dataUriToBuffer(uri) {\n    if (!/^data:/i.test(uri)) {\n        throw new TypeError('`uri` does not appear to be a Data URI (must begin with \"data:\")');\n    }\n    // strip newlines\n    uri = uri.replace(/\\r?\\n/g, \"\");\n    // split the URI up into the \"metadata\" and the \"data\" portions\n    const firstComma = uri.indexOf(\",\");\n    if (firstComma === -1 || firstComma <= 4) {\n        throw new TypeError(\"malformed data: URI\");\n    }\n    // remove the \"data:\" scheme and parse the metadata\n    const meta = uri.substring(5, firstComma).split(\";\");\n    let charset = \"\";\n    let base64 = false;\n    const type = meta[0] || \"text/plain\";\n    let typeFull = type;\n    for(let i = 1; i < meta.length; i++){\n        if (meta[i] === \"base64\") {\n            base64 = true;\n        } else if (meta[i]) {\n            typeFull += `;${meta[i]}`;\n            if (meta[i].indexOf(\"charset=\") === 0) {\n                charset = meta[i].substring(8);\n            }\n        }\n    }\n    // defaults to US-ASCII only if type is not provided\n    if (!meta[0] && !charset.length) {\n        typeFull += \";charset=US-ASCII\";\n        charset = \"US-ASCII\";\n    }\n    // get the encoded data portion and decode URI-encoded chars\n    const encoding = base64 ? \"base64\" : \"ascii\";\n    const data = unescape(uri.substring(firstComma + 1));\n    const buffer = Buffer.from(data, encoding);\n    // set `.type` and `.typeFull` properties to MIME type\n    buffer.type = type;\n    buffer.typeFull = typeFull;\n    // set the `.charset` property\n    buffer.charset = charset;\n    return buffer;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dataUriToBuffer); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/data-uri-to-buffer/dist/index.js\n");

/***/ })

};
;