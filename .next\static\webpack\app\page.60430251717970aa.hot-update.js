"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gmail-client */ \"(app-pages-browser)/./src/lib/gmail-client.ts\");\n/* harmony import */ var _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/realtime-email-service */ \"(app-pages-browser)/./src/lib/realtime-email-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    var _currentEmails_find, _currentEmails_find1, _currentEmails_find2;\n    _s();\n    var _s1 = $RefreshSig$();\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    const [gmailClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"]());\n    const [isLoadingEmails, setIsLoadingEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__[\"default\"]());\n    const [isServiceConnected, setIsServiceConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeEmails, setRealtimeEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notificationsEnabled, setNotificationsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从localStorage加载账户\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAccounts = ()=>{\n            try {\n                const saved = localStorage.getItem(\"emailAccounts\");\n                if (saved) {\n                    const accounts = JSON.parse(saved);\n                    setEmailAccounts(accounts);\n                    if (accounts.length > 0 && !activeAccountId) {\n                        setActiveAccountId(accounts[0].id);\n                    }\n                } else {\n                    // 默认示例账户\n                    const defaultAccounts = [\n                        {\n                            id: \"1\",\n                            email: \"<EMAIL>\",\n                            provider: \"gmail\",\n                            name: \"个人Gmail\",\n                            isActive: true,\n                            unreadCount: 3\n                        }\n                    ];\n                    setEmailAccounts(defaultAccounts);\n                    setActiveAccountId(\"1\");\n                }\n            } catch (error) {\n                console.error(\"Error loading accounts:\", error);\n            }\n        };\n        loadAccounts();\n    }, []);\n    // 初始化实时邮件服务\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initRealtimeService = async ()=>{\n            try {\n                // 连接到实时服务\n                const connected = await realtimeService.connect();\n                setIsServiceConnected(connected);\n                // 设置事件监听\n                realtimeService.on(\"service_status\", (data)=>{\n                    setIsServiceConnected(data.status === \"connected\");\n                });\n                realtimeService.on(\"new_email\", (email)=>{\n                    console.log(\"收到新邮件:\", email.subject);\n                    setRealtimeEmails((prev)=>[\n                            email,\n                            ...prev\n                        ]);\n                    // 更新未读计数\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === email.accountId ? {\n                                ...acc,\n                                unreadCount: acc.unreadCount + 1\n                            } : acc));\n                });\n                realtimeService.on(\"account_status_change\", (data)=>{\n                    console.log(\"账户状态变化:\", data);\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === data.accountId ? {\n                                ...acc,\n                                isConnected: data.status === \"connected\"\n                            } : acc));\n                });\n                realtimeService.on(\"service_error\", (data)=>{\n                    console.error(\"服务错误:\", data.error);\n                    setIsServiceConnected(false);\n                });\n                // 请求通知权限\n                const notificationGranted = await realtimeService.requestNotificationPermission();\n                setNotificationsEnabled(notificationGranted);\n            } catch (error) {\n                console.error(\"初始化实时服务失败:\", error);\n                setIsServiceConnected(false);\n            }\n        };\n        initRealtimeService();\n        // 清理函数\n        return ()=>{\n            realtimeService.disconnect();\n        };\n    }, []);\n    // 保存账户到localStorage\n    const saveAccounts = (accounts)=>{\n        try {\n            localStorage.setItem(\"emailAccounts\", JSON.stringify(accounts));\n            setEmailAccounts(accounts);\n        } catch (error) {\n            console.error(\"Error saving accounts:\", error);\n        }\n    };\n    // 模拟邮件数据\n    const emails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 获取当前账户的邮件\n    const currentEmails = emails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n        setShowAccountMenu(null) // 关闭菜单\n        ;\n    };\n    // 删除邮箱账户\n    const deleteAccount = (accountId)=>{\n        if (emailAccounts.length <= 1) {\n            alert(\"至少需要保留一个邮箱账户\");\n            return;\n        }\n        if (confirm(\"确定要删除这个邮箱账户吗？\")) {\n            const updatedAccounts = emailAccounts.filter((acc)=>acc.id !== accountId);\n            saveAccounts(updatedAccounts);\n            // 如果删除的是当前活跃账户，切换到第一个可用账户\n            if (accountId === activeAccountId) {\n                if (updatedAccounts.length > 0) {\n                    setActiveAccountId(updatedAccounts[0].id);\n                }\n            }\n            setShowAccountMenu(null);\n            setSelectedEmail(null);\n        }\n    };\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showAccountMenu) {\n                setShowAccountMenu(null);\n            }\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        return ()=>document.removeEventListener(\"click\", handleClickOutside);\n    }, [\n        showAccountMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-800\",\n                                        children: \"邮箱客户端\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative p-2 rounded text-sm transition-colors group \".concat(account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>switchAccount(account.id),\n                                                    className: \"flex items-center justify-between cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getProviderIcon(account.provider)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 289,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: account.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: account.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                                    children: account.unreadCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowAccountMenu(showAccountMenu === account.id ? null : account.id);\n                                                                    },\n                                                                    className: \"p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showAccountMenu === account.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // TODO: 实现编辑功能\n                                                                setShowAccountMenu(null);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"编辑\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteAccount(account.id),\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, account.id, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name) || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.unreadCount) && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: \"p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 \".concat(selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\", \" \").concat(email.unread ? \"bg-white\" : \"bg-gray-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"),\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm mb-1 \".concat(email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"),\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: (_currentEmails_find = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find === void 0 ? void 0 : _currentEmails_find.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: (_currentEmails_find1 = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find1 === void 0 ? void 0 : _currentEmails_find1.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: (_currentEmails_find2 = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find2 === void 0 ? void 0 : _currentEmails_find2.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"来自: \",\n                                                            activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name,\n                                                            \" (\",\n                                                            activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: [\n                                    \"当前账户: \",\n                                    activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 496,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"发件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" (\",\n                                                        account.email,\n                                                        \")\"\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 530,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 9\n            }, this),\n            showAddAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddAccountModal, {}, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 576,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, this);\n    // 添加邮箱账户弹窗组件\n    function AddAccountModal() {\n        _s1();\n        const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n            name: \"\",\n            email: \"\",\n            provider: \"gmail\",\n            password: \"\"\n        });\n        const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n        // Gmail OAuth认证\n        const handleGmailAuth = async ()=>{\n            try {\n                setIsSubmitting(true);\n                setError(\"\");\n                // 启动OAuth流程\n                gmailClient.startOAuthFlow();\n                // 关闭弹窗，OAuth完成后会自动刷新页面\n                setShowAddAccount(false);\n            } catch (error) {\n                console.error(\"Gmail auth error:\", error);\n                setError(\"Gmail授权失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        const handleSubmit = async (e)=>{\n            e.preventDefault();\n            // 如果是Gmail，使用OAuth\n            if (formData.provider === \"gmail\") {\n                handleGmailAuth();\n                return;\n            }\n            setIsSubmitting(true);\n            setError(\"\");\n            // 验证表单\n            if (!formData.name || !formData.email || !formData.password) {\n                setError(\"请填写所有必填字段\");\n                setIsSubmitting(false);\n                return;\n            }\n            // 检查邮箱是否已存在\n            if (emailAccounts.some((acc)=>acc.email === formData.email)) {\n                setError(\"该邮箱账户已存在\");\n                setIsSubmitting(false);\n                return;\n            }\n            try {\n                // 模拟添加账户的过程\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                const newAccount = {\n                    id: Date.now().toString(),\n                    email: formData.email,\n                    provider: formData.provider,\n                    name: formData.name,\n                    isActive: false,\n                    unreadCount: 0\n                };\n                const updatedAccounts = [\n                    ...emailAccounts,\n                    newAccount\n                ];\n                saveAccounts(updatedAccounts);\n                setShowAddAccount(false);\n                // 重置表单\n                setFormData({\n                    name: \"\",\n                    email: \"\",\n                    provider: \"gmail\",\n                    password: \"\"\n                });\n            } catch (err) {\n                setError(\"添加账户失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg w-full max-w-md mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"添加邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddAccount(false),\n                                className: \"text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 670,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"账户名称 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"例如：个人邮箱、工作邮箱\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"邮箱服务商 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 704,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: formData.provider,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    provider: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"gmail\",\n                                                children: \"Gmail (Google)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 711,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"outlook\",\n                                                children: \"Outlook (Microsoft)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"163\",\n                                                children: \"163邮箱 (网易)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qq\",\n                                                children: \"QQ邮箱 (腾讯)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 714,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"other\",\n                                                children: \"其他\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.provider === \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600 mt-1\",\n                                        children: \"Gmail使用OAuth2安全认证，无需输入密码\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 718,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 13\n                            }, this),\n                            formData.provider !== \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: [\n                                                    \"邮箱地址 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 26\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 727,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: formData.email,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            email: e.target.value\n                                                        })),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                placeholder: \"<EMAIL>\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 730,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: [\n                                                    \"密码/应用专用密码 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"password\",\n                                                value: formData.password,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            password: e.target.value\n                                                        })),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                placeholder: \"输入密码\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"建议使用应用专用密码以提高安全性\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            formData.provider === \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-blue-600 rounded flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xs font-bold\",\n                                                    children: \"G\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 763,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900\",\n                                                children: \"Gmail OAuth认证\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700 mb-3\",\n                                        children: \"点击下方按钮将打开Google授权页面，安全地连接您的Gmail账户。\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600\",\n                                        children: \"我们不会存储您的密码，所有认证都通过Google官方进行。\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 760,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddAccount(false),\n                                        className: \"flex-1 px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                        disabled: isSubmitting,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 21\n                                                }, this),\n                                                formData.provider === \"gmail\" ? \"授权中...\" : \"添加中...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: formData.provider === \"gmail\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 799,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"使用Google授权\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 804,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"添加账户\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 669,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 668,\n            columnNumber: 7\n        }, this);\n    }\n    _s1(AddAccountModal, \"Le4pLMZEobX1cUesYRCKZ54LCII=\");\n}\n_s(Home, \"AISFGFfFKTLEvVVOFFnGq9HpA0Q=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});