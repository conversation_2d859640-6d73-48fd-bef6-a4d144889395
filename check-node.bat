@echo off
echo 检查Node.js安装状态...
echo.

echo 1. 检查Node.js版本:
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH
    goto :error
) else (
    echo ✅ Node.js已安装
)

echo.
echo 2. 检查npm版本:
npm --version
if %errorlevel% neq 0 (
    echo ❌ npm未安装
    goto :error
) else (
    echo ✅ npm已安装
)

echo.
echo 3. 检查项目目录:
if exist "package.json" (
    echo ✅ 找到package.json文件
) else (
    echo ❌ 未找到package.json文件
    goto :error
)

echo.
echo 🎉 环境检查完成！可以开始安装项目依赖了。
echo 请运行以下命令：
echo   npm install
echo   npm run dev
goto :end

:error
echo.
echo ❌ 环境检查失败，请确保：
echo 1. Node.js已正确安装
echo 2. 已重启命令行窗口
echo 3. 在正确的项目目录中运行此脚本

:end
pause
