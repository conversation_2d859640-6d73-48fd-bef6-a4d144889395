/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { people_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof people_v1.People;
};
export declare function people(version: 'v1'): people_v1.People;
export declare function people(options: people_v1.Options): people_v1.People;
declare const auth: AuthPlus;
export { auth };
export { people_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
