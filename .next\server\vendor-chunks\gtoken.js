"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gtoken";
exports.ids = ["vendor-chunks/gtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/gtoken/build/cjs/src/index.cjs":
/*!*****************************************************!*\
  !*** ./node_modules/gtoken/build/cjs/src/index.cjs ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GoogleToken = void 0;\nvar fs = _interopRequireWildcard(__webpack_require__(/*! fs */ \"fs\"));\nvar _gaxios = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nvar jws = _interopRequireWildcard(__webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\"));\nvar path = _interopRequireWildcard(__webpack_require__(/*! path */ \"path\"));\nvar _util = __webpack_require__(/*! util */ \"util\");\nfunction _interopRequireWildcard(e, t) {\n    if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap();\n    return (_interopRequireWildcard = function _interopRequireWildcard(e, t) {\n        if (!t && e && e.__esModule) return e;\n        var o, i, f = {\n            __proto__: null,\n            \"default\": e\n        };\n        if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f;\n        if (o = t ? n : r) {\n            if (o.has(e)) return o.get(e);\n            o.set(e, f);\n        }\n        for(var _t3 in e)\"default\" !== _t3 && ({}).hasOwnProperty.call(e, _t3) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t3)) && (i.get || i.set) ? o(f, _t3, i) : f[_t3] = e[_t3]);\n        return f;\n    })(e, t);\n}\nfunction _typeof(o) {\n    \"@babel/helpers - typeof\";\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function(o) {\n        return typeof o;\n    } : function(o) {\n        return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n}\nfunction _classPrivateMethodInitSpec(e, a) {\n    _checkPrivateRedeclaration(e, a), a.add(e);\n}\nfunction _classPrivateFieldInitSpec(e, t, a) {\n    _checkPrivateRedeclaration(e, t), t.set(e, a);\n}\nfunction _checkPrivateRedeclaration(e, t) {\n    if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n}\nfunction _classPrivateFieldSet(s, a, r) {\n    return s.set(_assertClassBrand(s, a), r), r;\n}\nfunction _classPrivateFieldGet(s, a) {\n    return s.get(_assertClassBrand(s, a));\n}\nfunction _assertClassBrand(e, t, n) {\n    if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n    throw new TypeError(\"Private element is not present on this object\");\n}\nfunction _defineProperties(e, r) {\n    for(var t = 0; t < r.length; t++){\n        var o = r[t];\n        o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n    }\n}\nfunction _createClass(e, r, t) {\n    return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n        writable: !1\n    }), e;\n}\nfunction _classCallCheck(a, n) {\n    if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _callSuper(t, o, e) {\n    return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nfunction _possibleConstructorReturn(t, e) {\n    if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n    if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n    return _assertThisInitialized(t);\n}\nfunction _assertThisInitialized(e) {\n    if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n    return e;\n}\nfunction _inherits(t, e) {\n    if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n    t.prototype = Object.create(e && e.prototype, {\n        constructor: {\n            value: t,\n            writable: !0,\n            configurable: !0\n        }\n    }), Object.defineProperty(t, \"prototype\", {\n        writable: !1\n    }), e && _setPrototypeOf(t, e);\n}\nfunction _wrapNativeSuper(t) {\n    var r = \"function\" == typeof Map ? new Map() : void 0;\n    return _wrapNativeSuper = function _wrapNativeSuper(t) {\n        if (null === t || !_isNativeFunction(t)) return t;\n        if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n        if (void 0 !== r) {\n            if (r.has(t)) return r.get(t);\n            r.set(t, Wrapper);\n        }\n        function Wrapper() {\n            return _construct(t, arguments, _getPrototypeOf(this).constructor);\n        }\n        return Wrapper.prototype = Object.create(t.prototype, {\n            constructor: {\n                value: Wrapper,\n                enumerable: !1,\n                writable: !0,\n                configurable: !0\n            }\n        }), _setPrototypeOf(Wrapper, t);\n    }, _wrapNativeSuper(t);\n}\nfunction _construct(t, e, r) {\n    if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n    var o = [\n        null\n    ];\n    o.push.apply(o, e);\n    var p = new (t.bind.apply(t, o))();\n    return r && _setPrototypeOf(p, r.prototype), p;\n}\nfunction _isNativeReflectConstruct() {\n    try {\n        var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));\n    } catch (t) {}\n    return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n        return !!t;\n    })();\n}\nfunction _isNativeFunction(t) {\n    try {\n        return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n    } catch (n) {\n        return \"function\" == typeof t;\n    }\n}\nfunction _setPrototypeOf(t, e) {\n    return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(t, e) {\n        return t.__proto__ = e, t;\n    }, _setPrototypeOf(t, e);\n}\nfunction _getPrototypeOf(t) {\n    return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(t) {\n        return t.__proto__ || Object.getPrototypeOf(t);\n    }, _getPrototypeOf(t);\n}\nfunction _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n        value: t,\n        enumerable: !0,\n        configurable: !0,\n        writable: !0\n    }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n    if (\"object\" != _typeof(t) || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n        var i = e.call(t, r || \"default\");\n        if (\"object\" != _typeof(i)) return i;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n}\nfunction _regenerator() {\n    var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\";\n    function i(r, n, o, i) {\n        var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype);\n        return _regeneratorDefine2(u, \"_invoke\", function(r, n, o) {\n            var i, c, u, f = 0, p = o || [], y = !1, G = {\n                p: 0,\n                n: 0,\n                v: e,\n                a: d,\n                f: d.bind(e, 4),\n                d: function d(t, r) {\n                    return i = t, c = 0, u = e, G.n = r, a;\n                }\n            };\n            function d(r, n) {\n                for(c = r, u = n, t = 0; !y && f && !o && t < p.length; t++){\n                    var o, i = p[t], d = G.p, l = i[2];\n                    r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0));\n                }\n                if (o || r > 1) return a;\n                throw y = !0, n;\n            }\n            return function(o, p, l) {\n                if (f > 1) throw TypeError(\"Generator is already running\");\n                for(y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;){\n                    i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u);\n                    try {\n                        if (f = 2, i) {\n                            if (c || (o = \"next\"), t = i[o]) {\n                                if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\");\n                                if (!t.done) return t;\n                                u = t.value, c < 2 && (c = 0);\n                            } else 1 === c && (t = i[\"return\"]) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1);\n                            i = e;\n                        } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break;\n                    } catch (t) {\n                        i = e, c = 1, u = t;\n                    } finally{\n                        f = 1;\n                    }\n                }\n                return {\n                    value: t,\n                    done: y\n                };\n            };\n        }(r, o, i), !0), u;\n    }\n    var a = {};\n    function Generator() {}\n    function GeneratorFunction() {}\n    function GeneratorFunctionPrototype() {}\n    t = Object.getPrototypeOf;\n    var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function() {\n        return this;\n    }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c);\n    function f(e) {\n        return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e;\n    }\n    return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function() {\n        return this;\n    }), _regeneratorDefine2(u, \"toString\", function() {\n        return \"[object Generator]\";\n    }), (_regenerator = function _regenerator() {\n        return {\n            w: i,\n            m: f\n        };\n    })();\n}\nfunction _regeneratorDefine2(e, r, n, t) {\n    var i = Object.defineProperty;\n    try {\n        i({}, \"\", {});\n    } catch (e) {\n        i = 0;\n    }\n    _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) {\n        if (r) i ? i(e, r, {\n            value: n,\n            enumerable: !t,\n            configurable: !t,\n            writable: !t\n        }) : e[r] = n;\n        else {\n            var o = function o(r, n) {\n                _regeneratorDefine2(e, r, function(e) {\n                    return this._invoke(r, n, e);\n                });\n            };\n            o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2);\n        }\n    }, _regeneratorDefine2(e, r, n, t);\n}\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) {\n    try {\n        var i = n[a](c), u = i.value;\n    } catch (n) {\n        return void e(n);\n    }\n    i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n    return function() {\n        var t = this, e = arguments;\n        return new Promise(function(r, o) {\n            var a = n.apply(t, e);\n            function _next(n) {\n                asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n            }\n            function _throw(n) {\n                asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n            }\n            _next(void 0);\n        });\n    };\n} /**\n * Copyright 2018 Google LLC\n *\n * Distributed under MIT license.\n * See file LICENSE for detail or copy at https://opensource.org/licenses/MIT\n */ \nvar readFile = fs.readFile ? (0, _util.promisify)(fs.readFile) : /*#__PURE__*/ _asyncToGenerator(/*#__PURE__*/ _regenerator().m(function _callee() {\n    return _regenerator().w(function(_context) {\n        while(1)switch(_context.n){\n            case 0:\n                throw new ErrorWithCode(\"use key rather than keyFile.\", \"MISSING_CREDENTIALS\");\n            case 1:\n                return _context.a(2);\n        }\n    }, _callee);\n}));\nvar GOOGLE_TOKEN_URL = \"https://oauth2.googleapis.com/token\";\nvar GOOGLE_REVOKE_TOKEN_URL = \"https://oauth2.googleapis.com/revoke?token=\";\nvar ErrorWithCode = /*#__PURE__*/ function(_Error) {\n    function ErrorWithCode(message, code) {\n        var _this;\n        _classCallCheck(this, ErrorWithCode);\n        _this = _callSuper(this, ErrorWithCode, [\n            message\n        ]);\n        _defineProperty(_this, \"code\", void 0);\n        _this.code = code;\n        return _this;\n    }\n    _inherits(ErrorWithCode, _Error);\n    return _createClass(ErrorWithCode);\n}(/*#__PURE__*/ _wrapNativeSuper(Error));\nvar _inFlightRequest = /*#__PURE__*/ new WeakMap();\nvar _GoogleToken_brand = /*#__PURE__*/ new WeakSet();\nvar GoogleToken = exports.GoogleToken = /*#__PURE__*/ function() {\n    /**\n   * Create a GoogleToken.\n   *\n   * @param options  Configuration object.\n   */ function GoogleToken(_options) {\n        _classCallCheck(this, GoogleToken);\n        _classPrivateMethodInitSpec(this, _GoogleToken_brand);\n        _defineProperty(this, \"expiresAt\", void 0);\n        _defineProperty(this, \"key\", void 0);\n        _defineProperty(this, \"keyFile\", void 0);\n        _defineProperty(this, \"iss\", void 0);\n        _defineProperty(this, \"sub\", void 0);\n        _defineProperty(this, \"scope\", void 0);\n        _defineProperty(this, \"rawToken\", void 0);\n        _defineProperty(this, \"tokenExpires\", void 0);\n        _defineProperty(this, \"email\", void 0);\n        _defineProperty(this, \"additionalClaims\", void 0);\n        _defineProperty(this, \"eagerRefreshThresholdMillis\", void 0);\n        _defineProperty(this, \"transporter\", {\n            request: function request(opts) {\n                return (0, _gaxios.request)(opts);\n            }\n        });\n        _classPrivateFieldInitSpec(this, _inFlightRequest, void 0);\n        _assertClassBrand(_GoogleToken_brand, this, _configure).call(this, _options);\n    }\n    /**\n   * Returns whether the token has expired.\n   *\n   * @return true if the token has expired, false otherwise.\n   */ return _createClass(GoogleToken, [\n        {\n            key: \"accessToken\",\n            get: function get() {\n                return this.rawToken ? this.rawToken.access_token : undefined;\n            }\n        },\n        {\n            key: \"idToken\",\n            get: function get() {\n                return this.rawToken ? this.rawToken.id_token : undefined;\n            }\n        },\n        {\n            key: \"tokenType\",\n            get: function get() {\n                return this.rawToken ? this.rawToken.token_type : undefined;\n            }\n        },\n        {\n            key: \"refreshToken\",\n            get: function get() {\n                return this.rawToken ? this.rawToken.refresh_token : undefined;\n            }\n        },\n        {\n            key: \"hasExpired\",\n            value: function hasExpired() {\n                var now = new Date().getTime();\n                if (this.rawToken && this.expiresAt) {\n                    return now >= this.expiresAt;\n                } else {\n                    return true;\n                }\n            }\n        },\n        {\n            key: \"isTokenExpiring\",\n            value: function isTokenExpiring() {\n                var _this$eagerRefreshThr;\n                var now = new Date().getTime();\n                var eagerRefreshThresholdMillis = (_this$eagerRefreshThr = this.eagerRefreshThresholdMillis) !== null && _this$eagerRefreshThr !== void 0 ? _this$eagerRefreshThr : 0;\n                if (this.rawToken && this.expiresAt) {\n                    return this.expiresAt <= now + eagerRefreshThresholdMillis;\n                } else {\n                    return true;\n                }\n            }\n        },\n        {\n            key: \"getToken\",\n            value: function getToken(callback) {\n                var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n                if (_typeof(callback) === \"object\") {\n                    opts = callback;\n                    callback = undefined;\n                }\n                opts = Object.assign({\n                    forceRefresh: false\n                }, opts);\n                if (callback) {\n                    var cb = callback;\n                    _assertClassBrand(_GoogleToken_brand, this, _getTokenAsync).call(this, opts).then(function(t) {\n                        return cb(null, t);\n                    }, callback);\n                    return;\n                }\n                return _assertClassBrand(_GoogleToken_brand, this, _getTokenAsync).call(this, opts);\n            }\n        },\n        {\n            key: \"getCredentials\",\n            value: function() {\n                var _getCredentials = _asyncToGenerator(/*#__PURE__*/ _regenerator().m(function _callee2(keyFile) {\n                    var ext, key, body, privateKey, clientEmail, _privateKey, _t;\n                    return _regenerator().w(function(_context2) {\n                        while(1)switch(_context2.n){\n                            case 0:\n                                ext = path.extname(keyFile);\n                                _t = ext;\n                                _context2.n = _t === \".json\" ? 1 : _t === \".der\" ? 4 : _t === \".crt\" ? 4 : _t === \".pem\" ? 4 : _t === \".p12\" ? 6 : _t === \".pfx\" ? 6 : 7;\n                                break;\n                            case 1:\n                                _context2.n = 2;\n                                return readFile(keyFile, \"utf8\");\n                            case 2:\n                                key = _context2.v;\n                                body = JSON.parse(key);\n                                privateKey = body.private_key;\n                                clientEmail = body.client_email;\n                                if (!(!privateKey || !clientEmail)) {\n                                    _context2.n = 3;\n                                    break;\n                                }\n                                throw new ErrorWithCode(\"private_key and client_email are required.\", \"MISSING_CREDENTIALS\");\n                            case 3:\n                                return _context2.a(2, {\n                                    privateKey: privateKey,\n                                    clientEmail: clientEmail\n                                });\n                            case 4:\n                                _context2.n = 5;\n                                return readFile(keyFile, \"utf8\");\n                            case 5:\n                                _privateKey = _context2.v;\n                                return _context2.a(2, {\n                                    privateKey: _privateKey\n                                });\n                            case 6:\n                                throw new ErrorWithCode(\"*.p12 certificates are not supported after v6.1.2. \" + \"Consider utilizing *.json format or converting *.p12 to *.pem using the OpenSSL CLI.\", \"UNKNOWN_CERTIFICATE_TYPE\");\n                            case 7:\n                                throw new ErrorWithCode(\"Unknown certificate type. Type is determined based on file extension. \" + \"Current supported extensions are *.json, and *.pem.\", \"UNKNOWN_CERTIFICATE_TYPE\");\n                            case 8:\n                                return _context2.a(2);\n                        }\n                    }, _callee2);\n                }));\n                function getCredentials(_x) {\n                    return _getCredentials.apply(this, arguments);\n                }\n                return getCredentials;\n            }()\n        },\n        {\n            key: \"revokeToken\",\n            value: function revokeToken(callback) {\n                if (callback) {\n                    _assertClassBrand(_GoogleToken_brand, this, _revokeTokenAsync).call(this).then(function() {\n                        return callback();\n                    }, callback);\n                    return;\n                }\n                return _assertClassBrand(_GoogleToken_brand, this, _revokeTokenAsync).call(this);\n            }\n        }\n    ]);\n}();\nfunction _getTokenAsync(_x2) {\n    return _getTokenAsync2.apply(this, arguments);\n}\nfunction _getTokenAsync2() {\n    _getTokenAsync2 = _asyncToGenerator(/*#__PURE__*/ _regenerator().m(function _callee3(opts) {\n        return _regenerator().w(function(_context3) {\n            while(1)switch(_context3.n){\n                case 0:\n                    if (!(_classPrivateFieldGet(_inFlightRequest, this) && !opts.forceRefresh)) {\n                        _context3.n = 1;\n                        break;\n                    }\n                    return _context3.a(2, _classPrivateFieldGet(_inFlightRequest, this));\n                case 1:\n                    _context3.p = 1;\n                    _context3.n = 2;\n                    return _classPrivateFieldSet(_inFlightRequest, this, _assertClassBrand(_GoogleToken_brand, this, _getTokenAsyncInner).call(this, opts));\n                case 2:\n                    return _context3.a(2, _context3.v);\n                case 3:\n                    _context3.p = 3;\n                    _classPrivateFieldSet(_inFlightRequest, this, undefined);\n                    return _context3.f(3);\n                case 4:\n                    return _context3.a(2);\n            }\n        }, _callee3, this, [\n            [\n                1,\n                ,\n                3,\n                4\n            ]\n        ]);\n    }));\n    return _getTokenAsync2.apply(this, arguments);\n}\nfunction _getTokenAsyncInner(_x3) {\n    return _getTokenAsyncInner2.apply(this, arguments);\n}\nfunction _getTokenAsyncInner2() {\n    _getTokenAsyncInner2 = _asyncToGenerator(/*#__PURE__*/ _regenerator().m(function _callee4(opts) {\n        var creds;\n        return _regenerator().w(function(_context4) {\n            while(1)switch(_context4.n){\n                case 0:\n                    if (!(this.isTokenExpiring() === false && opts.forceRefresh === false)) {\n                        _context4.n = 1;\n                        break;\n                    }\n                    return _context4.a(2, Promise.resolve(this.rawToken));\n                case 1:\n                    if (!(!this.key && !this.keyFile)) {\n                        _context4.n = 2;\n                        break;\n                    }\n                    throw new Error(\"No key or keyFile set.\");\n                case 2:\n                    if (!(!this.key && this.keyFile)) {\n                        _context4.n = 4;\n                        break;\n                    }\n                    _context4.n = 3;\n                    return this.getCredentials(this.keyFile);\n                case 3:\n                    creds = _context4.v;\n                    this.key = creds.privateKey;\n                    this.iss = creds.clientEmail || this.iss;\n                    if (!creds.clientEmail) {\n                        _assertClassBrand(_GoogleToken_brand, this, _ensureEmail).call(this);\n                    }\n                case 4:\n                    return _context4.a(2, _assertClassBrand(_GoogleToken_brand, this, _requestToken).call(this));\n            }\n        }, _callee4, this);\n    }));\n    return _getTokenAsyncInner2.apply(this, arguments);\n}\nfunction _ensureEmail() {\n    if (!this.iss) {\n        throw new ErrorWithCode(\"email is required.\", \"MISSING_CREDENTIALS\");\n    }\n}\nfunction _revokeTokenAsync() {\n    return _revokeTokenAsync2.apply(this, arguments);\n}\nfunction _revokeTokenAsync2() {\n    _revokeTokenAsync2 = _asyncToGenerator(/*#__PURE__*/ _regenerator().m(function _callee5() {\n        var url;\n        return _regenerator().w(function(_context5) {\n            while(1)switch(_context5.n){\n                case 0:\n                    if (this.accessToken) {\n                        _context5.n = 1;\n                        break;\n                    }\n                    throw new Error(\"No token to revoke.\");\n                case 1:\n                    url = GOOGLE_REVOKE_TOKEN_URL + this.accessToken;\n                    _context5.n = 2;\n                    return this.transporter.request({\n                        url: url,\n                        retry: true\n                    });\n                case 2:\n                    _assertClassBrand(_GoogleToken_brand, this, _configure).call(this, {\n                        email: this.iss,\n                        sub: this.sub,\n                        key: this.key,\n                        keyFile: this.keyFile,\n                        scope: this.scope,\n                        additionalClaims: this.additionalClaims\n                    });\n                case 3:\n                    return _context5.a(2);\n            }\n        }, _callee5, this);\n    }));\n    return _revokeTokenAsync2.apply(this, arguments);\n}\n/**\n * Configure the GoogleToken for re-use.\n * @param  {object} options Configuration object.\n */ function _configure() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    this.keyFile = options.keyFile;\n    this.key = options.key;\n    this.rawToken = undefined;\n    this.iss = options.email || options.iss;\n    this.sub = options.sub;\n    this.additionalClaims = options.additionalClaims;\n    if (_typeof(options.scope) === \"object\") {\n        this.scope = options.scope.join(\" \");\n    } else {\n        this.scope = options.scope;\n    }\n    this.eagerRefreshThresholdMillis = options.eagerRefreshThresholdMillis;\n    if (options.transporter) {\n        this.transporter = options.transporter;\n    }\n}\n/**\n * Request the token from Google.\n */ function _requestToken() {\n    return _requestToken2.apply(this, arguments);\n}\nfunction _requestToken2() {\n    _requestToken2 = _asyncToGenerator(/*#__PURE__*/ _regenerator().m(function _callee6() {\n        var iat, additionalClaims, payload, signedJWT, r, _response, _response2, body, desc, _t2;\n        return _regenerator().w(function(_context6) {\n            while(1)switch(_context6.n){\n                case 0:\n                    iat = Math.floor(new Date().getTime() / 1000);\n                    additionalClaims = this.additionalClaims || {};\n                    payload = Object.assign({\n                        iss: this.iss,\n                        scope: this.scope,\n                        aud: GOOGLE_TOKEN_URL,\n                        exp: iat + 3600,\n                        iat: iat,\n                        sub: this.sub\n                    }, additionalClaims);\n                    signedJWT = jws.sign({\n                        header: {\n                            alg: \"RS256\"\n                        },\n                        payload: payload,\n                        secret: this.key\n                    });\n                    _context6.p = 1;\n                    _context6.n = 2;\n                    return this.transporter.request({\n                        method: \"POST\",\n                        url: GOOGLE_TOKEN_URL,\n                        data: new URLSearchParams({\n                            grant_type: \"urn:ietf:params:oauth:grant-type:jwt-bearer\",\n                            assertion: signedJWT\n                        }),\n                        responseType: \"json\",\n                        retryConfig: {\n                            httpMethodsToRetry: [\n                                \"POST\"\n                            ]\n                        }\n                    });\n                case 2:\n                    r = _context6.v;\n                    this.rawToken = r.data;\n                    this.expiresAt = r.data.expires_in === null || r.data.expires_in === undefined ? undefined : (iat + r.data.expires_in) * 1000;\n                    return _context6.a(2, this.rawToken);\n                case 3:\n                    _context6.p = 3;\n                    _t2 = _context6.v;\n                    this.rawToken = undefined;\n                    this.tokenExpires = undefined;\n                    body = _t2.response && (_response = _t2.response) !== null && _response !== void 0 && _response.data ? (_response2 = _t2.response) === null || _response2 === void 0 ? void 0 : _response2.data : {};\n                    if (body.error) {\n                        desc = body.error_description ? \": \".concat(body.error_description) : \"\";\n                        _t2.message = \"\".concat(body.error).concat(desc);\n                    }\n                    throw _t2;\n                case 4:\n                    return _context6.a(2);\n            }\n        }, _callee6, this, [\n            [\n                1,\n                3\n            ]\n        ]);\n    }));\n    return _requestToken2.apply(this, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gtoken/build/cjs/src/index.cjs\n");

/***/ })

};
;