/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { observability_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof observability_v1.Observability;
};
export declare function observability(version: 'v1'): observability_v1.Observability;
export declare function observability(options: observability_v1.Options): observability_v1.Observability;
declare const auth: AuthPlus;
export { auth };
export { observability_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
