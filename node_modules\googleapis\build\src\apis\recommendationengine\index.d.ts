/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { recommendationengine_v1beta1 } from './v1beta1';
export declare const VERSIONS: {
    v1beta1: typeof recommendationengine_v1beta1.Recommendationengine;
};
export declare function recommendationengine(version: 'v1beta1'): recommendationengine_v1beta1.Recommendationengine;
export declare function recommendationengine(options: recommendationengine_v1beta1.Options): recommendationengine_v1beta1.Recommendationengine;
declare const auth: AuthPlus;
export { auth };
export { recommendationengine_v1beta1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
