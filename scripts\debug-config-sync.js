// 配置同步调试工具
const Imap = require('imap')

// 模拟测试连接函数
async function testConnection(email, password, config) {
  console.log(`🔧 测试配置: ${config.host}:${config.port}, TLS: ${config.secure}`)
  
  return new Promise((resolve, reject) => {
    const imap = new Imap({
      user: email,
      password: password,
      host: config.host,
      port: config.port,
      tls: config.secure,
      tlsOptions: {
        rejectUnauthorized: false,
        secureProtocol: 'TLSv1_2_method',
        checkServerIdentity: () => undefined
      },
      authTimeout: 10000,
      connTimeout: 10000,
      keepalive: false,
      autotls: 'never',
      authMethods: ['PLAIN', 'LOGIN']
    })

    const timeout = setTimeout(() => {
      imap.end()
      reject(new Error('连接超时'))
    }, 15000)

    imap.once('ready', () => {
      console.log(`✅ 连接成功: ${config.host}:${config.port}`)
      clearTimeout(timeout)
      imap.end()
      resolve({
        success: true,
        config: config,
        message: `连接成功: ${config.host}:${config.port}`
      })
    })

    imap.once('error', (err) => {
      console.log(`❌ 连接失败: ${err.message}`)
      clearTimeout(timeout)
      reject(err)
    })

    imap.connect()
  })
}

// 测试配置同步
async function testConfigSync() {
  const email = process.argv[2]
  const password = process.argv[3]
  const host = process.argv[4] || 'mail2.serv00.com'

  if (!email || !password) {
    console.log('用法: node debug-config-sync.js <邮箱> <密码> [服务器]')
    console.log('例如: node debug-config-sync.js <EMAIL> password123 mail2.serv00.com')
    return
  }

  console.log('🔍 测试配置同步问题')
  console.log('='.repeat(50))

  const configs = [
    { host, port: 993, secure: true, name: '配置1: SSL' },
    { host, port: 143, secure: false, name: '配置2: STARTTLS' }
  ]

  let successfulConfig = null

  for (const config of configs) {
    try {
      console.log(`\n📋 ${config.name}`)
      const result = await testConnection(email, password, config)
      
      if (result.success) {
        successfulConfig = result.config
        console.log(`✅ 找到可用配置:`, successfulConfig)
        break
      }
    } catch (error) {
      console.log(`❌ ${config.name} 失败: ${error.message}`)
    }
  }

  if (successfulConfig) {
    console.log('\n🎯 测试配置重用:')
    console.log('='.repeat(30))
    
    // 模拟第二次连接（添加邮箱时）
    try {
      const result2 = await testConnection(email, password, successfulConfig)
      if (result2.success) {
        console.log('✅ 配置重用成功！')
        console.log('💡 这证明配置同步应该可以工作')
      }
    } catch (error) {
      console.log('❌ 配置重用失败:', error.message)
      console.log('💡 这可能是配置同步的问题所在')
    }
  } else {
    console.log('\n❌ 没有找到可用的配置')
  }

  console.log('\n📊 调试总结:')
  console.log('='.repeat(30))
  console.log('1. 如果第一次测试成功，第二次也应该成功')
  console.log('2. 如果第二次失败，说明配置没有正确保存或传递')
  console.log('3. 检查前端是否正确保存了成功的配置')
  console.log('4. 检查添加邮箱时是否使用了保存的配置')
}

if (require.main === module) {
  testConfigSync().catch(console.error)
}

module.exports = { testConnection, testConfigSync }
