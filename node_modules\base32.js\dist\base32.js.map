{"version": 3, "sources": ["webpack:///webpack/bootstrap 221c6ffea9706b5be457", "webpack:///./base32.js"], "names": [], "mappings": ";;AAAA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,uBAAe;AACf;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,wC;;;;;;;ACtCA;;AAEA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB;AACA;;AAEA;AACA,4BAA4B;AAC5B;AACA;AACA,GAAG;AACH;AACA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,SAAS;AACT;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA,aAAa,OAAO;AACpB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY,QAAQ;AACpB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,aAAa,OAAO;AACpB;AACA,aAAa,OAAO;AACpB;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY,QAAQ;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,aAAa,gBAAgB;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,YAAY,OAAO;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,YAAY,OAAO;AACnB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,WAAW,OAAO;AAClB,WAAW,OAAO;AAClB,YAAY,OAAO;AACnB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId])\n \t\t\treturn installedModules[moduleId].exports;\n\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\texports: {},\n \t\t\tid: moduleId,\n \t\t\tloaded: false\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.loaded = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(0);\n\n\n/** WEBPACK FOOTER **\n ** webpack/bootstrap 221c6ffea9706b5be457\n **/", "\"use strict\";\n\n/**\n * Generate a character map.\n * @param {string} alphabet e.g. \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\"\n * @param {object} mappings map overrides from key to value\n * @method\n */\n\nvar charmap = function (alphabet, mappings) {\n  mappings || (mappings = {});\n  alphabet.split(\"\").forEach(function (c, i) {\n    if (!(c in mappings)) mappings[c] = i;\n  });\n  return mappings;\n}\n\n/**\n * The RFC 4648 base 32 alphabet and character map.\n * @see {@link https://tools.ietf.org/html/rfc4648}\n */\n\nvar rfc4648 = {\n  alphabet: \"ABCDEFGHIJKLMNOPQRSTUVWXYZ234567\",\n  charmap: {\n    0: 14,\n    1: 8\n  }\n};\n\nrfc4648.charmap = charmap(rfc4648.alphabet, rfc4648.charmap);\n\n/**\n * The Crockford base 32 alphabet and character map.\n * @see {@link http://www.crockford.com/wrmg/base32.html}\n */\n\nvar crockford = {\n  alphabet: \"0123456789ABCDEFGHJKMNPQRSTVWXYZ\",\n  charmap: {\n    O: 0,\n    I: 1,\n    L: 1\n  }\n};\n\ncrockford.charmap = charmap(crockford.alphabet, crockford.charmap);\n\n/**\n * base32hex\n * @see {@link https://en.wikipedia.org/wiki/Base32#base32hex}\n */\n\nvar base32hex = {\n  alphabet: \"0123456789ABCDEFGHIJKLMNOPQRSTUV\",\n  charmap: {}\n};\n\nbase32hex.charmap = charmap(base32hex.alphabet, base32hex.charmap);\n\n/**\n * Create a new `Decoder` with the given options.\n *\n * @param {object} [options]\n *   @param {string} [type] Supported Base-32 variants are \"rfc4648\" and\n *     \"crockford\".\n *   @param {object} [charmap] Override the character map used in decoding.\n * @constructor\n */\n\nfunction Decoder (options) {\n  this.buf = [];\n  this.shift = 8;\n  this.carry = 0;\n\n  if (options) {\n\n    switch (options.type) {\n      case \"rfc4648\":\n        this.charmap = exports.rfc4648.charmap;\n        break;\n      case \"crockford\":\n        this.charmap = exports.crockford.charmap;\n        break;\n      case \"base32hex\":\n        this.charmap = exports.base32hex.charmap;\n        break;\n      default:\n        throw new Error(\"invalid type\");\n    }\n\n    if (options.charmap) this.charmap = options.charmap;\n  }\n}\n\n/**\n * The default character map coresponds to RFC4648.\n */\n\nDecoder.prototype.charmap = rfc4648.charmap;\n\n/**\n * Decode a string, continuing from the previous state.\n *\n * @param {string} str\n * @return {Decoder} this\n */\n\nDecoder.prototype.write = function (str) {\n  var charmap = this.charmap;\n  var buf = this.buf;\n  var shift = this.shift;\n  var carry = this.carry;\n\n  // decode string\n  str.toUpperCase().split(\"\").forEach(function (char) {\n\n    // ignore padding\n    if (char == \"=\") return;\n\n    // lookup symbol\n    var symbol = charmap[char] & 0xff;\n\n    // 1: 00000 000\n    // 2:          00 00000 0\n    // 3:                    0000 0000\n    // 4:                             0 00000 00\n    // 5:                                       000 00000\n    // 6:                                                00000 000\n    // 7:                                                         00 00000 0\n\n    shift -= 5;\n    if (shift > 0) {\n      carry |= symbol << shift;\n    } else if (shift < 0) {\n      buf.push(carry | (symbol >> -shift));\n      shift += 8;\n      carry = (symbol << shift) & 0xff;\n    } else {\n      buf.push(carry | symbol);\n      shift = 8;\n      carry = 0;\n    }\n  });\n\n  // save state\n  this.shift = shift;\n  this.carry = carry;\n\n  // for chaining\n  return this;\n};\n\n/**\n * Finish decoding.\n *\n * @param {string} [str] The final string to decode.\n * @return {Array} Decoded byte array.\n */\n\nDecoder.prototype.finalize = function (str) {\n  if (str) {\n    this.write(str);\n  }\n  if (this.shift !== 8 && this.carry !== 0) {\n    this.buf.push(this.carry);\n    this.shift = 8;\n    this.carry = 0;\n  }\n  return this.buf;\n};\n\n/**\n * Create a new `Encoder` with the given options.\n *\n * @param {object} [options]\n *   @param {string} [type] Supported Base-32 variants are \"rfc4648\" and\n *     \"crockford\".\n *   @param {object} [alphabet] Override the alphabet used in encoding.\n * @constructor\n */\n\nfunction Encoder (options) {\n  this.buf = \"\";\n  this.shift = 3;\n  this.carry = 0;\n\n  if (options) {\n\n    switch (options.type) {\n      case \"rfc4648\":\n        this.alphabet = exports.rfc4648.alphabet;\n        break;\n      case \"crockford\":\n        this.alphabet = exports.crockford.alphabet;\n        break;\n      case \"base32hex\":\n        this.alphabet = exports.base32hex.alphabet;\n        break;\n      default:\n        throw new Error(\"invalid type\");\n    }\n\n    if (options.alphabet) this.alphabet = options.alphabet;\n    else if (options.lc) this.alphabet = this.alphabet.toLowerCase();\n  }\n}\n\n/**\n * The default alphabet coresponds to RFC4648.\n */\n\nEncoder.prototype.alphabet = rfc4648.alphabet;\n\n/**\n * Encode a byte array, continuing from the previous state.\n *\n * @param {byte[]} buf The byte array to encode.\n * @return {Encoder} this\n */\n\nEncoder.prototype.write = function (buf) {\n  var shift = this.shift;\n  var carry = this.carry;\n  var symbol;\n  var byte;\n  var i;\n\n  // encode each byte in buf\n  for (i = 0; i < buf.length; i++) {\n    byte = buf[i];\n\n    // 1: 00000 000\n    // 2:          00 00000 0\n    // 3:                    0000 0000\n    // 4:                             0 00000 00\n    // 5:                                       000 00000\n    // 6:                                                00000 000\n    // 7:                                                         00 00000 0\n\n    symbol = carry | (byte >> shift);\n    this.buf += this.alphabet[symbol & 0x1f];\n\n    if (shift > 5) {\n      shift -= 5;\n      symbol = byte >> shift;\n      this.buf += this.alphabet[symbol & 0x1f];\n    }\n\n    shift = 5 - shift;\n    carry = byte << shift;\n    shift = 8 - shift;\n  }\n\n  // save state\n  this.shift = shift;\n  this.carry = carry;\n\n  // for chaining\n  return this;\n};\n\n/**\n * Finish encoding.\n *\n * @param {byte[]} [buf] The final byte array to encode.\n * @return {string} The encoded byte array.\n */\n\nEncoder.prototype.finalize = function (buf) {\n  if (buf) {\n    this.write(buf);\n  }\n  if (this.shift !== 3) {\n    this.buf += this.alphabet[this.carry & 0x1f];\n    this.shift = 3;\n    this.carry = 0;\n  }\n  return this.buf;\n};\n\n/**\n * Convenience encoder.\n *\n * @param {byte[]} buf The byte array to encode.\n * @param {object} [options] Options to pass to the encoder.\n * @return {string} The encoded string.\n */\n\nexports.encode = function (buf, options) {\n  return new Encoder(options).finalize(buf);\n};\n\n/**\n * Convenience decoder.\n *\n * @param {string} str The string to decode.\n * @param {object} [options] Options to pass to the decoder.\n * @return {byte[]} The decoded byte array.\n */\n\nexports.decode = function (str, options) {\n  return new Decoder(options).finalize(str);\n};\n\n// Exports.\nexports.Decoder = Decoder;\nexports.Encoder = Encoder;\nexports.charmap = charmap;\nexports.crockford = crockford;\nexports.rfc4648 = rfc4648;\nexports.base32hex = base32hex;\n\n\n\n/*****************\n ** WEBPACK FOOTER\n ** ./base32.js\n ** module id = 0\n ** module chunks = 0\n **/"], "sourceRoot": "", "file": "base32.js"}