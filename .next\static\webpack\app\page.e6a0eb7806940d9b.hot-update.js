"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Home() {\n    var _emails_find, _emails_find1, _emails_find2;\n    _s();\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            email: \"<EMAIL>\",\n            provider: \"gmail\",\n            name: \"个人Gmail\",\n            isActive: true,\n            unreadCount: 3\n        },\n        {\n            id: \"2\",\n            email: \"<EMAIL>\",\n            provider: \"outlook\",\n            name: \"工作邮箱\",\n            isActive: false,\n            unreadCount: 1\n        }\n    ]);\n    // 模拟邮件数据\n    const emails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 获取当前账户的邮件\n    const currentEmails = emails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-800\",\n                                        children: \"邮箱客户端\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>switchAccount(account.id),\n                                            className: \"p-2 rounded text-sm cursor-pointer transition-colors \".concat(account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: getProviderIcon(account.provider)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: account.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs opacity-75\",\n                                                                        children: account.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                        children: account.unreadCount\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, account.id, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name) || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.unreadCount) && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: \"p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 \".concat(selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\", \" \").concat(email.unread ? \"bg-white\" : \"bg-gray-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"),\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm mb-1 \".concat(email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"),\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: (_emails_find = emails.find((e)=>e.id === selectedEmail)) === null || _emails_find === void 0 ? void 0 : _emails_find.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: (_emails_find1 = emails.find((e)=>e.id === selectedEmail)) === null || _emails_find1 === void 0 ? void 0 : _emails_find1.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: (_emails_find2 = emails.find((e)=>e.id === selectedEmail)) === null || _emails_find2 === void 0 ? void 0 : _emails_find2.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"8Oj2PpNPY/1uOEqHmdxFDc73Aig=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ3NGO0FBdUJ2RyxTQUFTVTtRQW9RUEMsY0FNTUEsZUFHQUE7O0lBNVFyQixNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHYiwrQ0FBUUEsQ0FBZ0I7SUFDbEUsTUFBTSxDQUFDYyxhQUFhQyxlQUFlLEdBQUdmLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ2dCLGdCQUFnQkMsa0JBQWtCLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNrQixpQkFBaUJDLG1CQUFtQixHQUFHbkIsK0NBQVFBLENBQVM7SUFFL0QsU0FBUztJQUNULE1BQU0sQ0FBQ29CLGVBQWVDLGlCQUFpQixHQUFHckIsK0NBQVFBLENBQWlCO1FBQ2pFO1lBQ0VzQixJQUFJO1lBQ0pDLE9BQU87WUFDUEMsVUFBVTtZQUNWQyxNQUFNO1lBQ05DLFVBQVU7WUFDVkMsYUFBYTtRQUNmO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxVQUFVO1lBQ1ZDLGFBQWE7UUFDZjtLQUNEO0lBRUQsU0FBUztJQUNULE1BQU1oQixTQUFrQjtRQUN0QjtZQUNFVyxJQUFJO1lBQ0pNLE1BQU07WUFDTkMsU0FBUztZQUNUQyxTQUFTO1lBQ1RDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFdBQVc7UUFDYjtRQUNBO1lBQ0VaLElBQUk7WUFDSk0sTUFBTTtZQUNOQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsV0FBVztRQUNiO1FBQ0E7WUFDRVosSUFBSTtZQUNKTSxNQUFNO1lBQ05DLFNBQVM7WUFDVEMsU0FBUztZQUNUQyxNQUFNO1lBQ05DLFFBQVE7WUFDUkMsU0FBUztZQUNUQyxXQUFXO1FBQ2I7UUFDQTtZQUNFWixJQUFJO1lBQ0pNLE1BQU07WUFDTkMsU0FBUztZQUNUQyxTQUFTO1lBQ1RDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFdBQVc7UUFDYjtLQUNEO0lBRUQsV0FBVztJQUNYLE1BQU1DLGdCQUFnQmYsY0FBY2dCLElBQUksQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSWYsRUFBRSxLQUFLSjtJQUUzRCxZQUFZO0lBQ1osTUFBTW9CLGdCQUFnQjNCLE9BQU80QixNQUFNLENBQUNoQixDQUFBQSxRQUFTQSxNQUFNVyxTQUFTLEtBQUtoQjtJQUVqRSxZQUFZO0lBQ1osTUFBTXNCLGtCQUFrQixDQUFDaEI7UUFDdkIsT0FBUUE7WUFDTixLQUFLO2dCQUFTLE9BQU87WUFDckIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQU8sT0FBTztZQUNuQixLQUFLO2dCQUFNLE9BQU87WUFDbEI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1pQixnQkFBZ0IsQ0FBQ1A7UUFDckJmLG1CQUFtQmU7UUFDbkJyQixpQkFBaUIsTUFBTSxVQUFVOztJQUNuQztJQUVBLHFCQUNFLDhEQUFDNkI7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMxQyxvSUFBSUE7d0NBQUMwQyxXQUFVOzs7Ozs7a0RBQ2hCLDhEQUFDQzt3Q0FBR0QsV0FBVTtrREFBc0M7Ozs7Ozs7Ozs7OzswQ0FFdEQsOERBQUNFO2dDQUNDQyxTQUFTLElBQU0vQixlQUFlO2dDQUM5QjRCLFdBQVU7O2tEQUVWLDhEQUFDekMsb0lBQUlBO3dDQUFDeUMsV0FBVTs7Ozs7O29DQUFZOzs7Ozs7Ozs7Ozs7O2tDQU1oQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDSTtnQ0FBR0osV0FBVTswQ0FBeUM7Ozs7OzswQ0FDdkQsOERBQUNEO2dDQUFJQyxXQUFVOztvQ0FDWnZCLGNBQWM0QixHQUFHLENBQUMsQ0FBQ0Msd0JBQ2xCLDhEQUFDUDs0Q0FFQ0ksU0FBUyxJQUFNTCxjQUFjUSxRQUFRM0IsRUFBRTs0Q0FDdkNxQixXQUFXLHdEQUlWLE9BSENNLFFBQVEzQixFQUFFLEtBQUtKLGtCQUNYLG9EQUNBO3NEQUdOLDRFQUFDd0I7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNPOzBFQUFNVixnQkFBZ0JTLFFBQVF6QixRQUFROzs7Ozs7MEVBQ3ZDLDhEQUFDa0I7Z0VBQUlDLFdBQVU7O2tGQUNiLDhEQUFDTzt3RUFBS1AsV0FBVTtrRkFBZU0sUUFBUXhCLElBQUk7Ozs7OztrRkFDM0MsOERBQUN5Qjt3RUFBS1AsV0FBVTtrRkFBc0JNLFFBQVExQixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0RBR3REMEIsUUFBUXRCLFdBQVcsR0FBRyxtQkFDckIsOERBQUN1Qjt3REFBS1AsV0FBVTtrRUFDYk0sUUFBUXRCLFdBQVc7Ozs7Ozs7Ozs7OzsyQ0FsQnJCc0IsUUFBUTNCLEVBQUU7Ozs7O2tEQXdCbkIsOERBQUN1Qjt3Q0FDQ0MsU0FBUyxJQUFNN0Isa0JBQWtCO3dDQUNqQzBCLFdBQVU7OzBEQUVWLDhEQUFDekMsb0lBQUlBO2dEQUFDeUMsV0FBVTs7Ozs7OzRDQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9sQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUE2Qjs7Ozs7O2tEQUM1Qyw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1pSLENBQUFBLDBCQUFBQSxvQ0FBQUEsY0FBZVYsSUFBSSxLQUFJOzs7Ozs7Ozs7Ozs7MENBRzVCLDhEQUFDMEI7Z0NBQUlSLFdBQVU7O2tEQUNiLDhEQUFDUzt3Q0FBRUMsTUFBSzt3Q0FBSVYsV0FBVTs7MERBQ3BCLDhEQUFDckMsb0lBQUtBO2dEQUFDcUMsV0FBVTs7Ozs7OzRDQUFZOzRDQUU1QlIsQ0FBQUEsMEJBQUFBLG9DQUFBQSxjQUFlUixXQUFXLEtBQUlRLGNBQWNSLFdBQVcsR0FBRyxtQkFDekQsOERBQUN1QjtnREFBS1AsV0FBVTswREFDYlIsY0FBY1IsV0FBVzs7Ozs7Ozs7Ozs7O2tEQUloQyw4REFBQ3lCO3dDQUFFQyxNQUFLO3dDQUFJVixXQUFVOzswREFDcEIsOERBQUNwQyxvSUFBSUE7Z0RBQUNvQyxXQUFVOzs7Ozs7NENBQVk7Ozs7Ozs7a0RBRzlCLDhEQUFDUzt3Q0FBRUMsTUFBSzt3Q0FBSVYsV0FBVTs7MERBQ3BCLDhEQUFDdEMsb0lBQUlBO2dEQUFDc0MsV0FBVTs7Ozs7OzRDQUFZOzs7Ozs7O2tEQUc5Qiw4REFBQ1M7d0NBQUVDLE1BQUs7d0NBQUlWLFdBQVU7OzBEQUNwQiw4REFBQ25DLG9JQUFPQTtnREFBQ21DLFdBQVU7Ozs7Ozs0Q0FBWTs7Ozs7OztrREFHakMsOERBQUNTO3dDQUFFQyxNQUFLO3dDQUFJVixXQUFVOzswREFDcEIsOERBQUNsQyxvSUFBTUE7Z0RBQUNrQyxXQUFVOzs7Ozs7NENBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT3BDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0U7NEJBQU9GLFdBQVU7OzhDQUNoQiw4REFBQ3hDLG9JQUFRQTtvQ0FBQ3dDLFdBQVU7Ozs7OztnQ0FBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU90Qyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDdkMscUlBQU1BO29DQUFDdUMsV0FBVTs7Ozs7OzhDQUNsQiw4REFBQ1c7b0NBQ0NDLE1BQUs7b0NBQ0xDLGFBQVk7b0NBQ1piLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1oQiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1pMLGNBQWNtQixNQUFNLEdBQUcsSUFDdEJuQixjQUFjVSxHQUFHLENBQUMsQ0FBQ3pCLHNCQUNqQiw4REFBQ21CO2dDQUVDSSxTQUFTLElBQU1qQyxpQkFBaUJVLE1BQU1ELEVBQUU7Z0NBQ3hDcUIsV0FBVyxnRUFFUHBCLE9BREZYLGtCQUFrQlcsTUFBTUQsRUFBRSxHQUFHLCtCQUErQixJQUM3RCxLQUE0QyxPQUF6Q0MsTUFBTVMsTUFBTSxHQUFHLGFBQWE7O2tEQUVoQyw4REFBQ1U7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDTztnREFBS1AsV0FBVyxXQUEwRSxPQUEvRHBCLE1BQU1TLE1BQU0sR0FBRyxnQ0FBZ0M7MERBQ3hFVCxNQUFNSyxJQUFJOzs7Ozs7MERBRWIsOERBQUNzQjtnREFBS1AsV0FBVTswREFBeUJwQixNQUFNUSxJQUFJOzs7Ozs7Ozs7Ozs7a0RBRXJELDhEQUFDVzt3Q0FBSUMsV0FBVyxnQkFBNkUsT0FBN0RwQixNQUFNUyxNQUFNLEdBQUcsOEJBQThCO2tEQUMxRVQsTUFBTU0sT0FBTzs7Ozs7O2tEQUVoQiw4REFBQ2E7d0NBQUlDLFdBQVU7a0RBQ1pwQixNQUFNTyxPQUFPOzs7Ozs7a0RBRWhCLDhEQUFDWTt3Q0FBSUMsV0FBVTs7NENBQ1pwQixNQUFNUyxNQUFNLGtCQUNYLDhEQUFDa0I7Z0RBQUtQLFdBQVU7Ozs7Ozs0Q0FFakJwQixNQUFNVSxPQUFPLGtCQUNaLDhEQUFDMUIsb0lBQUlBO2dEQUFDb0MsV0FBVTs7Ozs7Ozs7Ozs7OzsrQkF2QmZwQixNQUFNRCxFQUFFOzs7O3NEQTZCakIsOERBQUNvQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMxQyxvSUFBSUE7b0NBQUMwQyxXQUFVOzs7Ozs7OENBQ2hCLDhEQUFDZTtvQ0FBRWYsV0FBVTs4Q0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTy9CLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWi9CLDhCQUNDLDhEQUFDOEI7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ2dCO2dDQUFHaEIsV0FBVTsyQ0FDWGhDLGVBQUFBLE9BQU95QixJQUFJLENBQUN3QixDQUFBQSxJQUFLQSxFQUFFdEMsRUFBRSxLQUFLViw0QkFBMUJELG1DQUFBQSxhQUEwQ2tCLE9BQU87Ozs7OzswQ0FFcEQsOERBQUNhO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDs7a0VBQ0MsOERBQUNBO3dEQUFJQyxXQUFVO21FQUNaaEMsZ0JBQUFBLE9BQU95QixJQUFJLENBQUN3QixDQUFBQSxJQUFLQSxFQUFFdEMsRUFBRSxLQUFLViw0QkFBMUJELG9DQUFBQSxjQUEwQ2lCLElBQUk7Ozs7OztrRUFFakQsOERBQUNjO3dEQUFJQyxXQUFVO21FQUNaaEMsZ0JBQUFBLE9BQU95QixJQUFJLENBQUN3QixDQUFBQSxJQUFLQSxFQUFFdEMsRUFBRSxLQUFLViw0QkFBMUJELG9DQUFBQSxjQUEwQ29CLElBQUk7Ozs7Ozs7Ozs7OzswREFHbkQsOERBQUNXO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0U7d0RBQU9GLFdBQVU7a0VBQ2hCLDRFQUFDcEMsb0lBQUlBOzREQUFDb0MsV0FBVTs7Ozs7Ozs7Ozs7a0VBRWxCLDhEQUFDRTt3REFBT0YsV0FBVTtrRUFDaEIsNEVBQUNuQyxvSUFBT0E7NERBQUNtQyxXQUFVOzs7Ozs7Ozs7OztrRUFFckIsOERBQUNFO3dEQUFPRixXQUFVO2tFQUNoQiw0RUFBQ2xDLG9JQUFNQTs0REFBQ2tDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUl4Qiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDZTtnREFBRWYsV0FBVTswREFBZ0M7Ozs7OzswREFJN0MsOERBQUNlO2dEQUFFZixXQUFVOzBEQUFxQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt5Q0FTMUQsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUMxQyxvSUFBSUE7Z0NBQUMwQyxXQUFVOzs7Ozs7MENBQ2hCLDhEQUFDZTswQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9WNUMsNkJBQ0MsOERBQUM0QjtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNJO29DQUFHSixXQUFVOzhDQUF3Qjs7Ozs7OzhDQUN0Qyw4REFBQ0U7b0NBQ0NDLFNBQVMsSUFBTS9CLGVBQWU7b0NBQzlCNEIsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQUlILDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEOztzREFDQyw4REFBQ21COzRDQUFNbEIsV0FBVTtzREFBK0M7Ozs7OztzREFDaEUsOERBQUNXOzRDQUNDQyxNQUFLOzRDQUNMWixXQUFVOzRDQUNWYSxhQUFZOzs7Ozs7Ozs7Ozs7OENBR2hCLDhEQUFDZDs7c0RBQ0MsOERBQUNtQjs0Q0FBTWxCLFdBQVU7c0RBQStDOzs7Ozs7c0RBQ2hFLDhEQUFDVzs0Q0FDQ0MsTUFBSzs0Q0FDTFosV0FBVTs0Q0FDVmEsYUFBWTs7Ozs7Ozs7Ozs7OzhDQUdoQiw4REFBQ2Q7O3NEQUNDLDhEQUFDbUI7NENBQU1sQixXQUFVO3NEQUErQzs7Ozs7O3NEQUNoRSw4REFBQ21COzRDQUNDQyxNQUFNOzRDQUNOcEIsV0FBVTs0Q0FDVmEsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlsQiw4REFBQ2Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBT0YsV0FBVTs4Q0FBNEM7Ozs7Ozs4Q0FHOUQsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0U7NENBQ0NDLFNBQVMsSUFBTS9CLGVBQWU7NENBQzlCNEIsV0FBVTtzREFDWDs7Ozs7O3NEQUdELDhEQUFDRTs0Q0FBT0YsV0FBVTtzREFBZ0U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWxHO0dBL1d3QmpDO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IE1haWwsIFBsdXMsIFNldHRpbmdzLCBTZWFyY2gsIFNlbmQsIEluYm94LCBTdGFyLCBBcmNoaXZlLCBUcmFzaDIsIFgsIENoZWNrLCBBbGVydENpcmNsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuLy8g57G75Z6L5a6a5LmJXG5pbnRlcmZhY2UgRW1haWxBY2NvdW50IHtcbiAgaWQ6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIHByb3ZpZGVyOiAnZ21haWwnIHwgJ291dGxvb2snIHwgJzE2MycgfCAncXEnIHwgJ290aGVyJ1xuICBuYW1lOiBzdHJpbmdcbiAgaXNBY3RpdmU6IGJvb2xlYW5cbiAgdW5yZWFkQ291bnQ6IG51bWJlclxufVxuXG5pbnRlcmZhY2UgRW1haWwge1xuICBpZDogbnVtYmVyXG4gIGZyb206IHN0cmluZ1xuICBzdWJqZWN0OiBzdHJpbmdcbiAgcHJldmlldzogc3RyaW5nXG4gIHRpbWU6IHN0cmluZ1xuICB1bnJlYWQ6IGJvb2xlYW5cbiAgc3RhcnJlZDogYm9vbGVhblxuICBhY2NvdW50SWQ6IHN0cmluZ1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCBbc2VsZWN0ZWRFbWFpbCwgc2V0U2VsZWN0ZWRFbWFpbF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKVxuICBjb25zdCBbc2hvd0NvbXBvc2UsIHNldFNob3dDb21wb3NlXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd0FkZEFjY291bnQsIHNldFNob3dBZGRBY2NvdW50XSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbYWN0aXZlQWNjb3VudElkLCBzZXRBY3RpdmVBY2NvdW50SWRdID0gdXNlU3RhdGU8c3RyaW5nPignMScpXG5cbiAgLy8g6YKu566x6LSm5oi35pWw5o2uXG4gIGNvbnN0IFtlbWFpbEFjY291bnRzLCBzZXRFbWFpbEFjY291bnRzXSA9IHVzZVN0YXRlPEVtYWlsQWNjb3VudFtdPihbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIGVtYWlsOiAndXNlckBnbWFpbC5jb20nLFxuICAgICAgcHJvdmlkZXI6ICdnbWFpbCcsXG4gICAgICBuYW1lOiAn5Liq5Lq6R21haWwnLFxuICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICB1bnJlYWRDb3VudDogM1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICcyJyxcbiAgICAgIGVtYWlsOiAnd29ya0BvdXRsb29rLmNvbScsXG4gICAgICBwcm92aWRlcjogJ291dGxvb2snLFxuICAgICAgbmFtZTogJ+W3peS9nOmCrueusScsXG4gICAgICBpc0FjdGl2ZTogZmFsc2UsXG4gICAgICB1bnJlYWRDb3VudDogMVxuICAgIH1cbiAgXSlcblxuICAvLyDmqKHmi5/pgq7ku7bmlbDmja5cbiAgY29uc3QgZW1haWxzOiBFbWFpbFtdID0gW1xuICAgIHtcbiAgICAgIGlkOiAxLFxuICAgICAgZnJvbTogJ2pvaG5AZ21haWwuY29tJyxcbiAgICAgIHN1YmplY3Q6ICfpobnnm67ov5vluqbmm7TmlrAnLFxuICAgICAgcHJldmlldzogJ+WFs+S6juacrOWRqOmhueebrui/m+WxleeahOivpue7huaKpeWRii4uLicsXG4gICAgICB0aW1lOiAnMTA6MzAnLFxuICAgICAgdW5yZWFkOiB0cnVlLFxuICAgICAgc3RhcnJlZDogZmFsc2UsXG4gICAgICBhY2NvdW50SWQ6ICcxJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IDIsXG4gICAgICBmcm9tOiAnc3VwcG9ydEBjb21wYW55LmNvbScsXG4gICAgICBzdWJqZWN0OiAn57O757uf57u05oqk6YCa55+lJyxcbiAgICAgIHByZXZpZXc6ICfmiJHku6zlsIblnKjku4rmmZrov5vooYzns7vnu5/nu7TmiqQuLi4nLFxuICAgICAgdGltZTogJzA5OjE1JyxcbiAgICAgIHVucmVhZDogZmFsc2UsXG4gICAgICBzdGFycmVkOiB0cnVlLFxuICAgICAgYWNjb3VudElkOiAnMSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAzLFxuICAgICAgZnJvbTogJ25ld3NsZXR0ZXJAdGVjaC5jb20nLFxuICAgICAgc3ViamVjdDogJ+acrOWRqOaKgOacr+i1hOiurycsXG4gICAgICBwcmV2aWV3OiAn5pyA5paw55qE5oqA5pyv6LaL5Yq/5ZKM5byA5Y+R5bel5YW35o6o6I2QLi4uJyxcbiAgICAgIHRpbWU6ICfmmKjlpKknLFxuICAgICAgdW5yZWFkOiBmYWxzZSxcbiAgICAgIHN0YXJyZWQ6IGZhbHNlLFxuICAgICAgYWNjb3VudElkOiAnMSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiA0LFxuICAgICAgZnJvbTogJ2Jvc3NAY29tcGFueS5jb20nLFxuICAgICAgc3ViamVjdDogJ+S8muiuruWuieaOkicsXG4gICAgICBwcmV2aWV3OiAn5piO5aSp5LiL5Y2IMueCueW8gOS8muiuqOiuuuaWsOmhueebri4uLicsXG4gICAgICB0aW1lOiAnMTE6MjAnLFxuICAgICAgdW5yZWFkOiB0cnVlLFxuICAgICAgc3RhcnJlZDogZmFsc2UsXG4gICAgICBhY2NvdW50SWQ6ICcyJ1xuICAgIH1cbiAgXVxuXG4gIC8vIOiOt+WPluW9k+WJjea0u+i3g+i0puaIt1xuICBjb25zdCBhY3RpdmVBY2NvdW50ID0gZW1haWxBY2NvdW50cy5maW5kKGFjYyA9PiBhY2MuaWQgPT09IGFjdGl2ZUFjY291bnRJZClcblxuICAvLyDojrflj5blvZPliY3otKbmiLfnmoTpgq7ku7ZcbiAgY29uc3QgY3VycmVudEVtYWlscyA9IGVtYWlscy5maWx0ZXIoZW1haWwgPT4gZW1haWwuYWNjb3VudElkID09PSBhY3RpdmVBY2NvdW50SWQpXG5cbiAgLy8g6I635Y+W6YKu566x5o+Q5L6b5ZWG5Zu+5qCHXG4gIGNvbnN0IGdldFByb3ZpZGVySWNvbiA9IChwcm92aWRlcjogRW1haWxBY2NvdW50Wydwcm92aWRlciddKSA9PiB7XG4gICAgc3dpdGNoIChwcm92aWRlcikge1xuICAgICAgY2FzZSAnZ21haWwnOiByZXR1cm4gJ/Cfk6cnXG4gICAgICBjYXNlICdvdXRsb29rJzogcmV0dXJuICfwn5OuJ1xuICAgICAgY2FzZSAnMTYzJzogcmV0dXJuICfwn5OsJ1xuICAgICAgY2FzZSAncXEnOiByZXR1cm4gJ/Cfk60nXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ+Kcie+4jydcbiAgICB9XG4gIH1cblxuICAvLyDliIfmjaLpgq7nrrHotKbmiLdcbiAgY29uc3Qgc3dpdGNoQWNjb3VudCA9IChhY2NvdW50SWQ6IHN0cmluZykgPT4ge1xuICAgIHNldEFjdGl2ZUFjY291bnRJZChhY2NvdW50SWQpXG4gICAgc2V0U2VsZWN0ZWRFbWFpbChudWxsKSAvLyDmuIXpmaTpgInkuK3nmoTpgq7ku7ZcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICB7Lyog5L6n6L655qCPICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTY0IGJnLWdyYXktMTAwIGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIHsvKiDlpLTpg6ggKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWItNFwiPlxuICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMFwiPumCrueuseWuouaIt+errzwvaDE+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDb21wb3NlKHRydWUpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcHgtNCBweS0yIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgaG92ZXI6YmctYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAg5YaZ6YKu5Lu2XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDpgq7nrrHotKbmiLcgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS02MDAgbWItMlwiPumCrueusei0puaItzwvaDM+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTFcIj5cbiAgICAgICAgICAgIHtlbWFpbEFjY291bnRzLm1hcCgoYWNjb3VudCkgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXthY2NvdW50LmlkfVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHN3aXRjaEFjY291bnQoYWNjb3VudC5pZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQgdGV4dC1zbSBjdXJzb3ItcG9pbnRlciB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgYWNjb3VudC5pZCA9PT0gYWN0aXZlQWNjb3VudElkXG4gICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNTAgdGV4dC1ibHVlLTcwMCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAnXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Z2V0UHJvdmlkZXJJY29uKGFjY291bnQucHJvdmlkZXIpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57YWNjb3VudC5uYW1lfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktNzVcIj57YWNjb3VudC5lbWFpbH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICB7YWNjb3VudC51bnJlYWRDb3VudCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJiZy1yZWQtNTAwIHRleHQtd2hpdGUgdGV4dC14cyBweC0xLjUgcHktMC41IHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHthY2NvdW50LnVucmVhZENvdW50fVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93QWRkQWNjb3VudCh0cnVlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHAtMiB0ZXh0LWxlZnQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBib3JkZXIgYm9yZGVyLWRhc2hlZCBib3JkZXItZ3JheS0zMDBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAg5re75Yqg6YKu566xXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOWvvOiIquiPnOWNlSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0zXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtYi0xXCI+5b2T5YmN6LSm5oi3PC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICB7YWN0aXZlQWNjb3VudD8ubmFtZSB8fCAn5pyq6YCJ5oup6LSm5oi3J31cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwic3BhY2UteS0xXCI+XG4gICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHB4LTMgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtYmx1ZS02MDAgYmctYmx1ZS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIDxJbmJveCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAg5pS25Lu2566xXG4gICAgICAgICAgICAgIHthY3RpdmVBY2NvdW50Py51bnJlYWRDb3VudCAmJiBhY3RpdmVBY2NvdW50LnVucmVhZENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtYXV0byBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIHRleHQteHMgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAge2FjdGl2ZUFjY291bnQudW5yZWFkQ291bnR9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBweC0zIHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8U3RhciBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAg5bey5Yqg5pif5qCHXG4gICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHB4LTMgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS02MDAgaG92ZXI6YmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICDlt7Llj5HpgIFcbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgcHgtMyBweS0yIHRleHQtc20gdGV4dC1ncmF5LTYwMCBob3ZlcjpiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPEFyY2hpdmUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIOW9kuaho1xuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBweC0zIHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICDlnoPlnL7nrrFcbiAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICA8L25hdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOiuvue9riAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBweC0zIHB5LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIGhvdmVyOmJnLWdyYXktNTAgcm91bmRlZC1sZyB3LWZ1bGxcIj5cbiAgICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgIOiuvue9rlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog6YKu5Lu25YiX6KGoICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTk2IGJvcmRlci1yIGJvcmRlci1ncmF5LTIwMCBmbGV4IGZsZXgtY29sXCI+XG4gICAgICAgIHsvKiDmkJzntKLmoI8gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwidy00IGgtNCBhYnNvbHV0ZSBsZWZ0LTMgdG9wLTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS15LTEvMiB0ZXh0LWdyYXktNDAwXCIgLz5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5pCc57Si6YKu5Lu2Li4uXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHBsLTEwIHByLTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDpgq7ku7bliJfooaggKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG92ZXJmbG93LXktYXV0b1wiPlxuICAgICAgICAgIHtjdXJyZW50RW1haWxzLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICBjdXJyZW50RW1haWxzLm1hcCgoZW1haWwpID0+IChcbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGtleT17ZW1haWwuaWR9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRFbWFpbChlbWFpbC5pZCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTEwMCBjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1ncmF5LTUwICR7XG4gICAgICAgICAgICAgICAgICBzZWxlY3RlZEVtYWlsID09PSBlbWFpbC5pZCA/ICdiZy1ibHVlLTUwIGJvcmRlci1ibHVlLTIwMCcgOiAnJ1xuICAgICAgICAgICAgICAgIH0gJHtlbWFpbC51bnJlYWQgPyAnYmctd2hpdGUnIDogJ2JnLWdyYXktNTAnfWB9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gJHtlbWFpbC51bnJlYWQgPyAnZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwJyA6ICd0ZXh0LWdyYXktNzAwJ31gfT5cbiAgICAgICAgICAgICAgICAgICAge2VtYWlsLmZyb219XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDBcIj57ZW1haWwudGltZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2B0ZXh0LXNtIG1iLTEgJHtlbWFpbC51bnJlYWQgPyAnZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCcgOiAndGV4dC1ncmF5LTcwMCd9YH0+XG4gICAgICAgICAgICAgICAgICB7ZW1haWwuc3ViamVjdH1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBsaW5lLWNsYW1wLTJcIj5cbiAgICAgICAgICAgICAgICAgIHtlbWFpbC5wcmV2aWV3fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAge2VtYWlsLnVucmVhZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInctMiBoLTIgYmctYmx1ZS02MDAgcm91bmRlZC1mdWxsIG1yLTJcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAge2VtYWlsLnN0YXJyZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8U3RhciBjbGFzc05hbWU9XCJ3LTMgaC0zIHRleHQteWVsbG93LTUwMCBmaWxsLWN1cnJlbnRcIiAvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKVxuICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtZnVsbCB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cInctMTIgaC0xMiBtYi0yIHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+5q2k6LSm5oi35pqC5peg6YKu5Lu2PC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOmCruS7tuWGheWuueWMuuWfnyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAge3NlbGVjdGVkRW1haWwgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgcC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIHtlbWFpbHMuZmluZChlID0+IGUuaWQgPT09IHNlbGVjdGVkRW1haWwpPy5zdWJqZWN0fVxuICAgICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi00IHBiLTQgYm9yZGVyLWIgYm9yZGVyLWdyYXktMTAwXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7ZW1haWxzLmZpbmQoZSA9PiBlLmlkID09PSBzZWxlY3RlZEVtYWlsKT8uZnJvbX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2VtYWlscy5maW5kKGUgPT4gZS5pZCA9PT0gc2VsZWN0ZWRFbWFpbCk/LnRpbWV9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFN0YXIgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QXJjaGl2ZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9zZSBtYXgtdy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICDov5nph4zmmK/pgq7ku7bnmoTor6bnu4blhoXlrrnjgILlnKjlrp7pmYXlupTnlKjkuK3vvIzov5nph4zkvJrmmL7npLrlrozmlbTnmoTpgq7ku7bmraPmlofvvIxcbiAgICAgICAgICAgICAgICAgICAg5YyF5ousSFRNTOagvOW8j+OAgemZhOS7tuetieOAguebruWJjei/meWPquaYr+S4gOS4qua8lOekuueVjOmdouOAglxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBsZWFkaW5nLXJlbGF4ZWQgbXQtNFwiPlxuICAgICAgICAgICAgICAgICAgICDkvaDlj6/ku6XlnKjov5nph4znnIvliLDpgq7ku7bnmoTlrozmlbTlhoXlrrnvvIzljIXmi6zmoLzlvI/ljJbnmoTmlofmnKzjgIHlm77niYfjgIHpk77mjqXnrYnjgIJcbiAgICAgICAgICAgICAgICAgICAg55WM6Z2i6K6+6K6h566A5rSB5piO5LqG77yM5L6/5LqO6ZiF6K+75ZKM5pON5L2c44CCXG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TWFpbCBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byBtYi00IHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICA8cD7pgInmi6nkuIDlsIHpgq7ku7bmnaXmn6XnnIvlhoXlrrk8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog5YaZ6YKu5Lu25by556qXICovfVxuICAgICAge3Nob3dDb21wb3NlICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyB3LWZ1bGwgbWF4LXctMnhsIG14LTQgbWF4LWgtWzgwdmhdIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkXCI+5YaZ6YKu5Lu2PC9oMz5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q29tcG9zZShmYWxzZSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOKclVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+5pS25Lu25Lq6PC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLovpPlhaXmlLbku7bkurrpgq7nrrHlnLDlnYBcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPuS4u+mimDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1sZyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLpgq7ku7bkuLvpophcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPuWGheWuuTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICByb3dzPXs4fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6L6T5YWl6YKu5Lu25YaF5a65Li4uXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC00IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBob3Zlcjp0ZXh0LWdyYXktODAwXCI+XG4gICAgICAgICAgICAgICAg5re75Yqg6ZmE5Lu2XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIFxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NvbXBvc2UoZmFsc2UpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1ncmF5LTgwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNzAwXCI+XG4gICAgICAgICAgICAgICAgICDlj5HpgIFcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJNYWlsIiwiUGx1cyIsIlNldHRpbmdzIiwiU2VhcmNoIiwiU2VuZCIsIkluYm94IiwiU3RhciIsIkFyY2hpdmUiLCJUcmFzaDIiLCJIb21lIiwiZW1haWxzIiwic2VsZWN0ZWRFbWFpbCIsInNldFNlbGVjdGVkRW1haWwiLCJzaG93Q29tcG9zZSIsInNldFNob3dDb21wb3NlIiwic2hvd0FkZEFjY291bnQiLCJzZXRTaG93QWRkQWNjb3VudCIsImFjdGl2ZUFjY291bnRJZCIsInNldEFjdGl2ZUFjY291bnRJZCIsImVtYWlsQWNjb3VudHMiLCJzZXRFbWFpbEFjY291bnRzIiwiaWQiLCJlbWFpbCIsInByb3ZpZGVyIiwibmFtZSIsImlzQWN0aXZlIiwidW5yZWFkQ291bnQiLCJmcm9tIiwic3ViamVjdCIsInByZXZpZXciLCJ0aW1lIiwidW5yZWFkIiwic3RhcnJlZCIsImFjY291bnRJZCIsImFjdGl2ZUFjY291bnQiLCJmaW5kIiwiYWNjIiwiY3VycmVudEVtYWlscyIsImZpbHRlciIsImdldFByb3ZpZGVySWNvbiIsInN3aXRjaEFjY291bnQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoMyIsIm1hcCIsImFjY291bnQiLCJzcGFuIiwibmF2IiwiYSIsImhyZWYiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsImxlbmd0aCIsInAiLCJoMiIsImUiLCJsYWJlbCIsInRleHRhcmVhIiwicm93cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});