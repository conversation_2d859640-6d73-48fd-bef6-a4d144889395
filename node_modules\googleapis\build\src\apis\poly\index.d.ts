/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { poly_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof poly_v1.Poly;
};
export declare function poly(version: 'v1'): poly_v1.Poly;
export declare function poly(options: poly_v1.Options): poly_v1.Poly;
declare const auth: AuthPlus;
export { auth };
export { poly_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
