"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/google/route";
exports.ids = ["app/api/auth/google/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

module.exports = require("http2");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:buffer");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("process");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fgoogle%2Froute&page=%2Fapi%2Fauth%2Fgoogle%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgoogle%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fgoogle%2Froute&page=%2Fapi%2Fauth%2Fgoogle%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgoogle%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_cloud_email_src_app_api_auth_google_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/api/auth/google/route.ts */ \"(rsc)/./src/app/api/auth/google/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/google/route\",\n        pathname: \"/api/auth/google\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/google/route\"\n    },\n    resolvedPagePath: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\api\\\\auth\\\\google\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_cloud_email_src_app_api_auth_google_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/google/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fgoogle%2Froute&page=%2Fapi%2Fauth%2Fgoogle%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgoogle%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/google/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/auth/google/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_gmail_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/gmail-api */ \"(rsc)/./src/lib/gmail-api.ts\");\n\n\n// 获取Google OAuth授权URL\nasync function GET() {\n    try {\n        const gmailService = new _lib_gmail_api__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\n        const authUrl = gmailService.getAuthUrl();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            authUrl\n        });\n    } catch (error) {\n        console.error(\"Error generating auth URL:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to generate authorization URL\"\n        }, {\n            status: 500\n        });\n    }\n}\n// 处理OAuth回调\nasync function POST(request) {\n    try {\n        const { code } = await request.json();\n        if (!code) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Authorization code is required\"\n            }, {\n                status: 400\n            });\n        }\n        const gmailService = new _lib_gmail_api__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\n        const tokens = await gmailService.getAccessToken(code);\n        // 获取用户信息\n        const userProfile = await gmailService.getUserProfile();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            tokens,\n            userProfile,\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error handling OAuth callback:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Failed to authenticate with Google\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9hdXRoL2dvb2dsZS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVEO0FBQ2I7QUFFMUMsc0JBQXNCO0FBQ2YsZUFBZUU7SUFDcEIsSUFBSTtRQUNGLE1BQU1DLGVBQWUsSUFBSUYsc0RBQVlBO1FBQ3JDLE1BQU1HLFVBQVVELGFBQWFFLFVBQVU7UUFFdkMsT0FBT0wsa0ZBQVlBLENBQUNNLElBQUksQ0FBQztZQUFFRjtRQUFRO0lBQ3JDLEVBQUUsT0FBT0csT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxPQUFPUCxrRkFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXVDLEdBQ2hEO1lBQUVFLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsWUFBWTtBQUNMLGVBQWVDLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNLEVBQUVDLElBQUksRUFBRSxHQUFHLE1BQU1ELFFBQVFMLElBQUk7UUFFbkMsSUFBSSxDQUFDTSxNQUFNO1lBQ1QsT0FBT1osa0ZBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBaUMsR0FDMUM7Z0JBQUVFLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE1BQU1OLGVBQWUsSUFBSUYsc0RBQVlBO1FBQ3JDLE1BQU1ZLFNBQVMsTUFBTVYsYUFBYVcsY0FBYyxDQUFDRjtRQUVqRCxTQUFTO1FBQ1QsTUFBTUcsY0FBYyxNQUFNWixhQUFhYSxjQUFjO1FBRXJELE9BQU9oQixrRkFBWUEsQ0FBQ00sSUFBSSxDQUFDO1lBQ3ZCTztZQUNBRTtZQUNBRSxTQUFTO1FBQ1g7SUFDRixFQUFFLE9BQU9WLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBT1Asa0ZBQVlBLENBQUNNLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUFxQyxHQUM5QztZQUFFRSxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2VtYWlsLWNsaWVudC8uL3NyYy9hcHAvYXBpL2F1dGgvZ29vZ2xlL3JvdXRlLnRzP2Y2MTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IEdtYWlsU2VydmljZSBmcm9tICdAL2xpYi9nbWFpbC1hcGknXG5cbi8vIOiOt+WPlkdvb2dsZSBPQXV0aOaOiOadg1VSTFxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVCgpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBnbWFpbFNlcnZpY2UgPSBuZXcgR21haWxTZXJ2aWNlKClcbiAgICBjb25zdCBhdXRoVXJsID0gZ21haWxTZXJ2aWNlLmdldEF1dGhVcmwoKVxuICAgIFxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IGF1dGhVcmwgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZW5lcmF0aW5nIGF1dGggVVJMOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdGYWlsZWQgdG8gZ2VuZXJhdGUgYXV0aG9yaXphdGlvbiBVUkwnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuLy8g5aSE55CGT0F1dGjlm57osINcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBjb2RlIH0gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKVxuICAgIFxuICAgIGlmICghY29kZSkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnQXV0aG9yaXphdGlvbiBjb2RlIGlzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICBjb25zdCBnbWFpbFNlcnZpY2UgPSBuZXcgR21haWxTZXJ2aWNlKClcbiAgICBjb25zdCB0b2tlbnMgPSBhd2FpdCBnbWFpbFNlcnZpY2UuZ2V0QWNjZXNzVG9rZW4oY29kZSlcbiAgICBcbiAgICAvLyDojrflj5bnlKjmiLfkv6Hmga9cbiAgICBjb25zdCB1c2VyUHJvZmlsZSA9IGF3YWl0IGdtYWlsU2VydmljZS5nZXRVc2VyUHJvZmlsZSgpXG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHRva2VucyxcbiAgICAgIHVzZXJQcm9maWxlLFxuICAgICAgc3VjY2VzczogdHJ1ZVxuICAgIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaGFuZGxpbmcgT0F1dGggY2FsbGJhY2s6JywgZXJyb3IpXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ZhaWxlZCB0byBhdXRoZW50aWNhdGUgd2l0aCBHb29nbGUnIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJHbWFpbFNlcnZpY2UiLCJHRVQiLCJnbWFpbFNlcnZpY2UiLCJhdXRoVXJsIiwiZ2V0QXV0aFVybCIsImpzb24iLCJlcnJvciIsImNvbnNvbGUiLCJzdGF0dXMiLCJQT1NUIiwicmVxdWVzdCIsImNvZGUiLCJ0b2tlbnMiLCJnZXRBY2Nlc3NUb2tlbiIsInVzZXJQcm9maWxlIiwiZ2V0VXNlclByb2ZpbGUiLCJzdWNjZXNzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/google/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gmail-api.ts":
/*!******************************!*\
  !*** ./src/lib/gmail-api.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GmailService: () => (/* binding */ GmailService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var googleapis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! googleapis */ \"(rsc)/./node_modules/googleapis/build/src/index.js\");\n/* harmony import */ var google_auth_library__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\n\n\n// Gmail API 配置\nconst GMAIL_SCOPES = [\n    \"https://www.googleapis.com/auth/gmail.readonly\",\n    \"https://www.googleapis.com/auth/gmail.send\",\n    \"https://www.googleapis.com/auth/gmail.modify\"\n];\n// OAuth2 客户端配置\nconst oauth2Client = new google_auth_library__WEBPACK_IMPORTED_MODULE_0__.OAuth2Client(\"your_google_client_id_here.apps.googleusercontent.com\", process.env.GOOGLE_CLIENT_SECRET, \"http://localhost:3000/auth/google/callback\");\n// Gmail API 客户端\nconst gmail = googleapis__WEBPACK_IMPORTED_MODULE_1__.google.gmail({\n    version: \"v1\",\n    auth: oauth2Client\n});\nclass GmailService {\n    constructor(accessToken){\n        this.accessToken = null;\n        if (accessToken) {\n            this.setAccessToken(accessToken);\n        }\n    }\n    // 设置访问令牌\n    setAccessToken(token) {\n        this.accessToken = token;\n        oauth2Client.setCredentials({\n            access_token: token\n        });\n    }\n    // 获取授权URL\n    getAuthUrl() {\n        return oauth2Client.generateAuthUrl({\n            access_type: \"offline\",\n            scope: GMAIL_SCOPES,\n            prompt: \"consent\"\n        });\n    }\n    // 通过授权码获取访问令牌\n    async getAccessToken(code) {\n        try {\n            const { tokens } = await oauth2Client.getAccessToken(code);\n            if (tokens.access_token) {\n                this.setAccessToken(tokens.access_token);\n                return tokens;\n            }\n            throw new Error(\"No access token received\");\n        } catch (error) {\n            console.error(\"Error getting access token:\", error);\n            throw error;\n        }\n    }\n    // 获取用户信息\n    async getUserProfile() {\n        try {\n            const response = await gmail.users.getProfile({\n                userId: \"me\"\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Error getting user profile:\", error);\n            throw error;\n        }\n    }\n    // 获取邮件列表\n    async getMessages(maxResults = 20, query) {\n        try {\n            const listResponse = await gmail.users.messages.list({\n                userId: \"me\",\n                maxResults,\n                q: query\n            });\n            if (!listResponse.data.messages) {\n                return [];\n            }\n            // 获取每封邮件的详细信息\n            const messages = await Promise.all(listResponse.data.messages.map(async (message)=>{\n                const messageResponse = await gmail.users.messages.get({\n                    userId: \"me\",\n                    id: message.id,\n                    format: \"full\"\n                });\n                return messageResponse.data;\n            }));\n            return messages;\n        } catch (error) {\n            console.error(\"Error getting messages:\", error);\n            throw error;\n        }\n    }\n    // 解析邮件内容\n    parseEmail(message) {\n        const headers = message.payload.headers;\n        const getHeader = (name)=>headers.find((h)=>h.name.toLowerCase() === name.toLowerCase())?.value || \"\";\n        // 提取邮件正文\n        let body = \"\";\n        if (message.payload.body?.data) {\n            body = this.decodeBase64(message.payload.body.data);\n        } else if (message.payload.parts) {\n            // 查找文本部分\n            const textPart = message.payload.parts.find((part)=>part.mimeType === \"text/plain\" || part.mimeType === \"text/html\");\n            if (textPart?.body?.data) {\n                body = this.decodeBase64(textPart.body.data);\n            }\n        }\n        return {\n            id: message.id,\n            from: getHeader(\"From\"),\n            to: getHeader(\"To\"),\n            subject: getHeader(\"Subject\"),\n            date: new Date(parseInt(message.internalDate)).toISOString(),\n            body,\n            snippet: message.snippet,\n            isUnread: message.labelIds.includes(\"UNREAD\"),\n            isStarred: message.labelIds.includes(\"STARRED\"),\n            labels: message.labelIds\n        };\n    }\n    // Base64 解码\n    decodeBase64(data) {\n        try {\n            // Gmail API 使用 URL-safe base64\n            const base64 = data.replace(/-/g, \"+\").replace(/_/g, \"/\");\n            return Buffer.from(base64, \"base64\").toString(\"utf-8\");\n        } catch (error) {\n            console.error(\"Error decoding base64:\", error);\n            return \"\";\n        }\n    }\n    // 发送邮件\n    async sendEmail(to, subject, body, from) {\n        try {\n            const email = [\n                `To: ${to}`,\n                `Subject: ${subject}`,\n                from ? `From: ${from}` : \"\",\n                \"\",\n                body\n            ].join(\"\\n\");\n            const encodedEmail = Buffer.from(email).toString(\"base64\").replace(/\\+/g, \"-\").replace(/\\//g, \"_\").replace(/=+$/, \"\");\n            const response = await gmail.users.messages.send({\n                userId: \"me\",\n                requestBody: {\n                    raw: encodedEmail\n                }\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Error sending email:\", error);\n            throw error;\n        }\n    }\n    // 标记邮件为已读\n    async markAsRead(messageId) {\n        try {\n            await gmail.users.messages.modify({\n                userId: \"me\",\n                id: messageId,\n                requestBody: {\n                    removeLabelIds: [\n                        \"UNREAD\"\n                    ]\n                }\n            });\n        } catch (error) {\n            console.error(\"Error marking as read:\", error);\n            throw error;\n        }\n    }\n    // 添加/移除星标\n    async toggleStar(messageId, isStarred) {\n        try {\n            await gmail.users.messages.modify({\n                userId: \"me\",\n                id: messageId,\n                requestBody: {\n                    addLabelIds: isStarred ? [] : [\n                        \"STARRED\"\n                    ],\n                    removeLabelIds: isStarred ? [\n                        \"STARRED\"\n                    ] : []\n                }\n            });\n        } catch (error) {\n            console.error(\"Error toggling star:\", error);\n            throw error;\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GmailService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gmail-api.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/googleapis","vendor-chunks/google-auth-library","vendor-chunks/googleapis-common","vendor-chunks/math-intrinsics","vendor-chunks/gaxios","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/jws","vendor-chunks/call-bind-apply-helpers","vendor-chunks/json-bigint","vendor-chunks/google-logging-utils","vendor-chunks/get-proto","vendor-chunks/gcp-metadata","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/gtoken","vendor-chunks/url-template","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/safe-buffer","vendor-chunks/jwa","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/extend","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound","vendor-chunks/buffer-equal-constant-time","vendor-chunks/bignumber.js","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fgoogle%2Froute&page=%2Fapi%2Fauth%2Fgoogle%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fgoogle%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();