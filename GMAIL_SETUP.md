# Gmail API 设置指南

要使用Gmail功能，您需要设置Google OAuth2认证。以下是详细步骤：

## 1. 创建Google Cloud项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 点击"选择项目" → "新建项目"
3. 输入项目名称（例如：Email Client）
4. 点击"创建"

## 2. 启用Gmail API

1. 在项目中，转到"API和服务" → "库"
2. 搜索"Gmail API"
3. 点击"Gmail API"并点击"启用"

## 3. 创建OAuth2凭据

1. 转到"API和服务" → "凭据"
2. 点击"创建凭据" → "OAuth 2.0 客户端ID"
3. 如果提示配置OAuth同意屏幕，请先完成配置：
   - 选择"外部"用户类型
   - 填写应用名称、用户支持电子邮件等必填信息
   - 在"范围"步骤中，添加以下范围：
     - `https://www.googleapis.com/auth/gmail.readonly`
     - `https://www.googleapis.com/auth/gmail.send`
     - `https://www.googleapis.com/auth/gmail.modify`
   - 添加测试用户（您的Gmail地址）

4. 回到凭据页面，创建OAuth 2.0客户端ID：
   - 应用类型：Web应用
   - 名称：Email Client
   - 已获授权的重定向URI：
     - `http://localhost:3000/auth/google/callback`
     - `https://yourdomain.com/auth/google/callback` (生产环境)

## 4. 配置环境变量

1. 复制客户端ID和客户端密钥
2. 在项目根目录的 `.env.local` 文件中设置：

```env
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_client_id_here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_client_secret_here
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 5. 测试设置

1. 重启开发服务器：`npm run dev`
2. 在邮箱客户端中点击"添加邮箱"
3. 选择Gmail并点击"使用Google授权"
4. 应该会打开Google授权页面

## 6. 生产环境部署

部署到生产环境时：

1. 在Google Cloud Console中添加生产环境的重定向URI
2. 更新 `.env.local` 中的 `NEXT_PUBLIC_APP_URL`
3. 确保OAuth同意屏幕已发布（不再是测试模式）

## 常见问题

### 错误：redirect_uri_mismatch
- 确保重定向URI在Google Cloud Console中正确配置
- 检查URI是否完全匹配（包括协议、域名、端口、路径）

### 错误：access_denied
- 检查OAuth同意屏幕配置
- 确保用户在测试用户列表中（测试模式下）

### 错误：invalid_client
- 检查客户端ID和密钥是否正确
- 确保环境变量已正确设置

## 安全注意事项

1. **永远不要**将客户端密钥提交到版本控制系统
2. 在生产环境中使用HTTPS
3. 定期轮换客户端密钥
4. 限制OAuth范围到最小必需权限
5. 实施适当的访问令牌刷新机制

## 支持的功能

配置完成后，您的邮箱客户端将支持：

- ✅ Gmail账户OAuth2认证
- ✅ 读取邮件列表
- ✅ 查看邮件内容
- ✅ 发送邮件
- ✅ 标记邮件（已读、星标）
- ✅ 搜索邮件

## 下一步

完成Gmail设置后，您可以继续添加其他邮箱服务的支持：
- Microsoft Outlook (OAuth2)
- 163邮箱 (IMAP/SMTP)
- QQ邮箱 (IMAP/SMTP)
- 其他支持IMAP/SMTP的邮箱服务
