"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-map";
exports.ids = ["vendor-chunks/side-channel-map"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-map/index.js":
/*!************************************************!*\
  !*** ./node_modules/side-channel-map/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(rsc)/./node_modules/get-intrinsic/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(rsc)/./node_modules/call-bound/index.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar $Map = GetIntrinsic(\"%Map%\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => V} */ var $mapGet = callBound(\"Map.prototype.get\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K, value: V) => void} */ var $mapSet = callBound(\"Map.prototype.set\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */ var $mapHas = callBound(\"Map.prototype.has\", true);\n/** @type {<K, V>(thisArg: Map<K, V>, key: K) => boolean} */ var $mapDelete = callBound(\"Map.prototype.delete\", true);\n/** @type {<K, V>(thisArg: Map<K, V>) => number} */ var $mapSize = callBound(\"Map.prototype.size\", true);\n/** @type {import('.')} */ module.exports = !!$Map && /** @type {Exclude<import('.'), false>} */ function getSideChannelMap() {\n    /** @typedef {ReturnType<typeof getSideChannelMap>} Channel */ /** @typedef {Parameters<Channel['get']>[0]} K */ /** @typedef {Parameters<Channel['set']>[1]} V */ /** @type {Map<K, V> | undefined} */ var $m;\n    /** @type {Channel} */ var channel = {\n        assert: function(key) {\n            if (!channel.has(key)) {\n                throw new $TypeError(\"Side channel does not contain \" + inspect(key));\n            }\n        },\n        \"delete\": function(key) {\n            if ($m) {\n                var result = $mapDelete($m, key);\n                if ($mapSize($m) === 0) {\n                    $m = void undefined;\n                }\n                return result;\n            }\n            return false;\n        },\n        get: function(key) {\n            if ($m) {\n                return $mapGet($m, key);\n            }\n        },\n        has: function(key) {\n            if ($m) {\n                return $mapHas($m, key);\n            }\n            return false;\n        },\n        set: function(key, value) {\n            if (!$m) {\n                // @ts-expect-error TS can't handle narrowing a variable inside a closure\n                $m = new $Map();\n            }\n            $mapSet($m, key, value);\n        }\n    };\n    // @ts-expect-error TODO: figure out why TS is erroring here\n    return channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-map/index.js\n");

/***/ })

};
;