"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/extend";
exports.ids = ["vendor-chunks/extend"];
exports.modules = {

/***/ "(rsc)/./node_modules/extend/index.js":
/*!**************************************!*\
  !*** ./node_modules/extend/index.js ***!
  \**************************************/
/***/ ((module) => {

eval("\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\nvar isArray = function isArray(arr) {\n    if (typeof Array.isArray === \"function\") {\n        return Array.isArray(arr);\n    }\n    return toStr.call(arr) === \"[object Array]\";\n};\nvar isPlainObject = function isPlainObject(obj) {\n    if (!obj || toStr.call(obj) !== \"[object Object]\") {\n        return false;\n    }\n    var hasOwnConstructor = hasOwn.call(obj, \"constructor\");\n    var hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, \"isPrototypeOf\");\n    // Not own constructor property must be Object\n    if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n        return false;\n    }\n    // Own properties are enumerated firstly, so to speed up,\n    // if last one is own, then all properties are own.\n    var key;\n    for(key in obj){}\n    return typeof key === \"undefined\" || hasOwn.call(obj, key);\n};\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n    if (defineProperty && options.name === \"__proto__\") {\n        defineProperty(target, options.name, {\n            enumerable: true,\n            configurable: true,\n            value: options.newValue,\n            writable: true\n        });\n    } else {\n        target[options.name] = options.newValue;\n    }\n};\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n    if (name === \"__proto__\") {\n        if (!hasOwn.call(obj, name)) {\n            return void 0;\n        } else if (gOPD) {\n            // In early versions of node, obj['__proto__'] is buggy when obj has\n            // __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n            return gOPD(obj, name).value;\n        }\n    }\n    return obj[name];\n};\nmodule.exports = function extend() {\n    var options, name, src, copy, copyIsArray, clone;\n    var target = arguments[0];\n    var i = 1;\n    var length = arguments.length;\n    var deep = false;\n    // Handle a deep copy situation\n    if (typeof target === \"boolean\") {\n        deep = target;\n        target = arguments[1] || {};\n        // skip the boolean and the target\n        i = 2;\n    }\n    if (target == null || typeof target !== \"object\" && typeof target !== \"function\") {\n        target = {};\n    }\n    for(; i < length; ++i){\n        options = arguments[i];\n        // Only deal with non-null/undefined values\n        if (options != null) {\n            // Extend the base object\n            for(name in options){\n                src = getProperty(target, name);\n                copy = getProperty(options, name);\n                // Prevent never-ending loop\n                if (target !== copy) {\n                    // Recurse if we're merging plain objects or arrays\n                    if (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n                        if (copyIsArray) {\n                            copyIsArray = false;\n                            clone = src && isArray(src) ? src : [];\n                        } else {\n                            clone = src && isPlainObject(src) ? src : {};\n                        }\n                        // Never move original objects, clone them\n                        setProperty(target, {\n                            name: name,\n                            newValue: extend(deep, clone, copy)\n                        });\n                    // Don't bring in undefined values\n                    } else if (typeof copy !== \"undefined\") {\n                        setProperty(target, {\n                            name: name,\n                            newValue: copy\n                        });\n                    }\n                }\n            }\n        }\n    }\n    // Return the modified object\n    return target;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/extend/index.js\n");

/***/ })

};
;