/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth-test/page";
exports.ids = ["app/auth-test/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth-test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth-test/page.tsx */ \"(rsc)/./src/app/auth-test/page.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/auth-test/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth-test/page\",\n        pathname: \"/auth-test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cauth-test%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cauth-test%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth-test/page.tsx */ \"(ssr)/./src/app/auth-test/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2Nsb3VkJTVDZW1haWwlNUNzcmMlNUNhcHAlNUNhdXRoLXRlc3QlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbWFpbC1jbGllbnQvPzc0NzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXGF1dGgtdGVzdFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cauth-test%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/auth-test/page.tsx":
/*!************************************!*\
  !*** ./src/app/auth-test/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthTestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Loader2,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AuthTestPage() {\n    const [testData, setTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        imapHost: \"\",\n        imapPort: 993,\n        useSSL: true\n    });\n    const [testing, setTesting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const runAuthTest = async ()=>{\n        setTesting(true);\n        setResults([]);\n        const testConfigs = [\n            {\n                name: \"SSL + PLAIN认证\",\n                ssl: true,\n                port: 993,\n                auth: [\n                    \"PLAIN\"\n                ]\n            },\n            {\n                name: \"SSL + LOGIN认证\",\n                ssl: true,\n                port: 993,\n                auth: [\n                    \"LOGIN\"\n                ]\n            },\n            {\n                name: \"STARTTLS + PLAIN认证\",\n                ssl: false,\n                port: 143,\n                auth: [\n                    \"PLAIN\"\n                ]\n            },\n            {\n                name: \"STARTTLS + LOGIN认证\",\n                ssl: false,\n                port: 143,\n                auth: [\n                    \"LOGIN\"\n                ]\n            },\n            {\n                name: \"无加密 + PLAIN认证\",\n                ssl: false,\n                port: 143,\n                auth: [\n                    \"PLAIN\"\n                ]\n            }\n        ];\n        for (const config of testConfigs){\n            try {\n                const response = await fetch(\"/api/test-imap-auth\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        email: testData.email,\n                        password: testData.password,\n                        host: testData.imapHost,\n                        port: config.port,\n                        ssl: config.ssl,\n                        authMethods: config.auth\n                    })\n                });\n                const result = await response.json();\n                setResults((prev)=>[\n                        ...prev,\n                        {\n                            config: config.name,\n                            success: result.success,\n                            message: result.message || result.error,\n                            details: result.details\n                        }\n                    ]);\n            } catch (error) {\n                setResults((prev)=>[\n                        ...prev,\n                        {\n                            config: config.name,\n                            success: false,\n                            message: `测试失败: ${error}`,\n                            details: null\n                        }\n                    ]);\n            }\n            // 短暂延迟避免服务器压力\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n        }\n        setTesting(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"\\uD83D\\uDD27 IMAP认证方法测试工具\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"测试不同的IMAP认证配置，找到最适合的连接方式\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"邮箱地址\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: testData.email,\n                                        onChange: (e)=>setTestData((prev)=>({\n                                                    ...prev,\n                                                    email: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"密码\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: testData.password,\n                                        onChange: (e)=>setTestData((prev)=>({\n                                                    ...prev,\n                                                    password: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"密码或应用专用密码\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"IMAP服务器地址\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: testData.imapHost,\n                                        onChange: (e)=>setTestData((prev)=>({\n                                                    ...prev,\n                                                    imapHost: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"imap.example.com\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: runAuthTest,\n                                disabled: testing || !testData.email || !testData.password || !testData.imapHost,\n                                className: \"w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                children: testing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-4 h-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"正在测试认证方法...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"开始测试\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this),\n                    results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"测试结果\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this),\n                            results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `p-4 rounded-lg border ${result.success ? \"bg-green-50 border-green-200\" : \"bg-red-50 border-red-200\"}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start gap-3\",\n                                        children: [\n                                            result.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Loader2_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 mt-0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `font-medium ${result.success ? \"text-green-900\" : \"text-red-900\"}`,\n                                                        children: result.config\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `text-sm mt-1 ${result.success ? \"text-green-700\" : \"text-red-700\"}`,\n                                                        children: result.message\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    result.details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs mt-2 text-gray-600\",\n                                                        children: result.details\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, this)),\n                            results.some((r)=>r.success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-blue-900 mb-2\",\n                                        children: \"✅ 推荐配置\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-blue-800 text-sm\",\n                                        children: \"使用测试成功的配置添加邮箱账户。通常第一个成功的配置是最佳选择。\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 17\n                            }, this),\n                            results.length > 0 && !results.some((r)=>r.success) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-yellow-900 mb-2\",\n                                        children: \"⚠️ 所有测试都失败了\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-yellow-800 text-sm space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"可能的原因：\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 邮箱地址或密码错误\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• IMAP服务器地址不正确\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 邮箱未开启IMAP服务\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 需要使用应用专用密码\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"• 网络连接问题\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/\",\n                            className: \"inline-flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700\",\n                            children: \"返回邮箱客户端\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\auth-test\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth-test/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0229964a418d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9nbG9iYWxzLmNzcz9mOGNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDIyOTk2NGE0MThkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/auth-test/page.tsx":
/*!************************************!*\
  !*** ./src/app/auth-test/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\cloud\email\src\app\auth-test\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"邮箱客户端\",\n    description: \"支持多邮箱的网页版邮件客户端\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFDOUIsNEVBQUNVO2dCQUFJRCxXQUFVOzBCQUNaSjs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfpgq7nrrHlrqLmiLfnq68nLFxuICBkZXNjcmlwdGlvbjogJ+aUr+aMgeWkmumCrueuseeahOe9kemhteeJiOmCruS7tuWuouaIt+errycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fauth-test%2Fpage&page=%2Fauth-test%2Fpage&appPaths=%2Fauth-test%2Fpage&pagePath=private-next-app-dir%2Fauth-test%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();