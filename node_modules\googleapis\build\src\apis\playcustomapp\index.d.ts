/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { playcustomapp_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof playcustomapp_v1.Playcustomapp;
};
export declare function playcustomapp(version: 'v1'): playcustomapp_v1.Playcustomapp;
export declare function playcustomapp(options: playcustomapp_v1.Options): playcustomapp_v1.Playcustomapp;
declare const auth: AuthPlus;
export { auth };
export { playcustomapp_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
