'use client'

import { useState } from 'react'
import { AlertCircle, CheckCircle, ExternalLink, Copy, Eye, EyeOff } from 'lucide-react'

export default function TroubleshootPage() {
  const [selectedProvider, setSelectedProvider] = useState('')
  const [showPasswords, setShowPasswords] = useState(false)

  const providers = {
    gmail: {
      name: 'Gmail',
      steps: [
        '访问 Google 账户设置 (myaccount.google.com)',
        '点击左侧"安全性"',
        '确保已开启"两步验证"',
        '在"两步验证"下找到"应用专用密码"',
        '选择"邮件"和你的设备',
        '复制生成的16位密码',
        '在邮箱客户端中使用这个密码，不是Google账户密码'
      ],
      links: [
        { text: 'Google账户设置', url: 'https://myaccount.google.com/security' },
        { text: '应用专用密码帮助', url: 'https://support.google.com/accounts/answer/185833' }
      ],
      notes: '⚠️ 必须先开启两步验证才能生成应用专用密码'
    },
    qq: {
      name: 'QQ邮箱',
      steps: [
        '登录 QQ邮箱网页版 (mail.qq.com)',
        '点击右上角"设置"',
        '选择"账户"标签',
        '找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"',
        '开启"IMAP/SMTP服务"',
        '点击"生成授权码"',
        '按提示发送短信验证',
        '复制获得的授权码',
        '在邮箱客户端中使用授权码，不是QQ密码'
      ],
      links: [
        { text: 'QQ邮箱网页版', url: 'https://mail.qq.com' }
      ],
      notes: '⚠️ 授权码不是QQ密码，是专门为第三方客户端生成的'
    },
    '163': {
      name: '163邮箱',
      steps: [
        '登录 163邮箱网页版 (mail.163.com)',
        '点击右上角"设置"',
        '选择"POP3/SMTP/IMAP"',
        '开启"IMAP/SMTP服务"',
        '点击"客户端授权密码"',
        '设置一个新的授权密码',
        '记住这个密码',
        '在邮箱客户端中使用客户端授权密码，不是163登录密码'
      ],
      links: [
        { text: '163邮箱网页版', url: 'https://mail.163.com' }
      ],
      notes: '⚠️ 客户端授权密码是单独设置的，不是登录密码'
    },
    '126': {
      name: '126邮箱',
      steps: [
        '登录 126邮箱网页版 (mail.126.com)',
        '点击右上角"设置"',
        '选择"POP3/SMTP/IMAP"',
        '开启"IMAP/SMTP服务"',
        '点击"客户端授权密码"',
        '设置一个新的授权密码',
        '记住这个密码',
        '在邮箱客户端中使用客户端授权密码，不是126登录密码'
      ],
      links: [
        { text: '126邮箱网页版', url: 'https://mail.126.com' }
      ],
      notes: '⚠️ 客户端授权密码是单独设置的，不是登录密码'
    },
    outlook: {
      name: 'Outlook',
      steps: [
        '登录 Microsoft 账户 (account.microsoft.com)',
        '点击"安全性"',
        '选择"高级安全选项"',
        '在"应用密码"下点击"创建新的应用密码"',
        '选择"邮件"',
        '复制生成的密码',
        '在邮箱客户端中使用应用密码'
      ],
      links: [
        { text: 'Microsoft账户设置', url: 'https://account.microsoft.com/security' }
      ],
      notes: '💡 个人Outlook账户通常可以直接使用登录密码'
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              📧 邮箱认证问题解决
            </h1>
            <p className="text-gray-600">
              "Authentication failed" 错误的完整解决方案
            </p>
          </div>

          {/* 邮箱选择 */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">选择你的邮箱类型</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.entries(providers).map(([key, provider]) => (
                <button
                  key={key}
                  onClick={() => setSelectedProvider(key)}
                  className={`p-4 rounded-lg border-2 transition-colors ${
                    selectedProvider === key
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{provider.name}</div>
                </button>
              ))}
            </div>
          </div>

          {/* 解决步骤 */}
          {selectedProvider && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">
                  {providers[selectedProvider as keyof typeof providers].name} 认证设置步骤
                </h3>
                
                <div className="space-y-3">
                  {providers[selectedProvider as keyof typeof providers].steps.map((step, index) => (
                    <div key={index} className="flex items-start gap-3">
                      <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium flex-shrink-0">
                        {index + 1}
                      </div>
                      <div className="text-blue-800">{step}</div>
                    </div>
                  ))}
                </div>

                {/* 重要提示 */}
                <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
                    <div className="text-yellow-800 text-sm">
                      {providers[selectedProvider as keyof typeof providers].notes}
                    </div>
                  </div>
                </div>

                {/* 相关链接 */}
                <div className="mt-4">
                  <h4 className="font-medium text-blue-900 mb-2">相关链接：</h4>
                  <div className="space-y-2">
                    {providers[selectedProvider as keyof typeof providers].links.map((link, index) => (
                      <a
                        key={index}
                        href={link.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm"
                      >
                        <ExternalLink className="w-3 h-3" />
                        {link.text}
                      </a>
                    ))}
                  </div>
                </div>
              </div>

              {/* 常见错误 */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-red-900 mb-4">
                  ❌ 常见错误
                </h3>
                <div className="space-y-2 text-red-800">
                  <div>• 使用登录密码而不是应用专用密码/授权码</div>
                  <div>• 没有开启IMAP/SMTP服务</div>
                  <div>• 没有开启两步验证（Gmail需要）</div>
                  <div>• 服务器地址或端口配置错误</div>
                </div>
              </div>

              {/* 测试建议 */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-green-900 mb-4">
                  ✅ 测试建议
                </h3>
                <div className="space-y-2 text-green-800">
                  <div>• 先在邮箱网页版确认设置正确</div>
                  <div>• 复制粘贴密码，避免输入错误</div>
                  <div>• 确认网络能访问邮箱服务器</div>
                  <div>• 如果还是失败，尝试重新生成密码</div>
                </div>
              </div>
            </div>
          )}

          {/* 返回按钮 */}
          <div className="mt-8 text-center">
            <a
              href="/"
              className="inline-flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              返回邮箱客户端
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
