# 📧 简单邮箱客户端使用指南

## 🚀 一键启动

```bash
npm run dev:integrated
```

**就这么简单！** 一个命令启动完整的邮箱客户端，包含：
- Web界面 (http://localhost:3000)
- 实时邮件服务
- WebSocket通信

## 📱 如何添加邮箱

### 1. 点击"添加邮箱"按钮

### 2. 输入邮箱信息
- **邮箱地址**: 输入你的完整邮箱地址
- **系统会自动识别邮箱类型** (Gmail、QQ、163等)
- **账户名称**: 可选，系统会自动生成
- **密码**: 输入密码或应用专用密码

### 3. 不同邮箱的密码设置

#### 📧 Gmail用户
1. 开启Google账户的"两步验证"
2. 生成"应用专用密码"
3. 使用应用专用密码，不是登录密码

#### 📮 QQ邮箱用户
1. 登录QQ邮箱网页版
2. 设置 → 账户 → 开启IMAP/SMTP服务
3. 获取"授权码"
4. 使用授权码，不是QQ密码

#### 📬 163邮箱用户
1. 登录163邮箱网页版
2. 设置 → POP3/SMTP/IMAP → 开启IMAP/SMTP服务
3. 设置"客户端授权密码"
4. 使用客户端授权密码

#### 📭 Outlook用户
- 可以直接使用登录密码
- 或者设置应用专用密码（更安全）

### 4. 点击"测试连接并添加"
- 系统会自动测试连接
- 连接成功后开始实时监听
- 新邮件会立即显示

## ✨ 功能特性

### 🔄 实时邮件接收
- 24/7持续监听你的邮箱
- 新邮件立即出现在界面
- 桌面通知提醒

### 📊 状态指示器
- **绿色WiFi图标** = 服务在线
- **红色WiFi图标** = 服务离线
- **铃铛图标** = 通知状态

### 📧 邮件操作
- 查看邮件内容
- 发送新邮件
- 标记已读/星标
- 搜索邮件

## 🔧 常见问题

### Q: 连接失败怎么办？
A: 检查以下几点：
1. 邮箱地址是否正确
2. 是否使用了正确的密码类型
3. 是否开启了IMAP/SMTP服务
4. 网络连接是否正常

### Q: Gmail连接失败？
A: Gmail需要：
1. 开启两步验证
2. 生成应用专用密码
3. 使用应用专用密码而不是登录密码

### Q: QQ邮箱连接失败？
A: QQ邮箱需要：
1. 在网页版开启IMAP/SMTP服务
2. 获取授权码
3. 使用授权码而不是QQ密码

### Q: 没有收到桌面通知？
A: 
1. 点击铃铛图标允许通知权限
2. 检查浏览器通知设置
3. 检查系统通知设置

## 🎯 使用技巧

1. **保持浏览器标签页开启** - 这样才能持续接收邮件
2. **允许通知权限** - 第一次使用时点击允许
3. **使用应用专用密码** - 更安全，推荐使用
4. **多账户支持** - 可以同时添加多个邮箱

## 🔐 安全说明

- ✅ 所有密码仅在本地存储
- ✅ 不会上传到任何服务器
- ✅ 使用SSL/TLS加密连接
- ✅ 建议使用应用专用密码

## 📞 获取帮助

如果遇到问题：
1. 检查浏览器控制台的错误信息
2. 确认邮箱服务设置正确
3. 验证网络连接
4. 重启服务器: `Ctrl+C` 然后重新运行 `npm run dev:integrated`

---

**享受你的实时邮箱体验！** 🎉
