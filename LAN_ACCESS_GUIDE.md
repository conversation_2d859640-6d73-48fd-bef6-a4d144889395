# 🌐 局域网访问配置指南

## 🚀 启动局域网服务

```bash
npm run dev:integrated
```

服务器会自动显示局域网访问地址。

## 📱 访问方式

### 本机访问
```
http://localhost:3000
```

### 局域网访问
```
http://[你的IP地址]:3000
例如: http://*************:3000
```

## 🔍 查看网络信息

```bash
npm run network-info
```

这个命令会显示：
- 主机名和系统信息
- 所有网络接口的IP地址
- 局域网访问链接

## 📋 设备访问指南

### 🖥️ 电脑访问
1. 确保电脑和服务器在同一局域网
2. 打开浏览器
3. 输入局域网地址：`http://[服务器IP]:3000`

### 📱 手机访问
1. 连接到同一WiFi网络
2. 打开手机浏览器
3. 输入局域网地址：`http://[服务器IP]:3000`
4. 可以添加到主屏幕作为Web应用

### 💻 平板访问
- 与手机访问方式相同
- 大屏幕体验更佳

## 🔧 网络配置

### 防火墙设置
如果无法访问，可能需要配置防火墙：

#### Windows防火墙
1. 打开"Windows Defender 防火墙"
2. 点击"允许应用或功能通过Windows Defender防火墙"
3. 点击"更改设置"
4. 添加端口3000的入站规则

#### 或者临时关闭防火墙测试
```powershell
# 管理员权限运行PowerShell
netsh advfirewall set allprofiles state off
```

### 路由器设置
- 确保路由器没有启用AP隔离
- 检查是否有访客网络限制

## 🌐 常见网络场景

### 家庭网络
```
路由器: ***********
服务器: *************:3000
手机: ***********01 → 访问 http://*************:3000
电脑: ***********02 → 访问 http://*************:3000
```

### 办公网络
```
交换机: ***********
服务器: ************:3000
同事电脑: ************ → 访问 http://************:3000
```

### 热点网络
```
手机热点: ************
服务器: **************:3000
其他设备: ************01 → 访问 http://**************:3000
```

## 🔐 安全考虑

### 局域网安全
- ✅ 仅局域网内可访问，相对安全
- ✅ 邮箱密码仅在本地存储
- ✅ 不会暴露到公网

### 访问控制
- 考虑设置访问密码（可选）
- 定期检查连接的设备
- 不要在不信任的网络中使用

## 🛠️ 故障排除

### 无法访问
1. **检查IP地址**
   ```bash
   npm run network-info
   ```

2. **检查防火墙**
   - Windows: 允许端口3000
   - macOS: 系统偏好设置 → 安全性与隐私 → 防火墙

3. **检查网络连接**
   ```bash
   ping [服务器IP]
   ```

4. **检查端口占用**
   ```bash
   netstat -ano | findstr :3000
   ```

### 连接不稳定
- 检查WiFi信号强度
- 确保路由器性能足够
- 避免网络拥堵时段

### 功能异常
- 确保WebSocket连接正常
- 检查浏览器控制台错误
- 尝试刷新页面

## 📊 性能优化

### 服务器端
- 确保服务器有足够内存
- 避免同时连接过多设备
- 定期重启服务释放资源

### 客户端
- 使用现代浏览器
- 清理浏览器缓存
- 关闭不必要的标签页

## 🎯 使用场景

### 家庭使用
- 在客厅电视上查看邮件
- 手机随时接收邮件通知
- 多设备同步邮件状态

### 办公使用
- 团队共享邮件客户端
- 会议室大屏显示邮件
- 移动办公设备访问

### 临时使用
- 朋友借用查看邮件
- 出差时用酒店电脑
- 网吧等公共场所

## 💡 高级技巧

### 创建桌面快捷方式
1. 访问局域网地址
2. 浏览器菜单 → "添加到主屏幕"
3. 创建桌面图标

### 设置书签
```
名称: 邮箱客户端
地址: http://[服务器IP]:3000
```

### 二维码分享
使用在线二维码生成器创建访问链接的二维码，方便其他设备扫码访问。

---

**享受你的局域网邮箱客户端！** 🎉
