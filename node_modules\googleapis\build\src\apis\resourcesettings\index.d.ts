/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { resourcesettings_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof resourcesettings_v1.Resourcesettings;
};
export declare function resourcesettings(version: 'v1'): resourcesettings_v1.Resourcesettings;
export declare function resourcesettings(options: resourcesettings_v1.Options): resourcesettings_v1.Resourcesettings;
declare const auth: AuthPlus;
export { auth };
export { resourcesettings_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
