// 前端Gmail API客户端
export interface GmailAccount {
  id: string
  email: string
  name: string
  accessToken: string
  refreshToken?: string
  expiresAt?: number
}

export interface EmailMessage {
  id: string
  from: string
  to: string
  subject: string
  date: string
  body: string
  snippet: string
  isUnread: boolean
  isStarred: boolean
  labels: string[]
}

export class GmailClient {
  private baseUrl: string

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
  }

  // 获取Google OAuth授权URL
  async getAuthUrl(): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/google`)
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to get auth URL')
      }
      
      return data.authUrl
    } catch (error) {
      console.error('Error getting auth URL:', error)
      throw error
    }
  }

  // 处理OAuth回调
  async handleAuthCallback(code: string): Promise<{
    tokens: any
    userProfile: any
    success: boolean
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/google`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Authentication failed')
      }
      
      return data
    } catch (error) {
      console.error('Error handling auth callback:', error)
      throw error
    }
  }

  // 获取邮件列表
  async getMessages(
    accessToken: string,
    maxResults: number = 20,
    query?: string
  ): Promise<EmailMessage[]> {
    try {
      const url = new URL(`${this.baseUrl}/api/gmail/messages`)
      url.searchParams.set('maxResults', maxResults.toString())
      if (query) {
        url.searchParams.set('query', query)
      }

      const response = await fetch(url.toString(), {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch messages')
      }
      
      return data.emails
    } catch (error) {
      console.error('Error fetching messages:', error)
      throw error
    }
  }

  // 发送邮件
  async sendMessage(
    accessToken: string,
    to: string,
    subject: string,
    body: string,
    from?: string
  ): Promise<{ success: boolean; messageId: string }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/gmail/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({ to, subject, body, from })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message')
      }
      
      return data
    } catch (error) {
      console.error('Error sending message:', error)
      throw error
    }
  }

  // 标记邮件为已读
  async markAsRead(accessToken: string, messageId: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({ action: 'markAsRead' })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to mark as read')
      }
      
      return data.success
    } catch (error) {
      console.error('Error marking as read:', error)
      throw error
    }
  }

  // 切换星标
  async toggleStar(
    accessToken: string,
    messageId: string,
    isStarred: boolean
  ): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({ action: 'toggleStar', value: isStarred })
      })
      
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to toggle star')
      }
      
      return data.success
    } catch (error) {
      console.error('Error toggling star:', error)
      throw error
    }
  }

  // 启动OAuth流程
  startOAuthFlow(): void {
    this.getAuthUrl().then(authUrl => {
      // 在新窗口中打开授权页面
      const popup = window.open(
        authUrl,
        'gmail-auth',
        'width=500,height=600,scrollbars=yes,resizable=yes'
      )

      // 监听授权完成
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed)
          // 可以在这里触发刷新或重新获取数据
          window.location.reload()
        }
      }, 1000)
    }).catch(error => {
      console.error('Failed to start OAuth flow:', error)
      alert('无法启动Gmail授权流程，请稍后重试')
    })
  }
}

export default GmailClient
