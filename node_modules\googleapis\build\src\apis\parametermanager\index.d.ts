/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { parametermanager_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof parametermanager_v1.Parametermanager;
};
export declare function parametermanager(version: 'v1'): parametermanager_v1.Parametermanager;
export declare function parametermanager(options: parametermanager_v1.Options): parametermanager_v1.Parametermanager;
declare const auth: AuthPlus;
export { auth };
export { parametermanager_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
