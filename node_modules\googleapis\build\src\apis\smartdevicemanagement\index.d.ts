/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { smartdevicemanagement_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof smartdevicemanagement_v1.Smartdevicemanagement;
};
export declare function smartdevicemanagement(version: 'v1'): smartdevicemanagement_v1.Smartdevicemanagement;
export declare function smartdevicemanagement(options: smartdevicemanagement_v1.Options): smartdevicemanagement_v1.Smartdevicemanagement;
declare const auth: AuthPlus;
export { auth };
export { smartdevicemanagement_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
