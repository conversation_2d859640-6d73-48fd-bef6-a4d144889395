// 简单的邮件服务 - 支持IMAP/SMTP直连
import nodemailer from 'nodemailer'

export interface EmailProvider {
  name: string
  imap: {
    host: string
    port: number
    secure: boolean
  }
  smtp: {
    host: string
    port: number
    secure: boolean
  }
}

// 预定义的邮件服务商配置
export const EMAIL_PROVIDERS: Record<string, EmailProvider> = {
  gmail: {
    name: 'Gmail',
    imap: { host: 'imap.gmail.com', port: 993, secure: true },
    smtp: { host: 'smtp.gmail.com', port: 587, secure: false }
  },
  outlook: {
    name: 'Outlook',
    imap: { host: 'outlook.office365.com', port: 993, secure: true },
    smtp: { host: 'smtp-mail.outlook.com', port: 587, secure: false }
  },
  '163': {
    name: '163邮箱',
    imap: { host: 'imap.163.com', port: 993, secure: true },
    smtp: { host: 'smtp.163.com', port: 465, secure: true }
  },
  qq: {
    name: 'QQ邮箱',
    imap: { host: 'imap.qq.com', port: 993, secure: true },
    smtp: { host: 'smtp.qq.com', port: 587, secure: false }
  },
  '126': {
    name: '126邮箱',
    imap: { host: 'imap.126.com', port: 993, secure: true },
    smtp: { host: 'smtp.126.com', port: 465, secure: true }
  },
  'sina': {
    name: '新浪邮箱',
    imap: { host: 'imap.sina.com', port: 993, secure: true },
    smtp: { host: 'smtp.sina.com', port: 587, secure: false }
  }
}

export interface SimpleEmail {
  id: string
  from: string
  to: string
  subject: string
  date: string
  body: string
  isUnread: boolean
  isStarred: boolean
}

export interface EmailAccount {
  id: string
  email: string
  password: string
  provider: string
  name: string
}

export class SimpleEmailService {
  private account: EmailAccount
  private provider: EmailProvider

  constructor(account: EmailAccount) {
    this.account = account
    this.provider = EMAIL_PROVIDERS[account.provider]
    if (!this.provider) {
      throw new Error(`不支持的邮件服务商: ${account.provider}`)
    }
  }

  // 测试连接
  async testConnection(): Promise<boolean> {
    try {
      const transporter = nodemailer.createTransporter({
        host: this.provider.smtp.host,
        port: this.provider.smtp.port,
        secure: this.provider.smtp.secure,
        auth: {
          user: this.account.email,
          pass: this.account.password
        }
      })

      await transporter.verify()
      return true
    } catch (error) {
      console.error('连接测试失败:', error)
      return false
    }
  }

  // 发送邮件
  async sendEmail(to: string, subject: string, body: string): Promise<boolean> {
    try {
      const transporter = nodemailer.createTransporter({
        host: this.provider.smtp.host,
        port: this.provider.smtp.port,
        secure: this.provider.smtp.secure,
        auth: {
          user: this.account.email,
          pass: this.account.password
        }
      })

      const mailOptions = {
        from: this.account.email,
        to: to,
        subject: subject,
        text: body,
        html: body.replace(/\n/g, '<br>')
      }

      await transporter.sendMail(mailOptions)
      return true
    } catch (error) {
      console.error('发送邮件失败:', error)
      throw error
    }
  }

  // 获取邮件列表 (模拟实现)
  async getEmails(): Promise<SimpleEmail[]> {
    // 注意：真实的IMAP实现需要在服务器端进行
    // 这里返回模拟数据，实际项目中需要通过API调用服务器端的IMAP服务
    
    const mockEmails: SimpleEmail[] = [
      {
        id: '1',
        from: '<EMAIL>',
        to: this.account.email,
        subject: '欢迎使用邮箱客户端',
        date: new Date().toISOString(),
        body: '这是一封测试邮件，用于演示邮箱客户端的功能。',
        isUnread: true,
        isStarred: false
      },
      {
        id: '2',
        from: '<EMAIL>',
        to: this.account.email,
        subject: '账户验证成功',
        date: new Date(Date.now() - 3600000).toISOString(),
        body: '您的邮箱账户已成功验证并添加到邮箱客户端中。',
        isUnread: false,
        isStarred: true
      }
    ]

    return mockEmails
  }
}

// 验证邮箱账户
export async function validateEmailAccount(
  email: string, 
  password: string, 
  provider: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const account: EmailAccount = {
      id: Date.now().toString(),
      email,
      password,
      provider,
      name: `${EMAIL_PROVIDERS[provider]?.name || provider} - ${email}`
    }

    const service = new SimpleEmailService(account)
    const isValid = await service.testConnection()

    if (isValid) {
      return { success: true }
    } else {
      return { 
        success: false, 
        error: '连接失败，请检查邮箱地址和密码是否正确' 
      }
    }
  } catch (error: any) {
    return { 
      success: false, 
      error: error.message || '验证失败，请重试' 
    }
  }
}

// 获取邮件服务商建议
export function getProviderSuggestion(email: string): string {
  const domain = email.split('@')[1]?.toLowerCase()
  
  const domainMap: Record<string, string> = {
    'gmail.com': 'gmail',
    'googlemail.com': 'gmail',
    'outlook.com': 'outlook',
    'hotmail.com': 'outlook',
    'live.com': 'outlook',
    '163.com': '163',
    '126.com': '126',
    'qq.com': 'qq',
    'sina.com': 'sina',
    'sina.cn': 'sina'
  }

  return domainMap[domain] || 'other'
}

export default SimpleEmailService
