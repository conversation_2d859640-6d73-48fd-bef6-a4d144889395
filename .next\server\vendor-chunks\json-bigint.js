"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/json-bigint";
exports.ids = ["vendor-chunks/json-bigint"];
exports.modules = {

/***/ "(rsc)/./node_modules/json-bigint/index.js":
/*!*******************************************!*\
  !*** ./node_modules/json-bigint/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar json_stringify = (__webpack_require__(/*! ./lib/stringify.js */ \"(rsc)/./node_modules/json-bigint/lib/stringify.js\").stringify);\nvar json_parse = __webpack_require__(/*! ./lib/parse.js */ \"(rsc)/./node_modules/json-bigint/lib/parse.js\");\nmodule.exports = function(options) {\n    return {\n        parse: json_parse(options),\n        stringify: json_stringify\n    };\n};\n//create the default method members with no options applied for backwards compatibility\nmodule.exports.parse = json_parse();\nmodule.exports.stringify = json_stringify;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbi1iaWdpbnQvaW5kZXguanMiLCJtYXBwaW5ncyI6IjtBQUFBLElBQUlBLGlCQUFpQkMsOEdBQXVDO0FBQzVELElBQUlFLGFBQWlCRixtQkFBT0EsQ0FBQztBQUU3QkcsT0FBT0MsT0FBTyxHQUFHLFNBQVNDLE9BQU87SUFDN0IsT0FBUTtRQUNKQyxPQUFPSixXQUFXRztRQUNsQkosV0FBV0Y7SUFDZjtBQUNKO0FBQ0EsdUZBQXVGO0FBQ3ZGSSxvQkFBb0IsR0FBR0Q7QUFDdkJDLHdCQUF3QixHQUFHSiIsInNvdXJjZXMiOlsid2VicGFjazovL2VtYWlsLWNsaWVudC8uL25vZGVfbW9kdWxlcy9qc29uLWJpZ2ludC9pbmRleC5qcz9jZDhjIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBqc29uX3N0cmluZ2lmeSA9IHJlcXVpcmUoJy4vbGliL3N0cmluZ2lmeS5qcycpLnN0cmluZ2lmeTtcbnZhciBqc29uX3BhcnNlICAgICA9IHJlcXVpcmUoJy4vbGliL3BhcnNlLmpzJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24ob3B0aW9ucykge1xuICAgIHJldHVybiAge1xuICAgICAgICBwYXJzZToganNvbl9wYXJzZShvcHRpb25zKSxcbiAgICAgICAgc3RyaW5naWZ5OiBqc29uX3N0cmluZ2lmeVxuICAgIH1cbn07XG4vL2NyZWF0ZSB0aGUgZGVmYXVsdCBtZXRob2QgbWVtYmVycyB3aXRoIG5vIG9wdGlvbnMgYXBwbGllZCBmb3IgYmFja3dhcmRzIGNvbXBhdGliaWxpdHlcbm1vZHVsZS5leHBvcnRzLnBhcnNlID0ganNvbl9wYXJzZSgpO1xubW9kdWxlLmV4cG9ydHMuc3RyaW5naWZ5ID0ganNvbl9zdHJpbmdpZnk7XG4iXSwibmFtZXMiOlsianNvbl9zdHJpbmdpZnkiLCJyZXF1aXJlIiwic3RyaW5naWZ5IiwianNvbl9wYXJzZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJvcHRpb25zIiwicGFyc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/json-bigint/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/json-bigint/lib/parse.js":
/*!***********************************************!*\
  !*** ./node_modules/json-bigint/lib/parse.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar BigNumber = null;\n// regexpxs extracted from\n// (c) BSD-3-Clause\n// https://github.com/fastify/secure-json-parse/graphs/contributors and https://github.com/hapijs/bourne/graphs/contributors\nconst suspectProtoRx = /(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])/;\nconst suspectConstructorRx = /(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)/;\n/*\n    json_parse.js\n    2012-06-20\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    This file creates a json_parse function.\n    During create you can (optionally) specify some behavioural switches\n\n        require('json-bigint')(options)\n\n            The optional options parameter holds switches that drive certain\n            aspects of the parsing process:\n            * options.strict = true will warn about duplicate-key usage in the json.\n              The default (strict = false) will silently ignore those and overwrite\n              values for keys that are in duplicate use.\n\n    The resulting function follows this signature:\n        json_parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = json_parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n*/ /*members \"\", \"\\\"\", \"\\/\", \"\\\\\", at, b, call, charAt, f, fromCharCode,\n    hasOwnProperty, message, n, name, prototype, push, r, t, text\n*/ var json_parse = function(options) {\n    \"use strict\";\n    // This is a function that can parse a JSON text, producing a JavaScript\n    // data structure. It is a simple, recursive descent parser. It does not use\n    // eval or regular expressions, so it can be used as a model for implementing\n    // a JSON parser in other languages.\n    // We are defining the function inside of another function to avoid creating\n    // global variables.\n    // Default options one can override by passing options to the parse()\n    var _options = {\n        strict: false,\n        storeAsString: false,\n        alwaysParseAsBig: false,\n        useNativeBigInt: false,\n        protoAction: \"error\",\n        constructorAction: \"error\"\n    };\n    // If there are options, then use them to override the default _options\n    if (options !== undefined && options !== null) {\n        if (options.strict === true) {\n            _options.strict = true;\n        }\n        if (options.storeAsString === true) {\n            _options.storeAsString = true;\n        }\n        _options.alwaysParseAsBig = options.alwaysParseAsBig === true ? options.alwaysParseAsBig : false;\n        _options.useNativeBigInt = options.useNativeBigInt === true ? options.useNativeBigInt : false;\n        if (typeof options.constructorAction !== \"undefined\") {\n            if (options.constructorAction === \"error\" || options.constructorAction === \"ignore\" || options.constructorAction === \"preserve\") {\n                _options.constructorAction = options.constructorAction;\n            } else {\n                throw new Error(`Incorrect value for constructorAction option, must be \"error\", \"ignore\" or undefined but passed ${options.constructorAction}`);\n            }\n        }\n        if (typeof options.protoAction !== \"undefined\") {\n            if (options.protoAction === \"error\" || options.protoAction === \"ignore\" || options.protoAction === \"preserve\") {\n                _options.protoAction = options.protoAction;\n            } else {\n                throw new Error(`Incorrect value for protoAction option, must be \"error\", \"ignore\" or undefined but passed ${options.protoAction}`);\n            }\n        }\n    }\n    var at, ch, escapee = {\n        '\"': '\"',\n        \"\\\\\": \"\\\\\",\n        \"/\": \"/\",\n        b: \"\\b\",\n        f: \"\\f\",\n        n: \"\\n\",\n        r: \"\\r\",\n        t: \"\t\"\n    }, text, error = function(m) {\n        // Call error when something is wrong.\n        throw {\n            name: \"SyntaxError\",\n            message: m,\n            at: at,\n            text: text\n        };\n    }, next = function(c) {\n        // If a c parameter is provided, verify that it matches the current character.\n        if (c && c !== ch) {\n            error(\"Expected '\" + c + \"' instead of '\" + ch + \"'\");\n        }\n        // Get the next character. When there are no more characters,\n        // return the empty string.\n        ch = text.charAt(at);\n        at += 1;\n        return ch;\n    }, number = function() {\n        // Parse a number value.\n        var number, string = \"\";\n        if (ch === \"-\") {\n            string = \"-\";\n            next(\"-\");\n        }\n        while(ch >= \"0\" && ch <= \"9\"){\n            string += ch;\n            next();\n        }\n        if (ch === \".\") {\n            string += \".\";\n            while(next() && ch >= \"0\" && ch <= \"9\"){\n                string += ch;\n            }\n        }\n        if (ch === \"e\" || ch === \"E\") {\n            string += ch;\n            next();\n            if (ch === \"-\" || ch === \"+\") {\n                string += ch;\n                next();\n            }\n            while(ch >= \"0\" && ch <= \"9\"){\n                string += ch;\n                next();\n            }\n        }\n        number = +string;\n        if (!isFinite(number)) {\n            error(\"Bad number\");\n        } else {\n            if (BigNumber == null) BigNumber = __webpack_require__(/*! bignumber.js */ \"(rsc)/./node_modules/bignumber.js/bignumber.js\");\n            //if (number > 9007199254740992 || number < -9007199254740992)\n            // Bignumber has stricter check: everything with length > 15 digits disallowed\n            if (string.length > 15) return _options.storeAsString ? string : _options.useNativeBigInt ? BigInt(string) : new BigNumber(string);\n            else return !_options.alwaysParseAsBig ? number : _options.useNativeBigInt ? BigInt(number) : new BigNumber(number);\n        }\n    }, string = function() {\n        // Parse a string value.\n        var hex, i, string = \"\", uffff;\n        // When parsing for string values, we must look for \" and \\ characters.\n        if (ch === '\"') {\n            var startAt = at;\n            while(next()){\n                if (ch === '\"') {\n                    if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n                    next();\n                    return string;\n                }\n                if (ch === \"\\\\\") {\n                    if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n                    next();\n                    if (ch === \"u\") {\n                        uffff = 0;\n                        for(i = 0; i < 4; i += 1){\n                            hex = parseInt(next(), 16);\n                            if (!isFinite(hex)) {\n                                break;\n                            }\n                            uffff = uffff * 16 + hex;\n                        }\n                        string += String.fromCharCode(uffff);\n                    } else if (typeof escapee[ch] === \"string\") {\n                        string += escapee[ch];\n                    } else {\n                        break;\n                    }\n                    startAt = at;\n                }\n            }\n        }\n        error(\"Bad string\");\n    }, white = function() {\n        // Skip whitespace.\n        while(ch && ch <= \" \"){\n            next();\n        }\n    }, word = function() {\n        // true, false, or null.\n        switch(ch){\n            case \"t\":\n                next(\"t\");\n                next(\"r\");\n                next(\"u\");\n                next(\"e\");\n                return true;\n            case \"f\":\n                next(\"f\");\n                next(\"a\");\n                next(\"l\");\n                next(\"s\");\n                next(\"e\");\n                return false;\n            case \"n\":\n                next(\"n\");\n                next(\"u\");\n                next(\"l\");\n                next(\"l\");\n                return null;\n        }\n        error(\"Unexpected '\" + ch + \"'\");\n    }, value, array = function() {\n        // Parse an array value.\n        var array = [];\n        if (ch === \"[\") {\n            next(\"[\");\n            white();\n            if (ch === \"]\") {\n                next(\"]\");\n                return array; // empty array\n            }\n            while(ch){\n                array.push(value());\n                white();\n                if (ch === \"]\") {\n                    next(\"]\");\n                    return array;\n                }\n                next(\",\");\n                white();\n            }\n        }\n        error(\"Bad array\");\n    }, object = function() {\n        // Parse an object value.\n        var key, object = Object.create(null);\n        if (ch === \"{\") {\n            next(\"{\");\n            white();\n            if (ch === \"}\") {\n                next(\"}\");\n                return object; // empty object\n            }\n            while(ch){\n                key = string();\n                white();\n                next(\":\");\n                if (_options.strict === true && Object.hasOwnProperty.call(object, key)) {\n                    error('Duplicate key \"' + key + '\"');\n                }\n                if (suspectProtoRx.test(key) === true) {\n                    if (_options.protoAction === \"error\") {\n                        error(\"Object contains forbidden prototype property\");\n                    } else if (_options.protoAction === \"ignore\") {\n                        value();\n                    } else {\n                        object[key] = value();\n                    }\n                } else if (suspectConstructorRx.test(key) === true) {\n                    if (_options.constructorAction === \"error\") {\n                        error(\"Object contains forbidden constructor property\");\n                    } else if (_options.constructorAction === \"ignore\") {\n                        value();\n                    } else {\n                        object[key] = value();\n                    }\n                } else {\n                    object[key] = value();\n                }\n                white();\n                if (ch === \"}\") {\n                    next(\"}\");\n                    return object;\n                }\n                next(\",\");\n                white();\n            }\n        }\n        error(\"Bad object\");\n    };\n    value = function() {\n        // Parse a JSON value. It could be an object, an array, a string, a number,\n        // or a word.\n        white();\n        switch(ch){\n            case \"{\":\n                return object();\n            case \"[\":\n                return array();\n            case '\"':\n                return string();\n            case \"-\":\n                return number();\n            default:\n                return ch >= \"0\" && ch <= \"9\" ? number() : word();\n        }\n    };\n    // Return the json_parse function. It will have access to all of the above\n    // functions and variables.\n    return function(source, reviver) {\n        var result;\n        text = source + \"\";\n        at = 0;\n        ch = \" \";\n        result = value();\n        white();\n        if (ch) {\n            error(\"Syntax error\");\n        }\n        // If there is a reviver function, we recursively walk the new structure,\n        // passing each name/value pair to the reviver function for possible\n        // transformation, starting with a temporary root object that holds the result\n        // in an empty key. If there is not a reviver function, we simply return the\n        // result.\n        return typeof reviver === \"function\" ? function walk(holder, key) {\n            var k, v, value = holder[key];\n            if (value && typeof value === \"object\") {\n                Object.keys(value).forEach(function(k) {\n                    v = walk(value, k);\n                    if (v !== undefined) {\n                        value[k] = v;\n                    } else {\n                        delete value[k];\n                    }\n                });\n            }\n            return reviver.call(holder, key, value);\n        }({\n            \"\": result\n        }, \"\") : result;\n    };\n};\nmodule.exports = json_parse;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/json-bigint/lib/parse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/json-bigint/lib/stringify.js":
/*!***************************************************!*\
  !*** ./node_modules/json-bigint/lib/stringify.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar BigNumber = __webpack_require__(/*! bignumber.js */ \"(rsc)/./node_modules/bignumber.js/bignumber.js\");\n/*\n    json2.js\n    2013-05-26\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    See http://www.JSON.org/js.html\n\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n\n\n    This file creates a global JSON object containing two methods: stringify\n    and parse.\n\n        JSON.stringify(value, replacer, space)\n            value       any JavaScript value, usually an object or array.\n\n            replacer    an optional parameter that determines how object\n                        values are stringified for objects. It can be a\n                        function or an array of strings.\n\n            space       an optional parameter that specifies the indentation\n                        of nested structures. If it is omitted, the text will\n                        be packed without extra whitespace. If it is a number,\n                        it will specify the number of spaces to indent at each\n                        level. If it is a string (such as '\\t' or '&nbsp;'),\n                        it contains the characters used to indent at each level.\n\n            This method produces a JSON text from a JavaScript value.\n\n            When an object value is found, if the object contains a toJSON\n            method, its toJSON method will be called and the result will be\n            stringified. A toJSON method does not serialize: it returns the\n            value represented by the name/value pair that should be serialized,\n            or undefined if nothing should be serialized. The toJSON method\n            will be passed the key associated with the value, and this will be\n            bound to the value\n\n            For example, this would serialize Dates as ISO strings.\n\n                Date.prototype.toJSON = function (key) {\n                    function f(n) {\n                        // Format integers to have at least two digits.\n                        return n < 10 ? '0' + n : n;\n                    }\n\n                    return this.getUTCFullYear()   + '-' +\n                         f(this.getUTCMonth() + 1) + '-' +\n                         f(this.getUTCDate())      + 'T' +\n                         f(this.getUTCHours())     + ':' +\n                         f(this.getUTCMinutes())   + ':' +\n                         f(this.getUTCSeconds())   + 'Z';\n                };\n\n            You can provide an optional replacer method. It will be passed the\n            key and value of each member, with this bound to the containing\n            object. The value that is returned from your method will be\n            serialized. If your method returns undefined, then the member will\n            be excluded from the serialization.\n\n            If the replacer parameter is an array of strings, then it will be\n            used to select the members to be serialized. It filters the results\n            such that only members with keys listed in the replacer array are\n            stringified.\n\n            Values that do not have JSON representations, such as undefined or\n            functions, will not be serialized. Such values in objects will be\n            dropped; in arrays they will be replaced with null. You can use\n            a replacer function to replace those with JSON values.\n            JSON.stringify(undefined) returns undefined.\n\n            The optional space parameter produces a stringification of the\n            value that is filled with line breaks and indentation to make it\n            easier to read.\n\n            If the space parameter is a non-empty string, then that string will\n            be used for indentation. If the space parameter is a number, then\n            the indentation will be that many spaces.\n\n            Example:\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}]);\n            // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}], null, '\\t');\n            // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n            text = JSON.stringify([new Date()], function (key, value) {\n                return this[key] instanceof Date ?\n                    'Date(' + this[key] + ')' : value;\n            });\n            // text is '[\"Date(---current time---)\"]'\n\n\n        JSON.parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = JSON.parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n            myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n                var d;\n                if (typeof value === 'string' &&\n                        value.slice(0, 5) === 'Date(' &&\n                        value.slice(-1) === ')') {\n                    d = new Date(value.slice(5, -1));\n                    if (d) {\n                        return d;\n                    }\n                }\n                return value;\n            });\n\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n*/ /*jslint evil: true, regexp: true */ /*members \"\", \"\\b\", \"\\t\", \"\\n\", \"\\f\", \"\\r\", \"\\\"\", JSON, \"\\\\\", apply,\n    call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n    getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n    lastIndex, length, parse, prototype, push, replace, slice, stringify,\n    test, toJSON, toString, valueOf\n*/ // Create a JSON object only if one does not already exist. We create the\n// methods in a closure to avoid creating global variables.\nvar JSON = module.exports;\n(function() {\n    \"use strict\";\n    function f(n) {\n        // Format integers to have at least two digits.\n        return n < 10 ? \"0\" + n : n;\n    }\n    var cx = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g, escapable = /[\\\\\\\"\\x00-\\x1f\\x7f-\\x9f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g, gap, indent, meta = {\n        \"\\b\": \"\\\\b\",\n        \"\t\": \"\\\\t\",\n        \"\\n\": \"\\\\n\",\n        \"\\f\": \"\\\\f\",\n        \"\\r\": \"\\\\r\",\n        '\"': '\\\\\"',\n        \"\\\\\": \"\\\\\\\\\"\n    }, rep;\n    function quote(string) {\n        // If the string contains no control characters, no quote characters, and no\n        // backslash characters, then we can safely slap some quotes around it.\n        // Otherwise we must also replace the offending characters with safe escape\n        // sequences.\n        escapable.lastIndex = 0;\n        return escapable.test(string) ? '\"' + string.replace(escapable, function(a) {\n            var c = meta[a];\n            return typeof c === \"string\" ? c : \"\\\\u\" + (\"0000\" + a.charCodeAt(0).toString(16)).slice(-4);\n        }) + '\"' : '\"' + string + '\"';\n    }\n    function str(key, holder) {\n        // Produce a string from holder[key].\n        var i, k, v, length, mind = gap, partial, value = holder[key], isBigNumber = value != null && (value instanceof BigNumber || BigNumber.isBigNumber(value));\n        // If the value has a toJSON method, call it to obtain a replacement value.\n        if (value && typeof value === \"object\" && typeof value.toJSON === \"function\") {\n            value = value.toJSON(key);\n        }\n        // If we were called with a replacer function, then call the replacer to\n        // obtain a replacement value.\n        if (typeof rep === \"function\") {\n            value = rep.call(holder, key, value);\n        }\n        // What happens next depends on the value's type.\n        switch(typeof value){\n            case \"string\":\n                if (isBigNumber) {\n                    return value;\n                } else {\n                    return quote(value);\n                }\n            case \"number\":\n                // JSON numbers must be finite. Encode non-finite numbers as null.\n                return isFinite(value) ? String(value) : \"null\";\n            case \"boolean\":\n            case \"null\":\n            case \"bigint\":\n                // If the value is a boolean or null, convert it to a string. Note:\n                // typeof null does not produce 'null'. The case is included here in\n                // the remote chance that this gets fixed someday.\n                return String(value);\n            // If the type is 'object', we might be dealing with an object or an array or\n            // null.\n            case \"object\":\n                // Due to a specification blunder in ECMAScript, typeof null is 'object',\n                // so watch out for that case.\n                if (!value) {\n                    return \"null\";\n                }\n                // Make an array to hold the partial results of stringifying this object value.\n                gap += indent;\n                partial = [];\n                // Is the value an array?\n                if (Object.prototype.toString.apply(value) === \"[object Array]\") {\n                    // The value is an array. Stringify every element. Use null as a placeholder\n                    // for non-JSON values.\n                    length = value.length;\n                    for(i = 0; i < length; i += 1){\n                        partial[i] = str(i, value) || \"null\";\n                    }\n                    // Join all of the elements together, separated with commas, and wrap them in\n                    // brackets.\n                    v = partial.length === 0 ? \"[]\" : gap ? \"[\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"]\" : \"[\" + partial.join(\",\") + \"]\";\n                    gap = mind;\n                    return v;\n                }\n                // If the replacer is an array, use it to select the members to be stringified.\n                if (rep && typeof rep === \"object\") {\n                    length = rep.length;\n                    for(i = 0; i < length; i += 1){\n                        if (typeof rep[i] === \"string\") {\n                            k = rep[i];\n                            v = str(k, value);\n                            if (v) {\n                                partial.push(quote(k) + (gap ? \": \" : \":\") + v);\n                            }\n                        }\n                    }\n                } else {\n                    // Otherwise, iterate through all of the keys in the object.\n                    Object.keys(value).forEach(function(k) {\n                        var v = str(k, value);\n                        if (v) {\n                            partial.push(quote(k) + (gap ? \": \" : \":\") + v);\n                        }\n                    });\n                }\n                // Join all of the member texts together, separated with commas,\n                // and wrap them in braces.\n                v = partial.length === 0 ? \"{}\" : gap ? \"{\\n\" + gap + partial.join(\",\\n\" + gap) + \"\\n\" + mind + \"}\" : \"{\" + partial.join(\",\") + \"}\";\n                gap = mind;\n                return v;\n        }\n    }\n    // If the JSON object does not yet have a stringify method, give it one.\n    if (typeof JSON.stringify !== \"function\") {\n        JSON.stringify = function(value, replacer, space) {\n            // The stringify method takes a value and an optional replacer, and an optional\n            // space parameter, and returns a JSON text. The replacer can be a function\n            // that can replace values, or an array of strings that will select the keys.\n            // A default replacer method can be provided. Use of the space parameter can\n            // produce text that is more easily readable.\n            var i;\n            gap = \"\";\n            indent = \"\";\n            // If the space parameter is a number, make an indent string containing that\n            // many spaces.\n            if (typeof space === \"number\") {\n                for(i = 0; i < space; i += 1){\n                    indent += \" \";\n                }\n            // If the space parameter is a string, it will be used as the indent string.\n            } else if (typeof space === \"string\") {\n                indent = space;\n            }\n            // If there is a replacer, it must be a function or an array.\n            // Otherwise, throw an error.\n            rep = replacer;\n            if (replacer && typeof replacer !== \"function\" && (typeof replacer !== \"object\" || typeof replacer.length !== \"number\")) {\n                throw new Error(\"JSON.stringify\");\n            }\n            // Make a fake root object containing our value under the key of ''.\n            // Return the result of stringifying the value.\n            return str(\"\", {\n                \"\": value\n            });\n        };\n    }\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/json-bigint/lib/stringify.js\n");

/***/ })

};
;