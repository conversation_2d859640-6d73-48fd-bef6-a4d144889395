/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2Nsb3VkJTVDZW1haWwlNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbWFpbC1jbGllbnQvPzhiNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gmail-client */ \"(ssr)/./src/lib/gmail-client.ts\");\n/* harmony import */ var _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/realtime-email-service */ \"(ssr)/./src/lib/realtime-email-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    const [gmailClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"]());\n    const [isLoadingEmails, setIsLoadingEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__[\"default\"]());\n    const [isServiceConnected, setIsServiceConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeEmails, setRealtimeEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notificationsEnabled, setNotificationsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从localStorage加载账户\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAccounts = ()=>{\n            try {\n                const saved = localStorage.getItem(\"emailAccounts\");\n                if (saved) {\n                    const accounts = JSON.parse(saved);\n                    setEmailAccounts(accounts);\n                    if (accounts.length > 0 && !activeAccountId) {\n                        setActiveAccountId(accounts[0].id);\n                    }\n                } else {\n                    // 默认示例账户\n                    const defaultAccounts = [\n                        {\n                            id: \"1\",\n                            email: \"<EMAIL>\",\n                            provider: \"gmail\",\n                            name: \"个人Gmail\",\n                            isActive: true,\n                            unreadCount: 3\n                        }\n                    ];\n                    setEmailAccounts(defaultAccounts);\n                    setActiveAccountId(\"1\");\n                }\n            } catch (error) {\n                console.error(\"Error loading accounts:\", error);\n            }\n        };\n        loadAccounts();\n    }, []);\n    // 初始化实时邮件服务\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initRealtimeService = async ()=>{\n            try {\n                // 连接到实时服务\n                const connected = await realtimeService.connect();\n                setIsServiceConnected(connected);\n                // 设置事件监听\n                realtimeService.on(\"service_status\", (data)=>{\n                    setIsServiceConnected(data.status === \"connected\");\n                });\n                realtimeService.on(\"new_email\", (email)=>{\n                    console.log(\"收到新邮件:\", email.subject);\n                    setRealtimeEmails((prev)=>[\n                            email,\n                            ...prev\n                        ]);\n                    // 更新未读计数\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === email.accountId ? {\n                                ...acc,\n                                unreadCount: acc.unreadCount + 1\n                            } : acc));\n                });\n                realtimeService.on(\"account_status_change\", (data)=>{\n                    console.log(\"账户状态变化:\", data);\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === data.accountId ? {\n                                ...acc,\n                                isConnected: data.status === \"connected\"\n                            } : acc));\n                });\n                realtimeService.on(\"service_error\", (data)=>{\n                    console.error(\"服务错误:\", data.error);\n                    setIsServiceConnected(false);\n                });\n                // 请求通知权限\n                const notificationGranted = await realtimeService.requestNotificationPermission();\n                setNotificationsEnabled(notificationGranted);\n            } catch (error) {\n                console.error(\"初始化实时服务失败:\", error);\n                setIsServiceConnected(false);\n            }\n        };\n        initRealtimeService();\n        // 清理函数\n        return ()=>{\n            realtimeService.disconnect();\n        };\n    }, []);\n    // 保存账户到localStorage\n    const saveAccounts = (accounts)=>{\n        try {\n            localStorage.setItem(\"emailAccounts\", JSON.stringify(accounts));\n            setEmailAccounts(accounts);\n        } catch (error) {\n            console.error(\"Error saving accounts:\", error);\n        }\n    };\n    // 模拟邮件数据\n    const mockEmails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 合并实时邮件和模拟邮件\n    const allEmails = [\n        ...realtimeEmails.map((email)=>({\n                id: parseInt(email.id),\n                from: email.from,\n                subject: email.subject,\n                preview: email.snippet,\n                time: new Date(email.date).toLocaleTimeString(\"zh-CN\", {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\"\n                }),\n                unread: email.isUnread,\n                starred: email.isStarred,\n                accountId: email.accountId\n            })),\n        ...mockEmails\n    ];\n    // 获取当前账户的邮件\n    const currentEmails = allEmails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n        setShowAccountMenu(null) // 关闭菜单\n        ;\n    };\n    // 删除邮箱账户\n    const deleteAccount = (accountId)=>{\n        if (emailAccounts.length <= 1) {\n            alert(\"至少需要保留一个邮箱账户\");\n            return;\n        }\n        if (confirm(\"确定要删除这个邮箱账户吗？\")) {\n            const updatedAccounts = emailAccounts.filter((acc)=>acc.id !== accountId);\n            saveAccounts(updatedAccounts);\n            // 如果删除的是当前活跃账户，切换到第一个可用账户\n            if (accountId === activeAccountId) {\n                if (updatedAccounts.length > 0) {\n                    setActiveAccountId(updatedAccounts[0].id);\n                }\n            }\n            setShowAccountMenu(null);\n            setSelectedEmail(null);\n        }\n    };\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showAccountMenu) {\n                setShowAccountMenu(null);\n            }\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        return ()=>document.removeEventListener(\"click\", handleClickOutside);\n    }, [\n        showAccountMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-800\",\n                                                children: \"邮箱客户端\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center gap-1 px-2 py-1 rounded-full text-xs ${isServiceConnected ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                                                children: [\n                                                    isServiceConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isServiceConnected ? \"在线\" : \"离线\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    const granted = await realtimeService.requestNotificationPermission();\n                                                    setNotificationsEnabled(granted);\n                                                },\n                                                className: `p-1 rounded ${notificationsEnabled ? \"text-blue-600 hover:bg-blue-50\" : \"text-gray-400 hover:bg-gray-50\"}`,\n                                                title: notificationsEnabled ? \"通知已开启\" : \"点击开启通知\",\n                                                children: notificationsEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `relative p-2 rounded text-sm transition-colors group ${account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>switchAccount(account.id),\n                                                    className: \"flex items-center justify-between cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getProviderIcon(account.provider)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: account.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: account.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                                    children: account.unreadCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowAccountMenu(showAccountMenu === account.id ? null : account.id);\n                                                                    },\n                                                                    className: \"p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showAccountMenu === account.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // TODO: 实现编辑功能\n                                                                setShowAccountMenu(null);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"编辑\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteAccount(account.id),\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, account.id, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: activeAccount?.name || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            activeAccount?.unreadCount && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: `p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\"} ${email.unread ? \"bg-white\" : \"bg-gray-50\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-sm ${email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"}`,\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm mb-1 ${email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"}`,\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 448,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: currentEmails.find((e)=>e.id === selectedEmail)?.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"来自: \",\n                                                            activeAccount?.name,\n                                                            \" (\",\n                                                            activeAccount?.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: [\n                                    \"当前账户: \",\n                                    activeAccount?.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"发件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" (\",\n                                                        account.email,\n                                                        \")\"\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, this),\n            showAddAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddAccountModal, {}, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 630,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n    // 添加邮箱账户弹窗组件\n    function AddAccountModal() {\n        const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n            name: \"\",\n            email: \"\",\n            provider: \"gmail\",\n            password: \"\"\n        });\n        const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n        // 自动检测邮箱服务商\n        const detectProvider = (email)=>{\n            const domain = email.split(\"@\")[1]?.toLowerCase();\n            const domainMap = {\n                \"gmail.com\": \"gmail\",\n                \"googlemail.com\": \"gmail\",\n                \"outlook.com\": \"outlook\",\n                \"hotmail.com\": \"outlook\",\n                \"live.com\": \"outlook\",\n                \"163.com\": \"163\",\n                \"126.com\": \"126\",\n                \"qq.com\": \"qq\"\n            };\n            return domainMap[domain] || \"other\";\n        };\n        // 处理邮箱地址变化\n        const handleEmailChange = (email)=>{\n            setFormData((prev)=>({\n                    ...prev,\n                    email,\n                    provider: detectProvider(email),\n                    name: prev.name || `${detectProvider(email)} - ${email}`\n                }));\n        };\n        const handleSubmit = async (e)=>{\n            e.preventDefault();\n            setIsSubmitting(true);\n            setError(\"\");\n            // 验证表单\n            if (!formData.email || !formData.password) {\n                setError(\"请填写邮箱地址和密码\");\n                setIsSubmitting(false);\n                return;\n            }\n            // 检查邮箱是否已存在\n            if (emailAccounts.some((acc)=>acc.email === formData.email)) {\n                setError(\"该邮箱账户已存在\");\n                setIsSubmitting(false);\n                return;\n            }\n            try {\n                // 先测试连接\n                setError(\"正在测试连接...\");\n                const testResult = await realtimeService.testEmailConnection(formData.email, formData.password, formData.provider);\n                if (!testResult.success) {\n                    setError(testResult.error || \"连接测试失败\");\n                    setIsSubmitting(false);\n                    return;\n                }\n                // 添加账户\n                setError(\"正在添加账户...\");\n                const newAccount = {\n                    id: Date.now().toString(),\n                    email: formData.email,\n                    provider: formData.provider,\n                    name: formData.name || `${formData.provider} - ${formData.email}`,\n                    isActive: false,\n                    unreadCount: 0,\n                    isConnected: false\n                };\n                const addResult = await realtimeService.addAccount(newAccount);\n                if (!addResult.success) {\n                    setError(addResult.error || \"添加账户失败\");\n                    setIsSubmitting(false);\n                    return;\n                }\n                // 保存到本地存储\n                const updatedAccounts = [\n                    ...emailAccounts,\n                    newAccount\n                ];\n                saveAccounts(updatedAccounts);\n                // 关闭弹窗并重置表单\n                setShowAddAccount(false);\n                setFormData({\n                    name: \"\",\n                    email: \"\",\n                    provider: \"gmail\",\n                    password: \"\"\n                });\n                // 切换到新添加的账户\n                setActiveAccountId(newAccount.id);\n            } catch (err) {\n                setError(err.message || \"添加账户失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg w-full max-w-md mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"添加邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddAccount(false),\n                                className: \"text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 751,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 763,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"邮箱地址 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: formData.email,\n                                        onChange: (e)=>handleEmailChange(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600 mt-1\",\n                                        children: [\n                                            \"✓ 自动识别为 \",\n                                            formData.provider === \"gmail\" ? \"Gmail\" : formData.provider === \"outlook\" ? \"Outlook\" : formData.provider === \"163\" ? \"163邮箱\" : formData.provider === \"qq\" ? \"QQ邮箱\" : formData.provider === \"126\" ? \"126邮箱\" : \"其他邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"账户名称 (可选)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"自动生成或自定义名称\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 796,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"密码/应用专用密码 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 807,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: formData.password,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    password: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"输入密码或应用专用密码\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-xs text-gray-600\",\n                                        children: [\n                                            formData.provider === \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-yellow-800\",\n                                                        children: \"Gmail用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-yellow-700\",\n                                                        children: '需要开启两步验证并生成\"应用专用密码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"qq\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border border-blue-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-blue-800\",\n                                                        children: \"QQ邮箱用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-700\",\n                                                        children: '需要开启IMAP/SMTP服务并使用\"授权码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 827,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 825,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"163\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-green-800\",\n                                                        children: \"163邮箱用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 832,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700\",\n                                                        children: '需要开启IMAP/SMTP服务并使用\"客户端授权密码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 831,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"outlook\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 border border-purple-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-purple-800\",\n                                                        children: \"Outlook用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-700\",\n                                                        children: \"可直接使用登录密码，或使用应用专用密码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 839,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 19\n                                            }, this),\n                                            ![\n                                                \"gmail\",\n                                                \"qq\",\n                                                \"163\",\n                                                \"outlook\"\n                                            ].includes(formData.provider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"建议使用应用专用密码以提高安全性\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 805,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddAccount(false),\n                                        className: \"flex-1 px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                        disabled: isSubmitting,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || !formData.email || !formData.password,\n                                        className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 866,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"连接中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 871,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"测试连接并添加\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 761,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 750,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 749,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/gmail-client.ts":
/*!*********************************!*\
  !*** ./src/lib/gmail-client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GmailClient: () => (/* binding */ GmailClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// 前端Gmail API客户端\nclass GmailClient {\n    constructor(){\n        this.baseUrl = \"http://localhost:3000\" || 0;\n    }\n    // 获取Google OAuth授权URL\n    async getAuthUrl() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`);\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to get auth URL\");\n            }\n            return data.authUrl;\n        } catch (error) {\n            console.error(\"Error getting auth URL:\", error);\n            throw error;\n        }\n    }\n    // 处理OAuth回调\n    async handleAuthCallback(code) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Authentication failed\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error handling auth callback:\", error);\n            throw error;\n        }\n    }\n    // 获取邮件列表\n    async getMessages(accessToken, maxResults = 20, query) {\n        try {\n            const url = new URL(`${this.baseUrl}/api/gmail/messages`);\n            url.searchParams.set(\"maxResults\", maxResults.toString());\n            if (query) {\n                url.searchParams.set(\"query\", query);\n            }\n            const response = await fetch(url.toString(), {\n                headers: {\n                    \"Authorization\": `Bearer ${accessToken}`\n                }\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to fetch messages\");\n            }\n            return data.emails;\n        } catch (error) {\n            console.error(\"Error fetching messages:\", error);\n            throw error;\n        }\n    }\n    // 发送邮件\n    async sendMessage(accessToken, to, subject, body, from) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    to,\n                    subject,\n                    body,\n                    from\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to send message\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            throw error;\n        }\n    }\n    // 标记邮件为已读\n    async markAsRead(accessToken, messageId) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"markAsRead\"\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to mark as read\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error marking as read:\", error);\n            throw error;\n        }\n    }\n    // 切换星标\n    async toggleStar(accessToken, messageId, isStarred) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"toggleStar\",\n                    value: isStarred\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to toggle star\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error toggling star:\", error);\n            throw error;\n        }\n    }\n    // 启动OAuth流程\n    startOAuthFlow() {\n        this.getAuthUrl().then((authUrl)=>{\n            // 在新窗口中打开授权页面\n            const popup = window.open(authUrl, \"gmail-auth\", \"width=500,height=600,scrollbars=yes,resizable=yes\");\n            // 监听授权完成\n            const checkClosed = setInterval(()=>{\n                if (popup?.closed) {\n                    clearInterval(checkClosed);\n                    // 可以在这里触发刷新或重新获取数据\n                    window.location.reload();\n                }\n            }, 1000);\n        }).catch((error)=>{\n            console.error(\"Failed to start OAuth flow:\", error);\n            alert(\"无法启动Gmail授权流程，请稍后重试\");\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GmailClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/gmail-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/realtime-email-service.ts":
/*!*******************************************!*\
  !*** ./src/lib/realtime-email-service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeEmailService: () => (/* binding */ RealtimeEmailService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n// 实时邮件服务 - 支持持续监听和实时推送\n\nclass RealtimeEmailService {\n    constructor(){\n        this.socket = null;\n        this.accounts = new Map();\n        this.listeners = new Map();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.initializeSocket();\n    }\n    // 初始化WebSocket连接\n    initializeSocket() {\n        try {\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(\"ws://localhost:3000\", {\n                transports: [\n                    \"websocket\"\n                ],\n                autoConnect: false\n            });\n            this.socket.on(\"connect\", ()=>{\n                console.log(\"✅ 实时邮件服务已连接\");\n                this.reconnectAttempts = 0;\n                this.emit(\"service_status\", {\n                    status: \"connected\"\n                });\n            });\n            this.socket.on(\"disconnect\", ()=>{\n                console.log(\"❌ 实时邮件服务已断开\");\n                this.emit(\"service_status\", {\n                    status: \"disconnected\"\n                });\n                this.handleReconnect();\n            });\n            this.socket.on(\"new_email\", (data)=>{\n                console.log(\"\\uD83D\\uDCE7 收到新邮件:\", data.subject);\n                this.handleNewEmail(data);\n            });\n            this.socket.on(\"email_status_change\", (data)=>{\n                this.emit(\"email_status_change\", data);\n            });\n            this.socket.on(\"account_status\", (data)=>{\n                this.updateAccountStatus(data.accountId, data.status);\n            });\n            this.socket.on(\"error\", (error)=>{\n                console.error(\"Socket错误:\", error);\n                this.emit(\"service_error\", {\n                    error: error.message\n                });\n            });\n        } catch (error) {\n            console.error(\"初始化Socket失败:\", error);\n        }\n    }\n    // 连接到实时服务\n    async connect() {\n        try {\n            if (!this.socket) {\n                this.initializeSocket();\n            }\n            this.socket?.connect();\n            return true;\n        } catch (error) {\n            console.error(\"连接失败:\", error);\n            return false;\n        }\n    }\n    // 断开连接\n    disconnect() {\n        this.socket?.disconnect();\n    }\n    // 测试邮箱连接\n    async testEmailConnection(email, password, provider) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                resolve({\n                    success: false,\n                    error: \"服务未连接\"\n                });\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                resolve({\n                    success: false,\n                    error: \"测试连接超时\"\n                });\n            }, 15000);\n            this.socket.emit(\"test_email_connection\", {\n                email,\n                password,\n                provider\n            });\n            this.socket.once(\"test_connection_result\", (data)=>{\n                clearTimeout(timeout);\n                resolve(data);\n            });\n        });\n    }\n    // 添加邮箱账户并开始监听\n    async addAccount(account) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                resolve({\n                    success: false,\n                    error: \"服务未连接\"\n                });\n                return;\n            }\n            // 保存账户信息\n            this.accounts.set(account.id, {\n                ...account,\n                isConnected: false\n            });\n            const timeout = setTimeout(()=>{\n                resolve({\n                    success: false,\n                    error: \"添加账户超时\"\n                });\n            }, 20000);\n            this.socket.emit(\"add_email_account\", {\n                accountId: account.id,\n                email: account.email,\n                password: account.password,\n                provider: account.provider,\n                name: account.name\n            });\n            this.socket.once(\"add_account_result\", (data)=>{\n                clearTimeout(timeout);\n                resolve(data);\n            });\n        });\n    }\n    // 移除邮箱账户\n    removeAccount(accountId) {\n        this.accounts.delete(accountId);\n        this.socket?.emit(\"remove_account\", {\n            accountId\n        });\n    }\n    // 获取邮件列表\n    async getEmails(accountId, limit = 50) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"获取邮件超时\"));\n            }, 10000);\n            this.socket.emit(\"get_emails\", {\n                accountId,\n                limit\n            });\n            this.socket.once(\"emails_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 发送邮件\n    async sendEmail(accountId, to, subject, body) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"发送邮件超时\"));\n            }, 30000);\n            this.socket.emit(\"send_email\", {\n                accountId,\n                to,\n                subject,\n                body\n            });\n            this.socket.once(\"send_email_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(true);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 标记邮件为已读\n    async markAsRead(accountId, emailId) {\n        this.socket?.emit(\"mark_as_read\", {\n            accountId,\n            emailId\n        });\n        return true;\n    }\n    // 切换星标\n    async toggleStar(accountId, emailId, starred) {\n        this.socket?.emit(\"toggle_star\", {\n            accountId,\n            emailId,\n            starred\n        });\n        return true;\n    }\n    // 搜索邮件\n    async searchEmails(accountId, query) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"搜索超时\"));\n            }, 15000);\n            this.socket.emit(\"search_emails\", {\n                accountId,\n                query\n            });\n            this.socket.once(\"search_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 事件监听\n    on(event, callback) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        this.listeners.get(event)?.push(callback);\n    }\n    // 移除事件监听\n    off(event, callback) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        }\n    }\n    // 触发事件\n    emit(event, data) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            callbacks.forEach((callback)=>callback(data));\n        }\n    }\n    // 处理新邮件\n    handleNewEmail(email) {\n        // 更新未读计数\n        const account = this.accounts.get(email.accountId);\n        if (account) {\n            account.unreadCount += 1;\n            this.accounts.set(email.accountId, account);\n        }\n        // 显示桌面通知\n        this.showDesktopNotification(email);\n        // 触发新邮件事件\n        this.emit(\"new_email\", email);\n    }\n    // 显示桌面通知\n    showDesktopNotification(email) {\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(`新邮件 - ${email.from}`, {\n                body: email.subject,\n                icon: \"/favicon.ico\",\n                tag: email.id\n            });\n        }\n    }\n    // 请求通知权限\n    async requestNotificationPermission() {\n        if (\"Notification\" in window) {\n            const permission = await Notification.requestPermission();\n            return permission === \"granted\";\n        }\n        return false;\n    }\n    // 处理重连\n    handleReconnect() {\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.reconnectAttempts++;\n            console.log(`尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n            setTimeout(()=>{\n                this.socket?.connect();\n            }, Math.pow(2, this.reconnectAttempts) * 1000) // 指数退避\n            ;\n        } else {\n            console.error(\"重连失败，已达到最大尝试次数\");\n            this.emit(\"service_error\", {\n                error: \"连接失败，请刷新页面重试\"\n            });\n        }\n    }\n    // 验证账户\n    async validateAccount(account) {\n        // 基本验证\n        if (!account.email || !account.password) {\n            return {\n                success: false,\n                error: \"邮箱地址和密码不能为空\"\n            };\n        }\n        if (!account.email.includes(\"@\")) {\n            return {\n                success: false,\n                error: \"邮箱地址格式不正确\"\n            };\n        }\n        return {\n            success: true\n        };\n    }\n    // 获取IMAP配置\n    getImapConfig(provider) {\n        const configs = {\n            gmail: {\n                host: \"imap.gmail.com\",\n                port: 993,\n                secure: true\n            },\n            outlook: {\n                host: \"outlook.office365.com\",\n                port: 993,\n                secure: true\n            },\n            \"163\": {\n                host: \"imap.163.com\",\n                port: 993,\n                secure: true\n            },\n            qq: {\n                host: \"imap.qq.com\",\n                port: 993,\n                secure: true\n            },\n            \"126\": {\n                host: \"imap.126.com\",\n                port: 993,\n                secure: true\n            },\n            sina: {\n                host: \"imap.sina.com\",\n                port: 993,\n                secure: true\n            }\n        };\n        return configs[provider] || null;\n    }\n    // 更新账户状态\n    updateAccountStatus(accountId, status) {\n        const account = this.accounts.get(accountId);\n        if (account) {\n            account.isConnected = status === \"connected\";\n            account.lastSync = new Date().toISOString();\n            this.accounts.set(accountId, account);\n            this.emit(\"account_status_change\", {\n                accountId,\n                status\n            });\n        }\n    }\n    // 获取账户状态\n    getAccountStatus(accountId) {\n        return this.accounts.get(accountId);\n    }\n    // 获取所有账户\n    getAllAccounts() {\n        return Array.from(this.accounts.values());\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RealtimeEmailService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/realtime-email-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0229964a418d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9nbG9iYWxzLmNzcz9mOGNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDIyOTk2NGE0MThkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"邮箱客户端\",\n    description: \"支持多邮箱的网页版邮件客户端\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFDOUIsNEVBQUNVO2dCQUFJRCxXQUFVOzBCQUNaSjs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfpgq7nrrHlrqLmiLfnq68nLFxuICBkZXNjcmlwdGlvbjogJ+aUr+aMgeWkmumCrueuseeahOe9kemhteeJiOmCruS7tuWuouaIt+errycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\cloud\email\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/lucide-react","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();