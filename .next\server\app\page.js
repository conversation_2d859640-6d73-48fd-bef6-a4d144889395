/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2Nsb3VkJTVDZW1haWwlNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbWFpbC1jbGllbnQvPzhiNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gmail-client */ \"(ssr)/./src/lib/gmail-client.ts\");\n/* harmony import */ var _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/realtime-email-service */ \"(ssr)/./src/lib/realtime-email-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    const [gmailClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"]());\n    const [isLoadingEmails, setIsLoadingEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__[\"default\"]());\n    const [isServiceConnected, setIsServiceConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeEmails, setRealtimeEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notificationsEnabled, setNotificationsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从localStorage加载账户\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAccounts = ()=>{\n            try {\n                const saved = localStorage.getItem(\"emailAccounts\");\n                if (saved) {\n                    const accounts = JSON.parse(saved);\n                    setEmailAccounts(accounts);\n                    if (accounts.length > 0 && !activeAccountId) {\n                        setActiveAccountId(accounts[0].id);\n                    }\n                } else {\n                    // 默认示例账户\n                    const defaultAccounts = [\n                        {\n                            id: \"1\",\n                            email: \"<EMAIL>\",\n                            provider: \"gmail\",\n                            name: \"个人Gmail\",\n                            isActive: true,\n                            unreadCount: 3\n                        }\n                    ];\n                    setEmailAccounts(defaultAccounts);\n                    setActiveAccountId(\"1\");\n                }\n            } catch (error) {\n                console.error(\"Error loading accounts:\", error);\n            }\n        };\n        loadAccounts();\n    }, []);\n    // 初始化实时邮件服务\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initRealtimeService = async ()=>{\n            try {\n                // 连接到实时服务\n                const connected = await realtimeService.connect();\n                setIsServiceConnected(connected);\n                // 设置事件监听\n                realtimeService.on(\"service_status\", (data)=>{\n                    setIsServiceConnected(data.status === \"connected\");\n                });\n                realtimeService.on(\"new_email\", (email)=>{\n                    console.log(\"收到新邮件:\", email.subject);\n                    setRealtimeEmails((prev)=>[\n                            email,\n                            ...prev\n                        ]);\n                    // 更新未读计数\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === email.accountId ? {\n                                ...acc,\n                                unreadCount: acc.unreadCount + 1\n                            } : acc));\n                });\n                realtimeService.on(\"account_status_change\", (data)=>{\n                    console.log(\"账户状态变化:\", data);\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === data.accountId ? {\n                                ...acc,\n                                isConnected: data.status === \"connected\"\n                            } : acc));\n                });\n                realtimeService.on(\"service_error\", (data)=>{\n                    console.error(\"服务错误:\", data.error);\n                    setIsServiceConnected(false);\n                });\n                // 请求通知权限\n                const notificationGranted = await realtimeService.requestNotificationPermission();\n                setNotificationsEnabled(notificationGranted);\n            } catch (error) {\n                console.error(\"初始化实时服务失败:\", error);\n                setIsServiceConnected(false);\n            }\n        };\n        initRealtimeService();\n        // 清理函数\n        return ()=>{\n            realtimeService.disconnect();\n        };\n    }, []);\n    // 保存账户到localStorage\n    const saveAccounts = (accounts)=>{\n        try {\n            localStorage.setItem(\"emailAccounts\", JSON.stringify(accounts));\n            setEmailAccounts(accounts);\n        } catch (error) {\n            console.error(\"Error saving accounts:\", error);\n        }\n    };\n    // 模拟邮件数据\n    const mockEmails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 合并实时邮件和模拟邮件\n    const allEmails = [\n        ...realtimeEmails.map((email)=>({\n                id: parseInt(email.id),\n                from: email.from,\n                subject: email.subject,\n                preview: email.snippet,\n                time: new Date(email.date).toLocaleTimeString(\"zh-CN\", {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\"\n                }),\n                unread: email.isUnread,\n                starred: email.isStarred,\n                accountId: email.accountId\n            })),\n        ...mockEmails\n    ];\n    // 获取当前账户的邮件\n    const currentEmails = allEmails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n        setShowAccountMenu(null) // 关闭菜单\n        ;\n    };\n    // 删除邮箱账户\n    const deleteAccount = (accountId)=>{\n        if (emailAccounts.length <= 1) {\n            alert(\"至少需要保留一个邮箱账户\");\n            return;\n        }\n        if (confirm(\"确定要删除这个邮箱账户吗？\")) {\n            const updatedAccounts = emailAccounts.filter((acc)=>acc.id !== accountId);\n            saveAccounts(updatedAccounts);\n            // 如果删除的是当前活跃账户，切换到第一个可用账户\n            if (accountId === activeAccountId) {\n                if (updatedAccounts.length > 0) {\n                    setActiveAccountId(updatedAccounts[0].id);\n                }\n            }\n            setShowAccountMenu(null);\n            setSelectedEmail(null);\n        }\n    };\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showAccountMenu) {\n                setShowAccountMenu(null);\n            }\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        return ()=>document.removeEventListener(\"click\", handleClickOutside);\n    }, [\n        showAccountMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-800\",\n                                                children: \"邮箱客户端\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center gap-1 px-2 py-1 rounded-full text-xs ${isServiceConnected ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                                                children: [\n                                                    isServiceConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isServiceConnected ? \"在线\" : \"离线\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    const granted = await realtimeService.requestNotificationPermission();\n                                                    setNotificationsEnabled(granted);\n                                                },\n                                                className: `p-1 rounded ${notificationsEnabled ? \"text-blue-600 hover:bg-blue-50\" : \"text-gray-400 hover:bg-gray-50\"}`,\n                                                title: notificationsEnabled ? \"通知已开启\" : \"点击开启通知\",\n                                                children: notificationsEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `relative p-2 rounded text-sm transition-colors group ${account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>switchAccount(account.id),\n                                                    className: \"flex items-center justify-between cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getProviderIcon(account.provider)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: account.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: account.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                                    children: account.unreadCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowAccountMenu(showAccountMenu === account.id ? null : account.id);\n                                                                    },\n                                                                    className: \"p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showAccountMenu === account.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // TODO: 实现编辑功能\n                                                                setShowAccountMenu(null);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"编辑\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteAccount(account.id),\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, account.id, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: activeAccount?.name || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            activeAccount?.unreadCount && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: `p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\"} ${email.unread ? \"bg-white\" : \"bg-gray-50\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-sm ${email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"}`,\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm mb-1 ${email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"}`,\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 448,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: currentEmails.find((e)=>e.id === selectedEmail)?.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"来自: \",\n                                                            activeAccount?.name,\n                                                            \" (\",\n                                                            activeAccount?.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: [\n                                    \"当前账户: \",\n                                    activeAccount?.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"发件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" (\",\n                                                        account.email,\n                                                        \")\"\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, this),\n            showAddAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddAccountModal, {}, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 630,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n    // 添加邮箱账户弹窗组件\n    function AddAccountModal() {\n        const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n            name: \"\",\n            email: \"\",\n            provider: \"gmail\",\n            password: \"\"\n        });\n        const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n        // Gmail OAuth认证\n        const handleGmailAuth = async ()=>{\n            try {\n                setIsSubmitting(true);\n                setError(\"\");\n                // 启动OAuth流程\n                gmailClient.startOAuthFlow();\n                // 关闭弹窗，OAuth完成后会自动刷新页面\n                setShowAddAccount(false);\n            } catch (error) {\n                console.error(\"Gmail auth error:\", error);\n                setError(\"Gmail授权失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        const handleSubmit = async (e)=>{\n            e.preventDefault();\n            // 如果是Gmail，使用OAuth\n            if (formData.provider === \"gmail\") {\n                handleGmailAuth();\n                return;\n            }\n            setIsSubmitting(true);\n            setError(\"\");\n            // 验证表单\n            if (!formData.name || !formData.email || !formData.password) {\n                setError(\"请填写所有必填字段\");\n                setIsSubmitting(false);\n                return;\n            }\n            // 检查邮箱是否已存在\n            if (emailAccounts.some((acc)=>acc.email === formData.email)) {\n                setError(\"该邮箱账户已存在\");\n                setIsSubmitting(false);\n                return;\n            }\n            try {\n                // 模拟添加账户的过程\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                const newAccount = {\n                    id: Date.now().toString(),\n                    email: formData.email,\n                    provider: formData.provider,\n                    name: formData.name,\n                    isActive: false,\n                    unreadCount: 0\n                };\n                const updatedAccounts = [\n                    ...emailAccounts,\n                    newAccount\n                ];\n                saveAccounts(updatedAccounts);\n                setShowAddAccount(false);\n                // 重置表单\n                setFormData({\n                    name: \"\",\n                    email: \"\",\n                    provider: \"gmail\",\n                    password: \"\"\n                });\n            } catch (err) {\n                setError(\"添加账户失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg w-full max-w-md mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"添加邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddAccount(false),\n                                className: \"text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 730,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"账户名称 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 743,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"例如：个人邮箱、工作邮箱\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"邮箱服务商 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: formData.provider,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    provider: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"gmail\",\n                                                children: \"Gmail (Google)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"outlook\",\n                                                children: \"Outlook (Microsoft)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"163\",\n                                                children: \"163邮箱 (网易)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qq\",\n                                                children: \"QQ邮箱 (腾讯)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"other\",\n                                                children: \"其他\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.provider === \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600 mt-1\",\n                                        children: \"Gmail使用OAuth2安全认证，无需输入密码\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 13\n                            }, this),\n                            formData.provider !== \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: [\n                                                    \"邮箱地址 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 782,\n                                                        columnNumber: 26\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: formData.email,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            email: e.target.value\n                                                        })),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                placeholder: \"<EMAIL>\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: [\n                                                    \"密码/应用专用密码 \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-500\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"password\",\n                                                value: formData.password,\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            password: e.target.value\n                                                        })),\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                placeholder: \"输入密码\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: \"建议使用应用专用密码以提高安全性\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 794,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            formData.provider === \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-blue-600 rounded flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xs font-bold\",\n                                                    children: \"G\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 817,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 816,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900\",\n                                                children: \"Gmail OAuth认证\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 815,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700 mb-3\",\n                                        children: \"点击下方按钮将打开Google授权页面，安全地连接您的Gmail账户。\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-600\",\n                                        children: \"我们不会存储您的密码，所有认证都通过Google官方进行。\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 824,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 814,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddAccount(false),\n                                        className: \"flex-1 px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                        disabled: isSubmitting,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 831,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 846,\n                                                    columnNumber: 21\n                                                }, this),\n                                                formData.provider === \"gmail\" ? \"授权中...\" : \"添加中...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: formData.provider === \"gmail\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"使用Google授权\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \"添加账户\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 734,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 723,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 722,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/gmail-client.ts":
/*!*********************************!*\
  !*** ./src/lib/gmail-client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GmailClient: () => (/* binding */ GmailClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// 前端Gmail API客户端\nclass GmailClient {\n    constructor(){\n        this.baseUrl = \"http://localhost:3000\" || 0;\n    }\n    // 获取Google OAuth授权URL\n    async getAuthUrl() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`);\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to get auth URL\");\n            }\n            return data.authUrl;\n        } catch (error) {\n            console.error(\"Error getting auth URL:\", error);\n            throw error;\n        }\n    }\n    // 处理OAuth回调\n    async handleAuthCallback(code) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Authentication failed\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error handling auth callback:\", error);\n            throw error;\n        }\n    }\n    // 获取邮件列表\n    async getMessages(accessToken, maxResults = 20, query) {\n        try {\n            const url = new URL(`${this.baseUrl}/api/gmail/messages`);\n            url.searchParams.set(\"maxResults\", maxResults.toString());\n            if (query) {\n                url.searchParams.set(\"query\", query);\n            }\n            const response = await fetch(url.toString(), {\n                headers: {\n                    \"Authorization\": `Bearer ${accessToken}`\n                }\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to fetch messages\");\n            }\n            return data.emails;\n        } catch (error) {\n            console.error(\"Error fetching messages:\", error);\n            throw error;\n        }\n    }\n    // 发送邮件\n    async sendMessage(accessToken, to, subject, body, from) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    to,\n                    subject,\n                    body,\n                    from\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to send message\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            throw error;\n        }\n    }\n    // 标记邮件为已读\n    async markAsRead(accessToken, messageId) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"markAsRead\"\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to mark as read\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error marking as read:\", error);\n            throw error;\n        }\n    }\n    // 切换星标\n    async toggleStar(accessToken, messageId, isStarred) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"toggleStar\",\n                    value: isStarred\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to toggle star\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error toggling star:\", error);\n            throw error;\n        }\n    }\n    // 启动OAuth流程\n    startOAuthFlow() {\n        this.getAuthUrl().then((authUrl)=>{\n            // 在新窗口中打开授权页面\n            const popup = window.open(authUrl, \"gmail-auth\", \"width=500,height=600,scrollbars=yes,resizable=yes\");\n            // 监听授权完成\n            const checkClosed = setInterval(()=>{\n                if (popup?.closed) {\n                    clearInterval(checkClosed);\n                    // 可以在这里触发刷新或重新获取数据\n                    window.location.reload();\n                }\n            }, 1000);\n        }).catch((error)=>{\n            console.error(\"Failed to start OAuth flow:\", error);\n            alert(\"无法启动Gmail授权流程，请稍后重试\");\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GmailClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/gmail-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/realtime-email-service.ts":
/*!*******************************************!*\
  !*** ./src/lib/realtime-email-service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeEmailService: () => (/* binding */ RealtimeEmailService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n// 实时邮件服务 - 支持持续监听和实时推送\n\nclass RealtimeEmailService {\n    constructor(){\n        this.socket = null;\n        this.accounts = new Map();\n        this.listeners = new Map();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.initializeSocket();\n    }\n    // 初始化WebSocket连接\n    initializeSocket() {\n        try {\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(\"ws://localhost:3001\", {\n                transports: [\n                    \"websocket\"\n                ],\n                autoConnect: false\n            });\n            this.socket.on(\"connect\", ()=>{\n                console.log(\"✅ 实时邮件服务已连接\");\n                this.reconnectAttempts = 0;\n                this.emit(\"service_status\", {\n                    status: \"connected\"\n                });\n            });\n            this.socket.on(\"disconnect\", ()=>{\n                console.log(\"❌ 实时邮件服务已断开\");\n                this.emit(\"service_status\", {\n                    status: \"disconnected\"\n                });\n                this.handleReconnect();\n            });\n            this.socket.on(\"new_email\", (data)=>{\n                console.log(\"\\uD83D\\uDCE7 收到新邮件:\", data.subject);\n                this.handleNewEmail(data);\n            });\n            this.socket.on(\"email_status_change\", (data)=>{\n                this.emit(\"email_status_change\", data);\n            });\n            this.socket.on(\"account_status\", (data)=>{\n                this.updateAccountStatus(data.accountId, data.status);\n            });\n            this.socket.on(\"error\", (error)=>{\n                console.error(\"Socket错误:\", error);\n                this.emit(\"service_error\", {\n                    error: error.message\n                });\n            });\n        } catch (error) {\n            console.error(\"初始化Socket失败:\", error);\n        }\n    }\n    // 连接到实时服务\n    async connect() {\n        try {\n            if (!this.socket) {\n                this.initializeSocket();\n            }\n            this.socket?.connect();\n            return true;\n        } catch (error) {\n            console.error(\"连接失败:\", error);\n            return false;\n        }\n    }\n    // 断开连接\n    disconnect() {\n        this.socket?.disconnect();\n    }\n    // 添加邮箱账户并开始监听\n    async addAccount(account) {\n        try {\n            // 验证账户配置\n            const validation = await this.validateAccount(account);\n            if (!validation.success) {\n                throw new Error(validation.error);\n            }\n            // 保存账户信息\n            this.accounts.set(account.id, {\n                ...account,\n                isConnected: false\n            });\n            // 通知服务器开始监听此账户\n            this.socket?.emit(\"add_account\", {\n                accountId: account.id,\n                email: account.email,\n                password: account.password,\n                provider: account.provider,\n                imapConfig: this.getImapConfig(account.provider)\n            });\n            return true;\n        } catch (error) {\n            console.error(\"添加账户失败:\", error);\n            return false;\n        }\n    }\n    // 移除邮箱账户\n    removeAccount(accountId) {\n        this.accounts.delete(accountId);\n        this.socket?.emit(\"remove_account\", {\n            accountId\n        });\n    }\n    // 获取邮件列表\n    async getEmails(accountId, limit = 50) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"获取邮件超时\"));\n            }, 10000);\n            this.socket.emit(\"get_emails\", {\n                accountId,\n                limit\n            });\n            this.socket.once(\"emails_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 发送邮件\n    async sendEmail(accountId, to, subject, body) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"发送邮件超时\"));\n            }, 30000);\n            this.socket.emit(\"send_email\", {\n                accountId,\n                to,\n                subject,\n                body\n            });\n            this.socket.once(\"send_email_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(true);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 标记邮件为已读\n    async markAsRead(accountId, emailId) {\n        this.socket?.emit(\"mark_as_read\", {\n            accountId,\n            emailId\n        });\n        return true;\n    }\n    // 切换星标\n    async toggleStar(accountId, emailId, starred) {\n        this.socket?.emit(\"toggle_star\", {\n            accountId,\n            emailId,\n            starred\n        });\n        return true;\n    }\n    // 搜索邮件\n    async searchEmails(accountId, query) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"搜索超时\"));\n            }, 15000);\n            this.socket.emit(\"search_emails\", {\n                accountId,\n                query\n            });\n            this.socket.once(\"search_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 事件监听\n    on(event, callback) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        this.listeners.get(event)?.push(callback);\n    }\n    // 移除事件监听\n    off(event, callback) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        }\n    }\n    // 触发事件\n    emit(event, data) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            callbacks.forEach((callback)=>callback(data));\n        }\n    }\n    // 处理新邮件\n    handleNewEmail(email) {\n        // 更新未读计数\n        const account = this.accounts.get(email.accountId);\n        if (account) {\n            account.unreadCount += 1;\n            this.accounts.set(email.accountId, account);\n        }\n        // 显示桌面通知\n        this.showDesktopNotification(email);\n        // 触发新邮件事件\n        this.emit(\"new_email\", email);\n    }\n    // 显示桌面通知\n    showDesktopNotification(email) {\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(`新邮件 - ${email.from}`, {\n                body: email.subject,\n                icon: \"/favicon.ico\",\n                tag: email.id\n            });\n        }\n    }\n    // 请求通知权限\n    async requestNotificationPermission() {\n        if (\"Notification\" in window) {\n            const permission = await Notification.requestPermission();\n            return permission === \"granted\";\n        }\n        return false;\n    }\n    // 处理重连\n    handleReconnect() {\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.reconnectAttempts++;\n            console.log(`尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n            setTimeout(()=>{\n                this.socket?.connect();\n            }, Math.pow(2, this.reconnectAttempts) * 1000) // 指数退避\n            ;\n        } else {\n            console.error(\"重连失败，已达到最大尝试次数\");\n            this.emit(\"service_error\", {\n                error: \"连接失败，请刷新页面重试\"\n            });\n        }\n    }\n    // 验证账户\n    async validateAccount(account) {\n        // 基本验证\n        if (!account.email || !account.password) {\n            return {\n                success: false,\n                error: \"邮箱地址和密码不能为空\"\n            };\n        }\n        if (!account.email.includes(\"@\")) {\n            return {\n                success: false,\n                error: \"邮箱地址格式不正确\"\n            };\n        }\n        return {\n            success: true\n        };\n    }\n    // 获取IMAP配置\n    getImapConfig(provider) {\n        const configs = {\n            gmail: {\n                host: \"imap.gmail.com\",\n                port: 993,\n                secure: true\n            },\n            outlook: {\n                host: \"outlook.office365.com\",\n                port: 993,\n                secure: true\n            },\n            \"163\": {\n                host: \"imap.163.com\",\n                port: 993,\n                secure: true\n            },\n            qq: {\n                host: \"imap.qq.com\",\n                port: 993,\n                secure: true\n            },\n            \"126\": {\n                host: \"imap.126.com\",\n                port: 993,\n                secure: true\n            },\n            sina: {\n                host: \"imap.sina.com\",\n                port: 993,\n                secure: true\n            }\n        };\n        return configs[provider] || null;\n    }\n    // 更新账户状态\n    updateAccountStatus(accountId, status) {\n        const account = this.accounts.get(accountId);\n        if (account) {\n            account.isConnected = status === \"connected\";\n            account.lastSync = new Date().toISOString();\n            this.accounts.set(accountId, account);\n            this.emit(\"account_status_change\", {\n                accountId,\n                status\n            });\n        }\n    }\n    // 获取账户状态\n    getAccountStatus(accountId) {\n        return this.accounts.get(accountId);\n    }\n    // 获取所有账户\n    getAllAccounts() {\n        return Array.from(this.accounts.values());\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RealtimeEmailService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/realtime-email-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0229964a418d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9nbG9iYWxzLmNzcz9mOGNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDIyOTk2NGE0MThkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"邮箱客户端\",\n    description: \"支持多邮箱的网页版邮件客户端\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFDOUIsNEVBQUNVO2dCQUFJRCxXQUFVOzBCQUNaSjs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfpgq7nrrHlrqLmiLfnq68nLFxuICBkZXNjcmlwdGlvbjogJ+aUr+aMgeWkmumCrueuseeahOe9kemhteeJiOmCruS7tuWuouaIt+errycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\cloud\email\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/lucide-react","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();