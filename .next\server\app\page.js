/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2Nsb3VkJTVDZW1haWwlNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbWFpbC1jbGllbnQvPzhiNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,CheckCircle,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gmail-client */ \"(ssr)/./src/lib/gmail-client.ts\");\n/* harmony import */ var _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/realtime-email-service */ \"(ssr)/./src/lib/realtime-email-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    const [gmailClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"]());\n    const [isLoadingEmails, setIsLoadingEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__[\"default\"]());\n    const [isServiceConnected, setIsServiceConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeEmails, setRealtimeEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notificationsEnabled, setNotificationsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从localStorage加载账户\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAccounts = ()=>{\n            try {\n                const saved = localStorage.getItem(\"emailAccounts\");\n                if (saved) {\n                    const accounts = JSON.parse(saved);\n                    setEmailAccounts(accounts);\n                    if (accounts.length > 0 && !activeAccountId) {\n                        setActiveAccountId(accounts[0].id);\n                    }\n                } else {\n                    // 默认示例账户\n                    const defaultAccounts = [\n                        {\n                            id: \"1\",\n                            email: \"<EMAIL>\",\n                            provider: \"gmail\",\n                            name: \"个人Gmail\",\n                            isActive: true,\n                            unreadCount: 3\n                        }\n                    ];\n                    setEmailAccounts(defaultAccounts);\n                    setActiveAccountId(\"1\");\n                }\n            } catch (error) {\n                console.error(\"Error loading accounts:\", error);\n            }\n        };\n        loadAccounts();\n    }, []);\n    // 初始化实时邮件服务\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initRealtimeService = async ()=>{\n            try {\n                // 连接到实时服务\n                const connected = await realtimeService.connect();\n                setIsServiceConnected(connected);\n                // 设置事件监听\n                realtimeService.on(\"service_status\", (data)=>{\n                    setIsServiceConnected(data.status === \"connected\");\n                });\n                realtimeService.on(\"new_email\", (email)=>{\n                    console.log(\"收到新邮件:\", email.subject);\n                    setRealtimeEmails((prev)=>[\n                            email,\n                            ...prev\n                        ]);\n                    // 更新未读计数\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === email.accountId ? {\n                                ...acc,\n                                unreadCount: acc.unreadCount + 1\n                            } : acc));\n                });\n                realtimeService.on(\"account_status_change\", (data)=>{\n                    console.log(\"账户状态变化:\", data);\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === data.accountId ? {\n                                ...acc,\n                                isConnected: data.status === \"connected\"\n                            } : acc));\n                });\n                realtimeService.on(\"service_error\", (data)=>{\n                    console.error(\"服务错误:\", data.error);\n                    setIsServiceConnected(false);\n                });\n                // 请求通知权限\n                const notificationGranted = await realtimeService.requestNotificationPermission();\n                setNotificationsEnabled(notificationGranted);\n            } catch (error) {\n                console.error(\"初始化实时服务失败:\", error);\n                setIsServiceConnected(false);\n            }\n        };\n        initRealtimeService();\n        // 清理函数\n        return ()=>{\n            realtimeService.disconnect();\n        };\n    }, []);\n    // 保存账户到localStorage\n    const saveAccounts = (accounts)=>{\n        try {\n            localStorage.setItem(\"emailAccounts\", JSON.stringify(accounts));\n            setEmailAccounts(accounts);\n        } catch (error) {\n            console.error(\"Error saving accounts:\", error);\n        }\n    };\n    // 模拟邮件数据\n    const mockEmails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 合并实时邮件和模拟邮件\n    const allEmails = [\n        ...realtimeEmails.map((email)=>({\n                id: parseInt(email.id),\n                from: email.from,\n                subject: email.subject,\n                preview: email.snippet,\n                time: new Date(email.date).toLocaleTimeString(\"zh-CN\", {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\"\n                }),\n                unread: email.isUnread,\n                starred: email.isStarred,\n                accountId: email.accountId\n            })),\n        ...mockEmails\n    ];\n    // 获取当前账户的邮件\n    const currentEmails = allEmails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n        setShowAccountMenu(null) // 关闭菜单\n        ;\n    };\n    // 删除邮箱账户\n    const deleteAccount = (accountId)=>{\n        if (emailAccounts.length <= 1) {\n            alert(\"至少需要保留一个邮箱账户\");\n            return;\n        }\n        if (confirm(\"确定要删除这个邮箱账户吗？\")) {\n            const updatedAccounts = emailAccounts.filter((acc)=>acc.id !== accountId);\n            saveAccounts(updatedAccounts);\n            // 如果删除的是当前活跃账户，切换到第一个可用账户\n            if (accountId === activeAccountId) {\n                if (updatedAccounts.length > 0) {\n                    setActiveAccountId(updatedAccounts[0].id);\n                }\n            }\n            setShowAccountMenu(null);\n            setSelectedEmail(null);\n        }\n    };\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showAccountMenu) {\n                setShowAccountMenu(null);\n            }\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        return ()=>document.removeEventListener(\"click\", handleClickOutside);\n    }, [\n        showAccountMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-800\",\n                                                children: \"邮箱客户端\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center gap-1 px-2 py-1 rounded-full text-xs ${isServiceConnected ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                                                children: [\n                                                    isServiceConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isServiceConnected ? \"在线\" : \"离线\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    const granted = await realtimeService.requestNotificationPermission();\n                                                    setNotificationsEnabled(granted);\n                                                },\n                                                className: `p-1 rounded ${notificationsEnabled ? \"text-blue-600 hover:bg-blue-50\" : \"text-gray-400 hover:bg-gray-50\"}`,\n                                                title: notificationsEnabled ? \"通知已开启\" : \"点击开启通知\",\n                                                children: notificationsEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `relative p-2 rounded text-sm transition-colors group ${account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>switchAccount(account.id),\n                                                    className: \"flex items-center justify-between cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getProviderIcon(account.provider)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: account.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: account.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                                    children: account.unreadCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowAccountMenu(showAccountMenu === account.id ? null : account.id);\n                                                                    },\n                                                                    className: \"p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showAccountMenu === account.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // TODO: 实现编辑功能\n                                                                setShowAccountMenu(null);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"编辑\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteAccount(account.id),\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, account.id, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: activeAccount?.name || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            activeAccount?.unreadCount && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: `p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\"} ${email.unread ? \"bg-white\" : \"bg-gray-50\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-sm ${email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"}`,\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm mb-1 ${email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"}`,\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 448,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: currentEmails.find((e)=>e.id === selectedEmail)?.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"来自: \",\n                                                            activeAccount?.name,\n                                                            \" (\",\n                                                            activeAccount?.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: [\n                                    \"当前账户: \",\n                                    activeAccount?.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"发件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" (\",\n                                                        account.email,\n                                                        \")\"\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, this),\n            showAddAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddAccountModal, {}, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 630,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n    // 添加邮箱账户弹窗组件\n    function AddAccountModal() {\n        const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n            name: \"\",\n            email: \"\",\n            provider: \"gmail\",\n            password: \"\",\n            customImap: {\n                host: \"\",\n                port: 993,\n                secure: true\n            },\n            customSmtp: {\n                host: \"\",\n                port: 587,\n                secure: false\n            }\n        });\n        const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n        const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [testingConnection, setTestingConnection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [testResult, setTestResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n        const [successfulConfig, setSuccessfulConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n        // 邮箱服务商配置\n        const getProviderConfig = (provider)=>{\n            const configs = {\n                gmail: {\n                    name: \"Gmail\",\n                    imap: {\n                        host: \"imap.gmail.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.gmail.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                outlook: {\n                    name: \"Outlook\",\n                    imap: {\n                        host: \"outlook.office365.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp-mail.outlook.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"163\": {\n                    name: \"163邮箱\",\n                    imap: {\n                        host: \"imap.163.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.163.com\",\n                        port: 465,\n                        secure: true\n                    }\n                },\n                qq: {\n                    name: \"QQ邮箱\",\n                    imap: {\n                        host: \"imap.qq.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.qq.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"126\": {\n                    name: \"126邮箱\",\n                    imap: {\n                        host: \"imap.126.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.126.com\",\n                        port: 465,\n                        secure: true\n                    }\n                },\n                sina: {\n                    name: \"新浪邮箱\",\n                    imap: {\n                        host: \"imap.sina.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.sina.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"139\": {\n                    name: \"139邮箱\",\n                    imap: {\n                        host: \"imap.139.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.139.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"yeah\": {\n                    name: \"Yeah邮箱\",\n                    imap: {\n                        host: \"imap.yeah.net\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.yeah.net\",\n                        port: 465,\n                        secure: true\n                    }\n                }\n            };\n            return configs[provider] || null;\n        };\n        // 自动检测邮箱服务商\n        const detectProvider = (email)=>{\n            const domain = email.split(\"@\")[1]?.toLowerCase();\n            const domainMap = {\n                \"gmail.com\": \"gmail\",\n                \"googlemail.com\": \"gmail\",\n                \"outlook.com\": \"outlook\",\n                \"hotmail.com\": \"outlook\",\n                \"live.com\": \"outlook\",\n                \"msn.com\": \"outlook\",\n                \"163.com\": \"163\",\n                \"126.com\": \"126\",\n                \"qq.com\": \"qq\",\n                \"foxmail.com\": \"qq\",\n                \"sina.com\": \"sina\",\n                \"sina.cn\": \"sina\",\n                \"139.com\": \"139\",\n                \"yeah.net\": \"yeah\"\n            };\n            return domainMap[domain] || \"other\";\n        };\n        // 处理邮箱地址变化\n        const handleEmailChange = (email)=>{\n            const provider = detectProvider(email);\n            const config = getProviderConfig(provider);\n            setFormData((prev)=>({\n                    ...prev,\n                    email,\n                    provider,\n                    name: prev.name || `${config?.name || provider} - ${email}`,\n                    customImap: config?.imap || prev.customImap,\n                    customSmtp: config?.smtp || prev.customSmtp\n                }));\n            // 如果是未知邮箱，自动显示高级设置\n            if (provider === \"other\") {\n                setShowAdvanced(true);\n            }\n        };\n        // 获取认证错误建议\n        const getAuthenticationErrorSuggestion = (provider, email)=>{\n            switch(provider){\n                case \"gmail\":\n                    return `Gmail需要使用应用专用密码：\n1. 开启Google账户两步验证\n2. 生成应用专用密码\n3. 使用16位应用专用密码，不是登录密码\n详细步骤：https://support.google.com/accounts/answer/185833`;\n                case \"qq\":\n                    return `QQ邮箱需要使用授权码：\n1. 登录QQ邮箱网页版\n2. 设置 → 账户 → 开启IMAP/SMTP服务\n3. 获取授权码（通过短信验证）\n4. 使用授权码，不是QQ密码`;\n                case \"163\":\n                    return `163邮箱需要客户端授权密码：\n1. 登录163邮箱网页版\n2. 设置 → POP3/SMTP/IMAP → 开启服务\n3. 设置客户端授权密码\n4. 使用客户端授权密码，不是登录密码`;\n                case \"126\":\n                    return `126邮箱需要客户端授权密码：\n1. 登录126邮箱网页版\n2. 设置 → POP3/SMTP/IMAP → 开启服务\n3. 设置客户端授权密码\n4. 使用客户端授权密码，不是登录密码`;\n                case \"outlook\":\n                    return `Outlook邮箱建议：\n1. 尝试使用登录密码\n2. 如果失败，生成应用专用密码\n3. 确保开启了IMAP访问权限\n4. 检查Microsoft账户安全设置`;\n                case \"sina\":\n                    return `新浪邮箱需要：\n1. 登录新浪邮箱网页版\n2. 设置 → 邮箱设置 → 开启IMAP/SMTP\n3. 可能需要设置独立密码\n4. 使用独立密码或登录密码`;\n                default:\n                    const domain = email.split(\"@\")[1];\n                    return `${domain} 邮箱认证失败，请检查：\n1. 是否开启了IMAP/SMTP服务\n2. 是否需要使用应用专用密码或授权码\n3. 密码是否正确\n4. 是否需要在邮箱设置中允许第三方客户端访问`;\n            }\n        };\n        // 获取高级认证建议\n        const getAdvancedAuthSuggestion = (provider, email)=>{\n            const domain = email.split(\"@\")[1];\n            return `${domain} 邮箱认证方法不兼容，请尝试：\n\n🔧 服务器配置检查：\n1. 确认IMAP服务器地址和端口正确\n2. 检查是否需要使用SSL/TLS或STARTTLS\n3. 确认服务器支持的认证方法\n\n🔐 认证设置：\n1. 尝试使用应用专用密码而不是登录密码\n2. 检查邮箱是否开启了\"安全性较低的应用访问\"\n3. 确认是否需要在邮箱设置中启用IMAP访问\n\n🌐 企业邮箱特殊设置：\n1. 联系IT管理员确认服务器配置\n2. 可能需要VPN或内网访问\n3. 检查是否有特殊的认证要求\n\n💡 常见解决方案：\n• 尝试不同的端口：993(SSL)、143(STARTTLS)\n• 检查防火墙是否阻止连接\n• 确认邮箱服务商是否支持第三方客户端`;\n        };\n        // 测试IMAP连接\n        const testImapConnection = async ()=>{\n            if (!formData.email || !formData.password || !formData.customImap.host) {\n                setTestResult({\n                    success: false,\n                    message: \"请先填写邮箱地址、密码和IMAP服务器信息\"\n                });\n                return;\n            }\n            setTestingConnection(true);\n            setTestResult(null);\n            try {\n                const result = await realtimeService.testEmailConnection(formData.email, formData.password, formData.provider, formData.customImap);\n                setTestResult({\n                    success: result.success,\n                    message: result.success ? result.message || \"连接成功！\" : result.error || \"连接失败\",\n                    config: result.success ? result.config : null\n                });\n                // 如果测试成功，保存成功的配置\n                if (result.success && result.config) {\n                    setSuccessfulConfig(result.config);\n                    console.log(\"保存成功的配置:\", result.config);\n                }\n            } catch (error) {\n                setTestResult({\n                    success: false,\n                    message: `测试失败: ${error}`\n                });\n            } finally{\n                setTestingConnection(false);\n            }\n        };\n        const handleSubmit = async (e)=>{\n            e.preventDefault();\n            setIsSubmitting(true);\n            setError(\"\");\n            // 验证表单\n            if (!formData.email || !formData.password) {\n                setError(\"请填写邮箱地址和密码\");\n                setIsSubmitting(false);\n                return;\n            }\n            // 检查邮箱是否已存在\n            if (emailAccounts.some((acc)=>acc.email === formData.email)) {\n                setError(\"该邮箱账户已存在\");\n                setIsSubmitting(false);\n                return;\n            }\n            try {\n                // 如果之前测试成功过，直接跳过测试\n                if (successfulConfig) {\n                    console.log(\"\\uD83C\\uDFAF 跳过测试，直接使用成功的配置:\", successfulConfig);\n                    setError(\"✅ 使用测试成功的配置，跳过重新测试...\");\n                // 直接进入添加流程，不进行任何测试\n                } else {\n                    // 先测试连接\n                    setError(\"正在测试连接...\");\n                    const testResult = await realtimeService.testEmailConnection(formData.email, formData.password, formData.provider, formData.customImap);\n                    if (!testResult.success) {\n                        // 提供详细的错误分析和解决建议\n                        const errorMessage = testResult.error || \"连接测试失败\";\n                        let suggestion = \"\";\n                        if (errorMessage.includes(\"Authentication failed\") || errorMessage.includes(\"No supported authentication method\")) {\n                            suggestion = getAuthenticationErrorSuggestion(formData.provider, formData.email);\n                        } else if (errorMessage.includes(\"ENOTFOUND\") || errorMessage.includes(\"ECONNREFUSED\")) {\n                            suggestion = \"服务器地址可能不正确，请检查IMAP服务器设置\";\n                        } else if (errorMessage.includes(\"timeout\")) {\n                            suggestion = \"连接超时，请检查网络连接和服务器端口\";\n                        } else if (errorMessage.includes(\"CERT\") || errorMessage.includes(\"certificate\")) {\n                            suggestion = \"证书验证失败，可能是服务器证书问题。请检查服务器配置或联系邮箱服务商\";\n                        } else if (errorMessage.includes(\"所有认证方法都失败\")) {\n                            suggestion = getAdvancedAuthSuggestion(formData.provider, formData.email);\n                        }\n                        setError(`${errorMessage}${suggestion ? \"\\n\\n\\uD83D\\uDCA1 建议：\" + suggestion : \"\"}`);\n                        setIsSubmitting(false);\n                        return;\n                    }\n                }\n                // 添加账户\n                setError(\"正在添加账户...\");\n                const newAccount = {\n                    id: Date.now().toString(),\n                    email: formData.email,\n                    provider: formData.provider,\n                    name: formData.name || `${formData.provider} - ${formData.email}`,\n                    isActive: false,\n                    unreadCount: 0,\n                    isConnected: false\n                };\n                // 根据是否有成功配置选择不同的添加方法\n                let addResult;\n                if (successfulConfig) {\n                    console.log(\"\\uD83C\\uDFAF 使用直接添加方法，跳过所有测试\");\n                    addResult = await realtimeService.addAccountDirect(newAccount, successfulConfig);\n                } else {\n                    console.log(\"\\uD83D\\uDD04 使用常规添加方法，包含测试\");\n                    addResult = await realtimeService.addAccount({\n                        ...newAccount,\n                        customImap: formData.customImap,\n                        customSmtp: formData.customSmtp\n                    });\n                }\n                if (!addResult.success) {\n                    setError(addResult.error || \"添加账户失败\");\n                    setIsSubmitting(false);\n                    return;\n                }\n                // 保存到本地存储\n                const updatedAccounts = [\n                    ...emailAccounts,\n                    newAccount\n                ];\n                saveAccounts(updatedAccounts);\n                // 关闭弹窗并重置表单\n                setShowAddAccount(false);\n                setFormData({\n                    name: \"\",\n                    email: \"\",\n                    provider: \"gmail\",\n                    password: \"\"\n                });\n                // 切换到新添加的账户\n                setActiveAccountId(newAccount.id);\n            } catch (err) {\n                setError(err.message || \"添加账户失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg w-full max-w-md mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"添加邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddAccount(false),\n                                className: \"text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 996,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 992,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 990,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"whitespace-pre-line\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1006,\n                                                    columnNumber: 21\n                                                }, this),\n                                                error.includes(\"Authentication failed\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 pt-2 border-t border-red-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/troubleshoot\",\n                                                        target: \"_blank\",\n                                                        className: \"inline-flex items-center gap-1 text-red-600 hover:text-red-800 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1014,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"查看详细解决方案\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1008,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1005,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1002,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"邮箱地址 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1026,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1025,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: formData.email,\n                                        onChange: (e)=>handleEmailChange(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1028,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: formData.provider !== \"other\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: [\n                                                \"✓ 自动识别为 \",\n                                                getProviderConfig(formData.provider)?.name || formData.provider\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1039,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-orange-600\",\n                                            children: \"⚠ 未识别的邮箱，请手动配置IMAP/SMTP设置\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 1043,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1037,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"账户名称 (可选)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1052,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"自动生成或自定义名称\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1055,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"密码/应用专用密码 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1065,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: formData.password,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    password: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"输入密码或应用专用密码\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1068,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-xs text-gray-600\",\n                                        children: [\n                                            formData.provider === \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-yellow-800\",\n                                                        children: \"Gmail用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1079,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-yellow-700\",\n                                                        children: '需要开启两步验证并生成\"应用专用密码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1080,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"qq\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border border-blue-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-blue-800\",\n                                                        children: \"QQ邮箱用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-700\",\n                                                        children: '需要开启IMAP/SMTP服务并使用\"授权码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1086,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1084,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"163\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-green-800\",\n                                                        children: \"163邮箱用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1091,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700\",\n                                                        children: '需要开启IMAP/SMTP服务并使用\"客户端授权密码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1090,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"outlook\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 border border-purple-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-purple-800\",\n                                                        children: \"Outlook用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1097,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-700\",\n                                                        children: \"可直接使用登录密码，或使用应用专用密码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1098,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1096,\n                                                columnNumber: 19\n                                            }, this),\n                                            ![\n                                                \"gmail\",\n                                                \"qq\",\n                                                \"163\",\n                                                \"outlook\"\n                                            ].includes(formData.provider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"建议使用应用专用密码以提高安全性\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1102,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1076,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1064,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAdvanced(!showAdvanced),\n                                        className: \"flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    showAdvanced ? \"隐藏\" : \"显示\",\n                                                    \"高级设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `transform transition-transform ${showAdvanced ? \"rotate-180\" : \"\"}`,\n                                                children: \"▼\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1111,\n                                        columnNumber: 15\n                                    }, this),\n                                    showAdvanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-y-4 bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                children: \"IMAP/SMTP 服务器配置\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-xs font-medium text-gray-600 mb-1\",\n                                                            children: \"IMAP服务器 (接收邮件)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1131,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1130,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.customImap.host,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                ...prev.customImap,\n                                                                                host: e.target.value\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"imap.example.com\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1136,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"服务器地址\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1146,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: formData.customImap.port,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                ...prev.customImap,\n                                                                                port: parseInt(e.target.value) || 993\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"993\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"端口\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1148,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.customImap.secure ? \"ssl\" : \"starttls\",\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                ...prev.customImap,\n                                                                                secure: e.target.value === \"ssl\"\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ssl\",\n                                                                        children: \"SSL/TLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1170,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"starttls\",\n                                                                        children: \"STARTTLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1171,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1162,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"加密方式\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1173,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1161,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1129,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-xs font-medium text-gray-600 mb-1\",\n                                                            children: \"SMTP服务器 (发送邮件)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1180,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.customSmtp.host,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customSmtp: {\n                                                                                ...prev.customSmtp,\n                                                                                host: e.target.value\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"smtp.example.com\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1185,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"服务器地址\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1184,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: formData.customSmtp.port,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customSmtp: {\n                                                                                ...prev.customSmtp,\n                                                                                port: parseInt(e.target.value) || 587\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"587\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1198,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"端口\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1208,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1197,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.customSmtp.secure ? \"ssl\" : \"starttls\",\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customSmtp: {\n                                                                                ...prev.customSmtp,\n                                                                                secure: e.target.value === \"ssl\"\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ssl\",\n                                                                        children: \"SSL/TLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1219,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"starttls\",\n                                                                        children: \"STARTTLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1220,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1211,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"加密方式\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1222,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1210,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 border border-gray-200 rounded p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: \"连接测试\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1229,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: testImapConnection,\n                                                                disabled: testingConnection || !formData.email || !formData.password || !formData.customImap.host,\n                                                                className: \"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1\",\n                                                                children: testingConnection ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1238,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"测试中...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1243,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"测试连接\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1230,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1228,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    testResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `p-2 rounded text-xs ${testResult.success ? \"bg-green-50 border border-green-200 text-green-700\" : \"bg-red-50 border border-red-200 text-red-700\"}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start gap-2\",\n                                                            children: [\n                                                                testResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"w-3 h-3 mt-0.5 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1258,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"w-3 h-3 mt-0.5 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1260,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"whitespace-pre-line\",\n                                                                            children: testResult.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1263,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        testResult.success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"mt-1 text-green-600 font-medium\",\n                                                                            children: '✅ 配置已保存，点击\"测试连接并添加\"将使用此配置'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 1265,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1251,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 mt-2\",\n                                                        children: \"测试IMAP服务器连接和认证是否正常\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1274,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border border-blue-200 rounded p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-blue-800 mb-2\",\n                                                        children: \"快速配置：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1281,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                host: \"imap.mxhichina.com\",\n                                                                                port: 993,\n                                                                                secure: true\n                                                                            },\n                                                                            customSmtp: {\n                                                                                host: \"smtp.mxhichina.com\",\n                                                                                port: 465,\n                                                                                secure: true\n                                                                            }\n                                                                        })),\n                                                                className: \"text-left px-2 py-1 text-xs bg-white border border-blue-300 rounded hover:bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"阿里云邮箱:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1292,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" IMAP: imap.mxhichina.com:993, SMTP: smtp.mxhichina.com:465\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1283,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                type: \"button\",\n                                                                onClick: ()=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                host: \"imap.exmail.qq.com\",\n                                                                                port: 993,\n                                                                                secure: true\n                                                                            },\n                                                                            customSmtp: {\n                                                                                host: \"smtp.exmail.qq.com\",\n                                                                                port: 465,\n                                                                                secure: true\n                                                                            }\n                                                                        })),\n                                                                className: \"text-left px-2 py-1 text-xs bg-white border border-blue-300 rounded hover:bg-blue-50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"腾讯企业邮箱:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1303,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" IMAP: imap.exmail.qq.com:993, SMTP: smtp.exmail.qq.com:465\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1294,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1282,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-600 mt-2\",\n                                                        children: '点击上方按钮可快速填充配置，然后点击\"测试连接\"验证'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1280,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1123,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1110,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddAccount(false),\n                                        className: \"flex-1 px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                        disabled: isSubmitting,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || !formData.email || !formData.password,\n                                        className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1330,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"连接中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1331,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_CheckCircle_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1335,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: successfulConfig ? \"使用测试配置添加\" : \"测试连接并添加\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1323,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 989,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 988,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/gmail-client.ts":
/*!*********************************!*\
  !*** ./src/lib/gmail-client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GmailClient: () => (/* binding */ GmailClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// 前端Gmail API客户端\nclass GmailClient {\n    constructor(){\n        this.baseUrl = \"http://localhost:3000\" || 0;\n    }\n    // 获取Google OAuth授权URL\n    async getAuthUrl() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`);\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to get auth URL\");\n            }\n            return data.authUrl;\n        } catch (error) {\n            console.error(\"Error getting auth URL:\", error);\n            throw error;\n        }\n    }\n    // 处理OAuth回调\n    async handleAuthCallback(code) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Authentication failed\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error handling auth callback:\", error);\n            throw error;\n        }\n    }\n    // 获取邮件列表\n    async getMessages(accessToken, maxResults = 20, query) {\n        try {\n            const url = new URL(`${this.baseUrl}/api/gmail/messages`);\n            url.searchParams.set(\"maxResults\", maxResults.toString());\n            if (query) {\n                url.searchParams.set(\"query\", query);\n            }\n            const response = await fetch(url.toString(), {\n                headers: {\n                    \"Authorization\": `Bearer ${accessToken}`\n                }\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to fetch messages\");\n            }\n            return data.emails;\n        } catch (error) {\n            console.error(\"Error fetching messages:\", error);\n            throw error;\n        }\n    }\n    // 发送邮件\n    async sendMessage(accessToken, to, subject, body, from) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    to,\n                    subject,\n                    body,\n                    from\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to send message\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            throw error;\n        }\n    }\n    // 标记邮件为已读\n    async markAsRead(accessToken, messageId) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"markAsRead\"\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to mark as read\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error marking as read:\", error);\n            throw error;\n        }\n    }\n    // 切换星标\n    async toggleStar(accessToken, messageId, isStarred) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"toggleStar\",\n                    value: isStarred\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to toggle star\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error toggling star:\", error);\n            throw error;\n        }\n    }\n    // 启动OAuth流程\n    startOAuthFlow() {\n        this.getAuthUrl().then((authUrl)=>{\n            // 在新窗口中打开授权页面\n            const popup = window.open(authUrl, \"gmail-auth\", \"width=500,height=600,scrollbars=yes,resizable=yes\");\n            // 监听授权完成\n            const checkClosed = setInterval(()=>{\n                if (popup?.closed) {\n                    clearInterval(checkClosed);\n                    // 可以在这里触发刷新或重新获取数据\n                    window.location.reload();\n                }\n            }, 1000);\n        }).catch((error)=>{\n            console.error(\"Failed to start OAuth flow:\", error);\n            alert(\"无法启动Gmail授权流程，请稍后重试\");\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GmailClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/gmail-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/realtime-email-service.ts":
/*!*******************************************!*\
  !*** ./src/lib/realtime-email-service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeEmailService: () => (/* binding */ RealtimeEmailService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n// 实时邮件服务 - 支持持续监听和实时推送\n\nclass RealtimeEmailService {\n    constructor(){\n        this.socket = null;\n        this.accounts = new Map();\n        this.listeners = new Map();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.initializeSocket();\n    }\n    // 初始化WebSocket连接\n    initializeSocket() {\n        try {\n            // 检查是否在浏览器环境中\n            if (true) {\n                console.log(\"服务器端渲染，跳过Socket初始化\");\n                return;\n            }\n            // 自动检测当前主机地址\n            const socketUrl = `${window.location.protocol}//${window.location.host}`;\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(socketUrl, {\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                autoConnect: false\n            });\n            this.socket.on(\"connect\", ()=>{\n                console.log(\"✅ 实时邮件服务已连接\");\n                this.reconnectAttempts = 0;\n                this.emit(\"service_status\", {\n                    status: \"connected\"\n                });\n            });\n            this.socket.on(\"disconnect\", ()=>{\n                console.log(\"❌ 实时邮件服务已断开\");\n                this.emit(\"service_status\", {\n                    status: \"disconnected\"\n                });\n                this.handleReconnect();\n            });\n            this.socket.on(\"new_email\", (data)=>{\n                console.log(\"\\uD83D\\uDCE7 收到新邮件:\", data.subject);\n                this.handleNewEmail(data);\n            });\n            this.socket.on(\"email_status_change\", (data)=>{\n                this.emit(\"email_status_change\", data);\n            });\n            this.socket.on(\"account_status\", (data)=>{\n                this.updateAccountStatus(data.accountId, data.status);\n            });\n            this.socket.on(\"error\", (error)=>{\n                console.error(\"Socket错误:\", error);\n                this.emit(\"service_error\", {\n                    error: error.message\n                });\n            });\n        } catch (error) {\n            console.error(\"初始化Socket失败:\", error);\n        }\n    }\n    // 连接到实时服务\n    async connect() {\n        try {\n            if (!this.socket) {\n                this.initializeSocket();\n            }\n            this.socket?.connect();\n            return true;\n        } catch (error) {\n            console.error(\"连接失败:\", error);\n            return false;\n        }\n    }\n    // 断开连接\n    disconnect() {\n        this.socket?.disconnect();\n    }\n    // 测试邮箱连接\n    async testEmailConnection(email, password, provider, customImap) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                resolve({\n                    success: false,\n                    error: \"服务未连接\"\n                });\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                resolve({\n                    success: false,\n                    error: \"测试连接超时\"\n                });\n            }, 15000);\n            this.socket.emit(\"test_email_connection\", {\n                email,\n                password,\n                provider,\n                customImap\n            });\n            this.socket.once(\"test_connection_result\", (data)=>{\n                clearTimeout(timeout);\n                resolve(data);\n            });\n        });\n    }\n    // 直接添加邮箱账户（跳过测试）\n    async addAccountDirect(account, successfulConfig) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                resolve({\n                    success: false,\n                    error: \"服务未连接\"\n                });\n                return;\n            }\n            // 保存账户信息\n            this.accounts.set(account.id, {\n                ...account,\n                isConnected: false\n            });\n            const timeout = setTimeout(()=>{\n                resolve({\n                    success: false,\n                    error: \"添加账户超时\"\n                });\n            }, 20000);\n            // 使用成功的配置\n            const imapConfig = successfulConfig?.imap || account.customImap;\n            const smtpConfig = successfulConfig?.smtp || account.customSmtp;\n            console.log(\"\\uD83D\\uDE80 直接添加账户，使用配置:\", {\n                imapConfig,\n                smtpConfig\n            });\n            this.socket.emit(\"add_email_account_direct\", {\n                accountId: account.id,\n                email: account.email,\n                password: account.password,\n                provider: account.provider,\n                name: account.name,\n                customImap: imapConfig,\n                customSmtp: smtpConfig,\n                skipTest: true // 标记跳过测试\n            });\n            this.socket.once(\"add_account_result\", (data)=>{\n                clearTimeout(timeout);\n                resolve(data);\n            });\n        });\n    }\n    // 添加邮箱账户并开始监听\n    async addAccount(account) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                resolve({\n                    success: false,\n                    error: \"服务未连接\"\n                });\n                return;\n            }\n            // 保存账户信息\n            this.accounts.set(account.id, {\n                ...account,\n                isConnected: false\n            });\n            const timeout = setTimeout(()=>{\n                resolve({\n                    success: false,\n                    error: \"添加账户超时\"\n                });\n            }, 20000);\n            this.socket.emit(\"add_email_account\", {\n                accountId: account.id,\n                email: account.email,\n                password: account.password,\n                provider: account.provider,\n                name: account.name,\n                customImap: account.customImap,\n                customSmtp: account.customSmtp\n            });\n            this.socket.once(\"add_account_result\", (data)=>{\n                clearTimeout(timeout);\n                resolve(data);\n            });\n        });\n    }\n    // 移除邮箱账户\n    removeAccount(accountId) {\n        this.accounts.delete(accountId);\n        this.socket?.emit(\"remove_account\", {\n            accountId\n        });\n    }\n    // 获取邮件列表\n    async getEmails(accountId, limit = 50) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"获取邮件超时\"));\n            }, 10000);\n            this.socket.emit(\"get_emails\", {\n                accountId,\n                limit\n            });\n            this.socket.once(\"emails_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 发送邮件\n    async sendEmail(accountId, to, subject, body) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"发送邮件超时\"));\n            }, 30000);\n            this.socket.emit(\"send_email\", {\n                accountId,\n                to,\n                subject,\n                body\n            });\n            this.socket.once(\"send_email_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(true);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 标记邮件为已读\n    async markAsRead(accountId, emailId) {\n        this.socket?.emit(\"mark_as_read\", {\n            accountId,\n            emailId\n        });\n        return true;\n    }\n    // 切换星标\n    async toggleStar(accountId, emailId, starred) {\n        this.socket?.emit(\"toggle_star\", {\n            accountId,\n            emailId,\n            starred\n        });\n        return true;\n    }\n    // 搜索邮件\n    async searchEmails(accountId, query) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"搜索超时\"));\n            }, 15000);\n            this.socket.emit(\"search_emails\", {\n                accountId,\n                query\n            });\n            this.socket.once(\"search_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 事件监听\n    on(event, callback) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        this.listeners.get(event)?.push(callback);\n    }\n    // 移除事件监听\n    off(event, callback) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        }\n    }\n    // 触发事件\n    emit(event, data) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            callbacks.forEach((callback)=>callback(data));\n        }\n    }\n    // 处理新邮件\n    handleNewEmail(email) {\n        // 更新未读计数\n        const account = this.accounts.get(email.accountId);\n        if (account) {\n            account.unreadCount += 1;\n            this.accounts.set(email.accountId, account);\n        }\n        // 显示桌面通知\n        this.showDesktopNotification(email);\n        // 触发新邮件事件\n        this.emit(\"new_email\", email);\n    }\n    // 显示桌面通知\n    showDesktopNotification(email) {\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(`新邮件 - ${email.from}`, {\n                body: email.subject,\n                icon: \"/favicon.ico\",\n                tag: email.id\n            });\n        }\n    }\n    // 请求通知权限\n    async requestNotificationPermission() {\n        if (\"Notification\" in window) {\n            const permission = await Notification.requestPermission();\n            return permission === \"granted\";\n        }\n        return false;\n    }\n    // 处理重连\n    handleReconnect() {\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.reconnectAttempts++;\n            console.log(`尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n            setTimeout(()=>{\n                this.socket?.connect();\n            }, Math.pow(2, this.reconnectAttempts) * 1000) // 指数退避\n            ;\n        } else {\n            console.error(\"重连失败，已达到最大尝试次数\");\n            this.emit(\"service_error\", {\n                error: \"连接失败，请刷新页面重试\"\n            });\n        }\n    }\n    // 验证账户\n    async validateAccount(account) {\n        // 基本验证\n        if (!account.email || !account.password) {\n            return {\n                success: false,\n                error: \"邮箱地址和密码不能为空\"\n            };\n        }\n        if (!account.email.includes(\"@\")) {\n            return {\n                success: false,\n                error: \"邮箱地址格式不正确\"\n            };\n        }\n        return {\n            success: true\n        };\n    }\n    // 获取IMAP配置\n    getImapConfig(provider) {\n        const configs = {\n            gmail: {\n                host: \"imap.gmail.com\",\n                port: 993,\n                secure: true\n            },\n            outlook: {\n                host: \"outlook.office365.com\",\n                port: 993,\n                secure: true\n            },\n            \"163\": {\n                host: \"imap.163.com\",\n                port: 993,\n                secure: true\n            },\n            qq: {\n                host: \"imap.qq.com\",\n                port: 993,\n                secure: true\n            },\n            \"126\": {\n                host: \"imap.126.com\",\n                port: 993,\n                secure: true\n            },\n            sina: {\n                host: \"imap.sina.com\",\n                port: 993,\n                secure: true\n            }\n        };\n        return configs[provider] || null;\n    }\n    // 更新账户状态\n    updateAccountStatus(accountId, status) {\n        const account = this.accounts.get(accountId);\n        if (account) {\n            account.isConnected = status === \"connected\";\n            account.lastSync = new Date().toISOString();\n            this.accounts.set(accountId, account);\n            this.emit(\"account_status_change\", {\n                accountId,\n                status\n            });\n        }\n    }\n    // 获取账户状态\n    getAccountStatus(accountId) {\n        return this.accounts.get(accountId);\n    }\n    // 获取所有账户\n    getAllAccounts() {\n        return Array.from(this.accounts.values());\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RealtimeEmailService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/realtime-email-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0229964a418d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9nbG9iYWxzLmNzcz9mOGNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDIyOTk2NGE0MThkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"邮箱客户端\",\n    description: \"支持多邮箱的网页版邮件客户端\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFDOUIsNEVBQUNVO2dCQUFJRCxXQUFVOzBCQUNaSjs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfpgq7nrrHlrqLmiLfnq68nLFxuICBkZXNjcmlwdGlvbjogJ+aUr+aMgeWkmumCrueuseeahOe9kemhteeJiOmCruS7tuWuouaIt+errycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\cloud\email\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/lucide-react","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();