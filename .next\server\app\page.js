/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?ed2e":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?abbf":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUQlM0ElNUNjbG91ZCU1Q2VtYWlsJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDY2xvdWQlNUNlbWFpbCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLGFBQWEsc0JBQXNCO0FBQ2lFO0FBQ3JDO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyx1QkFBdUIsZ0pBQXlFO0FBQ2hHO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBMkU7QUFDcEcsb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2VtYWlsLWNsaWVudC8/MDU5YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpLCBcIkQ6XFxcXGNsb3VkXFxcXGVtYWlsXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIiksIFwiRDpcXFxcY2xvdWRcXFxcZW1haWxcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCJdO1xuZXhwb3J0IHsgdHJlZSwgcGFnZXMgfTtcbmV4cG9ydCB7IGRlZmF1bHQgYXMgR2xvYmFsRXJyb3IgfSBmcm9tIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5XCI7XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9wYWdlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9cIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5Ccloud%5Cemail%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true! ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q2Nsb3VkJTVDZW1haWwlNUNzcmMlNUNhcHAlNUNwYWdlLnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbWFpbC1jbGllbnQvPzhiNGQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxjbG91ZFxcXFxlbWFpbFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5Ccloud%5Cemail%5Csrc%5Capp%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Bell,BellOff,Check,Edit,ExternalLink,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gmail-client */ \"(ssr)/./src/lib/gmail-client.ts\");\n/* harmony import */ var _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/realtime-email-service */ \"(ssr)/./src/lib/realtime-email-service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    const [gmailClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"]());\n    const [isLoadingEmails, setIsLoadingEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_realtime_email_service__WEBPACK_IMPORTED_MODULE_3__[\"default\"]());\n    const [isServiceConnected, setIsServiceConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [realtimeEmails, setRealtimeEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [notificationsEnabled, setNotificationsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从localStorage加载账户\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAccounts = ()=>{\n            try {\n                const saved = localStorage.getItem(\"emailAccounts\");\n                if (saved) {\n                    const accounts = JSON.parse(saved);\n                    setEmailAccounts(accounts);\n                    if (accounts.length > 0 && !activeAccountId) {\n                        setActiveAccountId(accounts[0].id);\n                    }\n                } else {\n                    // 默认示例账户\n                    const defaultAccounts = [\n                        {\n                            id: \"1\",\n                            email: \"<EMAIL>\",\n                            provider: \"gmail\",\n                            name: \"个人Gmail\",\n                            isActive: true,\n                            unreadCount: 3\n                        }\n                    ];\n                    setEmailAccounts(defaultAccounts);\n                    setActiveAccountId(\"1\");\n                }\n            } catch (error) {\n                console.error(\"Error loading accounts:\", error);\n            }\n        };\n        loadAccounts();\n    }, []);\n    // 初始化实时邮件服务\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initRealtimeService = async ()=>{\n            try {\n                // 连接到实时服务\n                const connected = await realtimeService.connect();\n                setIsServiceConnected(connected);\n                // 设置事件监听\n                realtimeService.on(\"service_status\", (data)=>{\n                    setIsServiceConnected(data.status === \"connected\");\n                });\n                realtimeService.on(\"new_email\", (email)=>{\n                    console.log(\"收到新邮件:\", email.subject);\n                    setRealtimeEmails((prev)=>[\n                            email,\n                            ...prev\n                        ]);\n                    // 更新未读计数\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === email.accountId ? {\n                                ...acc,\n                                unreadCount: acc.unreadCount + 1\n                            } : acc));\n                });\n                realtimeService.on(\"account_status_change\", (data)=>{\n                    console.log(\"账户状态变化:\", data);\n                    setEmailAccounts((prev)=>prev.map((acc)=>acc.id === data.accountId ? {\n                                ...acc,\n                                isConnected: data.status === \"connected\"\n                            } : acc));\n                });\n                realtimeService.on(\"service_error\", (data)=>{\n                    console.error(\"服务错误:\", data.error);\n                    setIsServiceConnected(false);\n                });\n                // 请求通知权限\n                const notificationGranted = await realtimeService.requestNotificationPermission();\n                setNotificationsEnabled(notificationGranted);\n            } catch (error) {\n                console.error(\"初始化实时服务失败:\", error);\n                setIsServiceConnected(false);\n            }\n        };\n        initRealtimeService();\n        // 清理函数\n        return ()=>{\n            realtimeService.disconnect();\n        };\n    }, []);\n    // 保存账户到localStorage\n    const saveAccounts = (accounts)=>{\n        try {\n            localStorage.setItem(\"emailAccounts\", JSON.stringify(accounts));\n            setEmailAccounts(accounts);\n        } catch (error) {\n            console.error(\"Error saving accounts:\", error);\n        }\n    };\n    // 模拟邮件数据\n    const mockEmails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 合并实时邮件和模拟邮件\n    const allEmails = [\n        ...realtimeEmails.map((email)=>({\n                id: parseInt(email.id),\n                from: email.from,\n                subject: email.subject,\n                preview: email.snippet,\n                time: new Date(email.date).toLocaleTimeString(\"zh-CN\", {\n                    hour: \"2-digit\",\n                    minute: \"2-digit\"\n                }),\n                unread: email.isUnread,\n                starred: email.isStarred,\n                accountId: email.accountId\n            })),\n        ...mockEmails\n    ];\n    // 获取当前账户的邮件\n    const currentEmails = allEmails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n        setShowAccountMenu(null) // 关闭菜单\n        ;\n    };\n    // 删除邮箱账户\n    const deleteAccount = (accountId)=>{\n        if (emailAccounts.length <= 1) {\n            alert(\"至少需要保留一个邮箱账户\");\n            return;\n        }\n        if (confirm(\"确定要删除这个邮箱账户吗？\")) {\n            const updatedAccounts = emailAccounts.filter((acc)=>acc.id !== accountId);\n            saveAccounts(updatedAccounts);\n            // 如果删除的是当前活跃账户，切换到第一个可用账户\n            if (accountId === activeAccountId) {\n                if (updatedAccounts.length > 0) {\n                    setActiveAccountId(updatedAccounts[0].id);\n                }\n            }\n            setShowAccountMenu(null);\n            setSelectedEmail(null);\n        }\n    };\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showAccountMenu) {\n                setShowAccountMenu(null);\n            }\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        return ()=>document.removeEventListener(\"click\", handleClickOutside);\n    }, [\n        showAccountMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-semibold text-gray-800\",\n                                                children: \"邮箱客户端\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center gap-1 px-2 py-1 rounded-full text-xs ${isServiceConnected ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                                                children: [\n                                                    isServiceConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-3 h-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isServiceConnected ? \"在线\" : \"离线\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    const granted = await realtimeService.requestNotificationPermission();\n                                                    setNotificationsEnabled(granted);\n                                                },\n                                                className: `p-1 rounded ${notificationsEnabled ? \"text-blue-600 hover:bg-blue-50\" : \"text-gray-400 hover:bg-gray-50\"}`,\n                                                title: notificationsEnabled ? \"通知已开启\" : \"点击开启通知\",\n                                                children: notificationsEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `relative p-2 rounded text-sm transition-colors group ${account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>switchAccount(account.id),\n                                                    className: \"flex items-center justify-between cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getProviderIcon(account.provider)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 343,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: account.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 345,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: account.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 346,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                                    children: account.unreadCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowAccountMenu(showAccountMenu === account.id ? null : account.id);\n                                                                    },\n                                                                    className: \"p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showAccountMenu === account.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // TODO: 实现编辑功能\n                                                                setShowAccountMenu(null);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"编辑\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteAccount(account.id),\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, account.id, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: activeAccount?.name || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            activeAccount?.unreadCount && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: `p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\"} ${email.unread ? \"bg-white\" : \"bg-gray-50\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-sm ${email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"}`,\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `text-sm mb-1 ${email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"}`,\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 448,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: currentEmails.find((e)=>e.id === selectedEmail)?.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: currentEmails.find((e)=>e.id === selectedEmail)?.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"来自: \",\n                                                            activeAccount?.name,\n                                                            \" (\",\n                                                            activeAccount?.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: [\n                                    \"当前账户: \",\n                                    activeAccount?.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 504,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"发件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" (\",\n                                                        account.email,\n                                                        \")\"\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 562,\n                columnNumber: 9\n            }, this),\n            showAddAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddAccountModal, {}, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 630,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n    // 添加邮箱账户弹窗组件\n    function AddAccountModal() {\n        const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n            name: \"\",\n            email: \"\",\n            provider: \"gmail\",\n            password: \"\",\n            customImap: {\n                host: \"\",\n                port: 993,\n                secure: true\n            },\n            customSmtp: {\n                host: \"\",\n                port: 587,\n                secure: false\n            }\n        });\n        const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n        const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        // 邮箱服务商配置\n        const getProviderConfig = (provider)=>{\n            const configs = {\n                gmail: {\n                    name: \"Gmail\",\n                    imap: {\n                        host: \"imap.gmail.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.gmail.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                outlook: {\n                    name: \"Outlook\",\n                    imap: {\n                        host: \"outlook.office365.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp-mail.outlook.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"163\": {\n                    name: \"163邮箱\",\n                    imap: {\n                        host: \"imap.163.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.163.com\",\n                        port: 465,\n                        secure: true\n                    }\n                },\n                qq: {\n                    name: \"QQ邮箱\",\n                    imap: {\n                        host: \"imap.qq.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.qq.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"126\": {\n                    name: \"126邮箱\",\n                    imap: {\n                        host: \"imap.126.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.126.com\",\n                        port: 465,\n                        secure: true\n                    }\n                },\n                sina: {\n                    name: \"新浪邮箱\",\n                    imap: {\n                        host: \"imap.sina.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.sina.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"139\": {\n                    name: \"139邮箱\",\n                    imap: {\n                        host: \"imap.139.com\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.139.com\",\n                        port: 587,\n                        secure: false\n                    }\n                },\n                \"yeah\": {\n                    name: \"Yeah邮箱\",\n                    imap: {\n                        host: \"imap.yeah.net\",\n                        port: 993,\n                        secure: true\n                    },\n                    smtp: {\n                        host: \"smtp.yeah.net\",\n                        port: 465,\n                        secure: true\n                    }\n                }\n            };\n            return configs[provider] || null;\n        };\n        // 自动检测邮箱服务商\n        const detectProvider = (email)=>{\n            const domain = email.split(\"@\")[1]?.toLowerCase();\n            const domainMap = {\n                \"gmail.com\": \"gmail\",\n                \"googlemail.com\": \"gmail\",\n                \"outlook.com\": \"outlook\",\n                \"hotmail.com\": \"outlook\",\n                \"live.com\": \"outlook\",\n                \"msn.com\": \"outlook\",\n                \"163.com\": \"163\",\n                \"126.com\": \"126\",\n                \"qq.com\": \"qq\",\n                \"foxmail.com\": \"qq\",\n                \"sina.com\": \"sina\",\n                \"sina.cn\": \"sina\",\n                \"139.com\": \"139\",\n                \"yeah.net\": \"yeah\"\n            };\n            return domainMap[domain] || \"other\";\n        };\n        // 处理邮箱地址变化\n        const handleEmailChange = (email)=>{\n            const provider = detectProvider(email);\n            const config = getProviderConfig(provider);\n            setFormData((prev)=>({\n                    ...prev,\n                    email,\n                    provider,\n                    name: prev.name || `${config?.name || provider} - ${email}`,\n                    customImap: config?.imap || prev.customImap,\n                    customSmtp: config?.smtp || prev.customSmtp\n                }));\n            // 如果是未知邮箱，自动显示高级设置\n            if (provider === \"other\") {\n                setShowAdvanced(true);\n            }\n        };\n        // 获取认证错误建议\n        const getAuthenticationErrorSuggestion = (provider, email)=>{\n            switch(provider){\n                case \"gmail\":\n                    return `Gmail需要使用应用专用密码：\n1. 开启Google账户两步验证\n2. 生成应用专用密码\n3. 使用16位应用专用密码，不是登录密码\n详细步骤：https://support.google.com/accounts/answer/185833`;\n                case \"qq\":\n                    return `QQ邮箱需要使用授权码：\n1. 登录QQ邮箱网页版\n2. 设置 → 账户 → 开启IMAP/SMTP服务\n3. 获取授权码（通过短信验证）\n4. 使用授权码，不是QQ密码`;\n                case \"163\":\n                    return `163邮箱需要客户端授权密码：\n1. 登录163邮箱网页版\n2. 设置 → POP3/SMTP/IMAP → 开启服务\n3. 设置客户端授权密码\n4. 使用客户端授权密码，不是登录密码`;\n                case \"126\":\n                    return `126邮箱需要客户端授权密码：\n1. 登录126邮箱网页版\n2. 设置 → POP3/SMTP/IMAP → 开启服务\n3. 设置客户端授权密码\n4. 使用客户端授权密码，不是登录密码`;\n                case \"outlook\":\n                    return `Outlook邮箱建议：\n1. 尝试使用登录密码\n2. 如果失败，生成应用专用密码\n3. 确保开启了IMAP访问权限\n4. 检查Microsoft账户安全设置`;\n                case \"sina\":\n                    return `新浪邮箱需要：\n1. 登录新浪邮箱网页版\n2. 设置 → 邮箱设置 → 开启IMAP/SMTP\n3. 可能需要设置独立密码\n4. 使用独立密码或登录密码`;\n                default:\n                    const domain = email.split(\"@\")[1];\n                    return `${domain} 邮箱认证失败，请检查：\n1. 是否开启了IMAP/SMTP服务\n2. 是否需要使用应用专用密码或授权码\n3. 密码是否正确\n4. 是否需要在邮箱设置中允许第三方客户端访问`;\n            }\n        };\n        // 获取高级认证建议\n        const getAdvancedAuthSuggestion = (provider, email)=>{\n            const domain = email.split(\"@\")[1];\n            return `${domain} 邮箱认证方法不兼容，请尝试：\n\n🔧 服务器配置检查：\n1. 确认IMAP服务器地址和端口正确\n2. 检查是否需要使用SSL/TLS或STARTTLS\n3. 确认服务器支持的认证方法\n\n🔐 认证设置：\n1. 尝试使用应用专用密码而不是登录密码\n2. 检查邮箱是否开启了\"安全性较低的应用访问\"\n3. 确认是否需要在邮箱设置中启用IMAP访问\n\n🌐 企业邮箱特殊设置：\n1. 联系IT管理员确认服务器配置\n2. 可能需要VPN或内网访问\n3. 检查是否有特殊的认证要求\n\n💡 常见解决方案：\n• 尝试不同的端口：993(SSL)、143(STARTTLS)\n• 检查防火墙是否阻止连接\n• 确认邮箱服务商是否支持第三方客户端`;\n        };\n        const handleSubmit = async (e)=>{\n            e.preventDefault();\n            setIsSubmitting(true);\n            setError(\"\");\n            // 验证表单\n            if (!formData.email || !formData.password) {\n                setError(\"请填写邮箱地址和密码\");\n                setIsSubmitting(false);\n                return;\n            }\n            // 检查邮箱是否已存在\n            if (emailAccounts.some((acc)=>acc.email === formData.email)) {\n                setError(\"该邮箱账户已存在\");\n                setIsSubmitting(false);\n                return;\n            }\n            try {\n                // 先测试连接\n                setError(\"正在测试连接...\");\n                const testResult = await realtimeService.testEmailConnection(formData.email, formData.password, formData.provider, formData.customImap);\n                if (!testResult.success) {\n                    // 提供详细的错误分析和解决建议\n                    const errorMessage = testResult.error || \"连接测试失败\";\n                    let suggestion = \"\";\n                    if (errorMessage.includes(\"Authentication failed\") || errorMessage.includes(\"No supported authentication method\")) {\n                        suggestion = getAuthenticationErrorSuggestion(formData.provider, formData.email);\n                    } else if (errorMessage.includes(\"ENOTFOUND\") || errorMessage.includes(\"ECONNREFUSED\")) {\n                        suggestion = \"服务器地址可能不正确，请检查IMAP服务器设置\";\n                    } else if (errorMessage.includes(\"timeout\")) {\n                        suggestion = \"连接超时，请检查网络连接和服务器端口\";\n                    } else if (errorMessage.includes(\"CERT\") || errorMessage.includes(\"certificate\")) {\n                        suggestion = \"证书验证失败，可能是服务器证书问题。请检查服务器配置或联系邮箱服务商\";\n                    } else if (errorMessage.includes(\"所有认证方法都失败\")) {\n                        suggestion = getAdvancedAuthSuggestion(formData.provider, formData.email);\n                    }\n                    setError(`${errorMessage}${suggestion ? \"\\n\\n\\uD83D\\uDCA1 建议：\" + suggestion : \"\"}`);\n                    setIsSubmitting(false);\n                    return;\n                }\n                // 添加账户\n                setError(\"正在添加账户...\");\n                const newAccount = {\n                    id: Date.now().toString(),\n                    email: formData.email,\n                    provider: formData.provider,\n                    name: formData.name || `${formData.provider} - ${formData.email}`,\n                    isActive: false,\n                    unreadCount: 0,\n                    isConnected: false\n                };\n                const addResult = await realtimeService.addAccount({\n                    ...newAccount,\n                    customImap: formData.customImap,\n                    customSmtp: formData.customSmtp\n                });\n                if (!addResult.success) {\n                    setError(addResult.error || \"添加账户失败\");\n                    setIsSubmitting(false);\n                    return;\n                }\n                // 保存到本地存储\n                const updatedAccounts = [\n                    ...emailAccounts,\n                    newAccount\n                ];\n                saveAccounts(updatedAccounts);\n                // 关闭弹窗并重置表单\n                setShowAddAccount(false);\n                setFormData({\n                    name: \"\",\n                    email: \"\",\n                    provider: \"gmail\",\n                    password: \"\"\n                });\n                // 切换到新添加的账户\n                setActiveAccountId(newAccount.id);\n            } catch (err) {\n                setError(err.message || \"添加账户失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg w-full max-w-md mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"添加邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddAccount(false),\n                                className: \"text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 927,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"w-4 h-4 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"whitespace-pre-line\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 21\n                                                }, this),\n                                                error.includes(\"Authentication failed\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 pt-2 border-t border-red-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"/troubleshoot\",\n                                                        target: \"_blank\",\n                                                        className: \"inline-flex items-center gap-1 text-red-600 hover:text-red-800 font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 951,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"查看详细解决方案\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 946,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 942,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"邮箱地址 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 963,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: formData.email,\n                                        onChange: (e)=>handleEmailChange(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 15\n                                    }, this),\n                                    formData.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1\",\n                                        children: formData.provider !== \"other\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-green-600\",\n                                            children: [\n                                                \"✓ 自动识别为 \",\n                                                getProviderConfig(formData.provider)?.name || formData.provider\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 21\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-orange-600\",\n                                            children: \"⚠ 未识别的邮箱，请手动配置IMAP/SMTP设置\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 980,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 961,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"账户名称 (可选)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 989,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"自动生成或自定义名称\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 992,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"密码/应用专用密码 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: formData.password,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    password: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"输入密码或应用专用密码\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1005,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 text-xs text-gray-600\",\n                                        children: [\n                                            formData.provider === \"gmail\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-50 border border-yellow-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-yellow-800\",\n                                                        children: \"Gmail用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-yellow-700\",\n                                                        children: '需要开启两步验证并生成\"应用专用密码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1017,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"qq\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border border-blue-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-blue-800\",\n                                                        children: \"QQ邮箱用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1022,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-blue-700\",\n                                                        children: '需要开启IMAP/SMTP服务并使用\"授权码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1021,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"163\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-50 border border-green-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-green-800\",\n                                                        children: \"163邮箱用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1028,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-green-700\",\n                                                        children: '需要开启IMAP/SMTP服务并使用\"客户端授权密码\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1027,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.provider === \"outlook\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 border border-purple-200 rounded p-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-purple-800\",\n                                                        children: \"Outlook用户请注意：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-700\",\n                                                        children: \"可直接使用登录密码，或使用应用专用密码\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 19\n                                            }, this),\n                                            ![\n                                                \"gmail\",\n                                                \"qq\",\n                                                \"163\",\n                                                \"outlook\"\n                                            ].includes(formData.provider) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"建议使用应用专用密码以提高安全性\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1001,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAdvanced(!showAdvanced),\n                                        className: \"flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    showAdvanced ? \"隐藏\" : \"显示\",\n                                                    \"高级设置\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1053,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `transform transition-transform ${showAdvanced ? \"rotate-180\" : \"\"}`,\n                                                children: \"▼\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1054,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 15\n                                    }, this),\n                                    showAdvanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-y-4 bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-700 mb-3\",\n                                                children: \"IMAP/SMTP 服务器配置\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1061,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-xs font-medium text-gray-600 mb-1\",\n                                                            children: \"IMAP服务器 (接收邮件)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1067,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.customImap.host,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                ...prev.customImap,\n                                                                                host: e.target.value\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"imap.example.com\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1073,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"服务器地址\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1072,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: formData.customImap.port,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                ...prev.customImap,\n                                                                                port: parseInt(e.target.value) || 993\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"993\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1086,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"端口\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1085,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.customImap.secure ? \"ssl\" : \"starttls\",\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customImap: {\n                                                                                ...prev.customImap,\n                                                                                secure: e.target.value === \"ssl\"\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ssl\",\n                                                                        children: \"SSL/TLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1107,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"starttls\",\n                                                                        children: \"STARTTLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1108,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1099,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"加密方式\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1110,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1098,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1066,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"md:col-span-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-xs font-medium text-gray-600 mb-1\",\n                                                            children: \"SMTP服务器 (发送邮件)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 1117,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1116,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: formData.customSmtp.host,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customSmtp: {\n                                                                                ...prev.customSmtp,\n                                                                                host: e.target.value\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"smtp.example.com\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1122,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"服务器地址\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1132,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1121,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: formData.customSmtp.port,\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customSmtp: {\n                                                                                ...prev.customSmtp,\n                                                                                port: parseInt(e.target.value) || 587\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                placeholder: \"587\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1135,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"端口\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1145,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData.customSmtp.secure ? \"ssl\" : \"starttls\",\n                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                            ...prev,\n                                                                            customSmtp: {\n                                                                                ...prev.customSmtp,\n                                                                                secure: e.target.value === \"ssl\"\n                                                                            }\n                                                                        })),\n                                                                className: \"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ssl\",\n                                                                        children: \"SSL/TLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1156,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"starttls\",\n                                                                        children: \"STARTTLS\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1157,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1148,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-xs text-gray-500 mt-1\",\n                                                                children: \"加密方式\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1159,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-blue-50 border border-blue-200 rounded p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs font-medium text-blue-800 mb-2\",\n                                                        children: \"常用邮箱配置参考：\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1165,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-blue-700 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"企业邮箱:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1167,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" 通常使用 mail.domain.com 或 smtp.domain.com\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1167,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"阿里云邮箱:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1168,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" IMAP: imap.mxhichina.com:993, SMTP: smtp.mxhichina.com:465\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1168,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"腾讯企业邮箱:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 1169,\n                                                                        columnNumber: 28\n                                                                    }, this),\n                                                                    \" IMAP: imap.exmail.qq.com:993, SMTP: smtp.exmail.qq.com:465\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 1169,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 1166,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 1164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1060,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1047,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddAccount(false),\n                                        className: \"flex-1 px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                        disabled: isSubmitting,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting || !formData.email || !formData.password,\n                                        className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1192,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"连接中...\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Bell_BellOff_Check_Edit_ExternalLink_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1197,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"测试连接并添加\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 1198,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 1185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 1176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 937,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 926,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 925,\n            columnNumber: 7\n        }, this);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/gmail-client.ts":
/*!*********************************!*\
  !*** ./src/lib/gmail-client.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GmailClient: () => (/* binding */ GmailClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// 前端Gmail API客户端\nclass GmailClient {\n    constructor(){\n        this.baseUrl = \"http://localhost:3000\" || 0;\n    }\n    // 获取Google OAuth授权URL\n    async getAuthUrl() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`);\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to get auth URL\");\n            }\n            return data.authUrl;\n        } catch (error) {\n            console.error(\"Error getting auth URL:\", error);\n            throw error;\n        }\n    }\n    // 处理OAuth回调\n    async handleAuthCallback(code) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/auth/google`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    code\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Authentication failed\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error handling auth callback:\", error);\n            throw error;\n        }\n    }\n    // 获取邮件列表\n    async getMessages(accessToken, maxResults = 20, query) {\n        try {\n            const url = new URL(`${this.baseUrl}/api/gmail/messages`);\n            url.searchParams.set(\"maxResults\", maxResults.toString());\n            if (query) {\n                url.searchParams.set(\"query\", query);\n            }\n            const response = await fetch(url.toString(), {\n                headers: {\n                    \"Authorization\": `Bearer ${accessToken}`\n                }\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to fetch messages\");\n            }\n            return data.emails;\n        } catch (error) {\n            console.error(\"Error fetching messages:\", error);\n            throw error;\n        }\n    }\n    // 发送邮件\n    async sendMessage(accessToken, to, subject, body, from) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    to,\n                    subject,\n                    body,\n                    from\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to send message\");\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            throw error;\n        }\n    }\n    // 标记邮件为已读\n    async markAsRead(accessToken, messageId) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"markAsRead\"\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to mark as read\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error marking as read:\", error);\n            throw error;\n        }\n    }\n    // 切换星标\n    async toggleStar(accessToken, messageId, isStarred) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/gmail/messages/${messageId}`, {\n                method: \"PATCH\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${accessToken}`\n                },\n                body: JSON.stringify({\n                    action: \"toggleStar\",\n                    value: isStarred\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to toggle star\");\n            }\n            return data.success;\n        } catch (error) {\n            console.error(\"Error toggling star:\", error);\n            throw error;\n        }\n    }\n    // 启动OAuth流程\n    startOAuthFlow() {\n        this.getAuthUrl().then((authUrl)=>{\n            // 在新窗口中打开授权页面\n            const popup = window.open(authUrl, \"gmail-auth\", \"width=500,height=600,scrollbars=yes,resizable=yes\");\n            // 监听授权完成\n            const checkClosed = setInterval(()=>{\n                if (popup?.closed) {\n                    clearInterval(checkClosed);\n                    // 可以在这里触发刷新或重新获取数据\n                    window.location.reload();\n                }\n            }, 1000);\n        }).catch((error)=>{\n            console.error(\"Failed to start OAuth flow:\", error);\n            alert(\"无法启动Gmail授权流程，请稍后重试\");\n        });\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GmailClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/gmail-client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/realtime-email-service.ts":
/*!*******************************************!*\
  !*** ./src/lib/realtime-email-service.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealtimeEmailService: () => (/* binding */ RealtimeEmailService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n// 实时邮件服务 - 支持持续监听和实时推送\n\nclass RealtimeEmailService {\n    constructor(){\n        this.socket = null;\n        this.accounts = new Map();\n        this.listeners = new Map();\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.initializeSocket();\n    }\n    // 初始化WebSocket连接\n    initializeSocket() {\n        try {\n            // 检查是否在浏览器环境中\n            if (true) {\n                console.log(\"服务器端渲染，跳过Socket初始化\");\n                return;\n            }\n            // 自动检测当前主机地址\n            const socketUrl = `${window.location.protocol}//${window.location.host}`;\n            this.socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_0__.io)(socketUrl, {\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                autoConnect: false\n            });\n            this.socket.on(\"connect\", ()=>{\n                console.log(\"✅ 实时邮件服务已连接\");\n                this.reconnectAttempts = 0;\n                this.emit(\"service_status\", {\n                    status: \"connected\"\n                });\n            });\n            this.socket.on(\"disconnect\", ()=>{\n                console.log(\"❌ 实时邮件服务已断开\");\n                this.emit(\"service_status\", {\n                    status: \"disconnected\"\n                });\n                this.handleReconnect();\n            });\n            this.socket.on(\"new_email\", (data)=>{\n                console.log(\"\\uD83D\\uDCE7 收到新邮件:\", data.subject);\n                this.handleNewEmail(data);\n            });\n            this.socket.on(\"email_status_change\", (data)=>{\n                this.emit(\"email_status_change\", data);\n            });\n            this.socket.on(\"account_status\", (data)=>{\n                this.updateAccountStatus(data.accountId, data.status);\n            });\n            this.socket.on(\"error\", (error)=>{\n                console.error(\"Socket错误:\", error);\n                this.emit(\"service_error\", {\n                    error: error.message\n                });\n            });\n        } catch (error) {\n            console.error(\"初始化Socket失败:\", error);\n        }\n    }\n    // 连接到实时服务\n    async connect() {\n        try {\n            if (!this.socket) {\n                this.initializeSocket();\n            }\n            this.socket?.connect();\n            return true;\n        } catch (error) {\n            console.error(\"连接失败:\", error);\n            return false;\n        }\n    }\n    // 断开连接\n    disconnect() {\n        this.socket?.disconnect();\n    }\n    // 测试邮箱连接\n    async testEmailConnection(email, password, provider, customImap) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                resolve({\n                    success: false,\n                    error: \"服务未连接\"\n                });\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                resolve({\n                    success: false,\n                    error: \"测试连接超时\"\n                });\n            }, 15000);\n            this.socket.emit(\"test_email_connection\", {\n                email,\n                password,\n                provider,\n                customImap\n            });\n            this.socket.once(\"test_connection_result\", (data)=>{\n                clearTimeout(timeout);\n                resolve(data);\n            });\n        });\n    }\n    // 添加邮箱账户并开始监听\n    async addAccount(account) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                resolve({\n                    success: false,\n                    error: \"服务未连接\"\n                });\n                return;\n            }\n            // 保存账户信息\n            this.accounts.set(account.id, {\n                ...account,\n                isConnected: false\n            });\n            const timeout = setTimeout(()=>{\n                resolve({\n                    success: false,\n                    error: \"添加账户超时\"\n                });\n            }, 20000);\n            this.socket.emit(\"add_email_account\", {\n                accountId: account.id,\n                email: account.email,\n                password: account.password,\n                provider: account.provider,\n                name: account.name,\n                customImap: account.customImap,\n                customSmtp: account.customSmtp\n            });\n            this.socket.once(\"add_account_result\", (data)=>{\n                clearTimeout(timeout);\n                resolve(data);\n            });\n        });\n    }\n    // 移除邮箱账户\n    removeAccount(accountId) {\n        this.accounts.delete(accountId);\n        this.socket?.emit(\"remove_account\", {\n            accountId\n        });\n    }\n    // 获取邮件列表\n    async getEmails(accountId, limit = 50) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"获取邮件超时\"));\n            }, 10000);\n            this.socket.emit(\"get_emails\", {\n                accountId,\n                limit\n            });\n            this.socket.once(\"emails_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 发送邮件\n    async sendEmail(accountId, to, subject, body) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"发送邮件超时\"));\n            }, 30000);\n            this.socket.emit(\"send_email\", {\n                accountId,\n                to,\n                subject,\n                body\n            });\n            this.socket.once(\"send_email_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(true);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 标记邮件为已读\n    async markAsRead(accountId, emailId) {\n        this.socket?.emit(\"mark_as_read\", {\n            accountId,\n            emailId\n        });\n        return true;\n    }\n    // 切换星标\n    async toggleStar(accountId, emailId, starred) {\n        this.socket?.emit(\"toggle_star\", {\n            accountId,\n            emailId,\n            starred\n        });\n        return true;\n    }\n    // 搜索邮件\n    async searchEmails(accountId, query) {\n        return new Promise((resolve, reject)=>{\n            if (!this.socket?.connected) {\n                reject(new Error(\"服务未连接\"));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                reject(new Error(\"搜索超时\"));\n            }, 15000);\n            this.socket.emit(\"search_emails\", {\n                accountId,\n                query\n            });\n            this.socket.once(\"search_response\", (data)=>{\n                clearTimeout(timeout);\n                if (data.success) {\n                    resolve(data.emails);\n                } else {\n                    reject(new Error(data.error));\n                }\n            });\n        });\n    }\n    // 事件监听\n    on(event, callback) {\n        if (!this.listeners.has(event)) {\n            this.listeners.set(event, []);\n        }\n        this.listeners.get(event)?.push(callback);\n    }\n    // 移除事件监听\n    off(event, callback) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            const index = callbacks.indexOf(callback);\n            if (index > -1) {\n                callbacks.splice(index, 1);\n            }\n        }\n    }\n    // 触发事件\n    emit(event, data) {\n        const callbacks = this.listeners.get(event);\n        if (callbacks) {\n            callbacks.forEach((callback)=>callback(data));\n        }\n    }\n    // 处理新邮件\n    handleNewEmail(email) {\n        // 更新未读计数\n        const account = this.accounts.get(email.accountId);\n        if (account) {\n            account.unreadCount += 1;\n            this.accounts.set(email.accountId, account);\n        }\n        // 显示桌面通知\n        this.showDesktopNotification(email);\n        // 触发新邮件事件\n        this.emit(\"new_email\", email);\n    }\n    // 显示桌面通知\n    showDesktopNotification(email) {\n        if (\"Notification\" in window && Notification.permission === \"granted\") {\n            new Notification(`新邮件 - ${email.from}`, {\n                body: email.subject,\n                icon: \"/favicon.ico\",\n                tag: email.id\n            });\n        }\n    }\n    // 请求通知权限\n    async requestNotificationPermission() {\n        if (\"Notification\" in window) {\n            const permission = await Notification.requestPermission();\n            return permission === \"granted\";\n        }\n        return false;\n    }\n    // 处理重连\n    handleReconnect() {\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.reconnectAttempts++;\n            console.log(`尝试重连... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n            setTimeout(()=>{\n                this.socket?.connect();\n            }, Math.pow(2, this.reconnectAttempts) * 1000) // 指数退避\n            ;\n        } else {\n            console.error(\"重连失败，已达到最大尝试次数\");\n            this.emit(\"service_error\", {\n                error: \"连接失败，请刷新页面重试\"\n            });\n        }\n    }\n    // 验证账户\n    async validateAccount(account) {\n        // 基本验证\n        if (!account.email || !account.password) {\n            return {\n                success: false,\n                error: \"邮箱地址和密码不能为空\"\n            };\n        }\n        if (!account.email.includes(\"@\")) {\n            return {\n                success: false,\n                error: \"邮箱地址格式不正确\"\n            };\n        }\n        return {\n            success: true\n        };\n    }\n    // 获取IMAP配置\n    getImapConfig(provider) {\n        const configs = {\n            gmail: {\n                host: \"imap.gmail.com\",\n                port: 993,\n                secure: true\n            },\n            outlook: {\n                host: \"outlook.office365.com\",\n                port: 993,\n                secure: true\n            },\n            \"163\": {\n                host: \"imap.163.com\",\n                port: 993,\n                secure: true\n            },\n            qq: {\n                host: \"imap.qq.com\",\n                port: 993,\n                secure: true\n            },\n            \"126\": {\n                host: \"imap.126.com\",\n                port: 993,\n                secure: true\n            },\n            sina: {\n                host: \"imap.sina.com\",\n                port: 993,\n                secure: true\n            }\n        };\n        return configs[provider] || null;\n    }\n    // 更新账户状态\n    updateAccountStatus(accountId, status) {\n        const account = this.accounts.get(accountId);\n        if (account) {\n            account.isConnected = status === \"connected\";\n            account.lastSync = new Date().toISOString();\n            this.accounts.set(accountId, account);\n            this.emit(\"account_status_change\", {\n                accountId,\n                status\n            });\n        }\n    }\n    // 获取账户状态\n    getAccountStatus(accountId) {\n        return this.accounts.get(accountId);\n    }\n    // 获取所有账户\n    getAllAccounts() {\n        return Array.from(this.accounts.values());\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RealtimeEmailService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/realtime-email-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0229964a418d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9nbG9iYWxzLmNzcz9mOGNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMDIyOTk2NGE0MThkXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"邮箱客户端\",\n    description: \"支持多邮箱的网页版邮件客户端\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGZ0I7QUFJZixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwrSkFBZTtzQkFDOUIsNEVBQUNVO2dCQUFJRCxXQUFVOzBCQUNaSjs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICfpgq7nrrHlrqLmiLfnq68nLFxuICBkZXNjcmlwdGlvbjogJ+aUr+aMgeWkmumCrueuseeahOe9kemhteeJiOmCruS7tuWuouaIt+errycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTUwXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\cloud\email\src\app\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/engine.io-client","vendor-chunks/lucide-react","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/@swc","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();