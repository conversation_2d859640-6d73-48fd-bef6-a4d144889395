"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bignumber.js";
exports.ids = ["vendor-chunks/bignumber.js"];
exports.modules = {

/***/ "(rsc)/./node_modules/bignumber.js/bignumber.js":
/*!************************************************!*\
  !*** ./node_modules/bignumber.js/bignumber.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_RESULT__;\n(function(globalObject) {\n    \"use strict\";\n    /*\r\n *      bignumber.js v9.3.1\r\n *      A JavaScript library for arbitrary-precision arithmetic.\r\n *      https://github.com/MikeMcl/bignumber.js\r\n *      Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>\r\n *      MIT Licensed.\r\n *\r\n *      BigNumber.prototype methods     |  BigNumber methods\r\n *                                      |\r\n *      absoluteValue            abs    |  clone\r\n *      comparedTo                      |  config               set\r\n *      decimalPlaces            dp     |      DECIMAL_PLACES\r\n *      dividedBy                div    |      ROUNDING_MODE\r\n *      dividedToIntegerBy       idiv   |      EXPONENTIAL_AT\r\n *      exponentiatedBy          pow    |      RANGE\r\n *      integerValue                    |      CRYPTO\r\n *      isEqualTo                eq     |      MODULO_MODE\r\n *      isFinite                        |      POW_PRECISION\r\n *      isGreaterThan            gt     |      FORMAT\r\n *      isGreaterThanOrEqualTo   gte    |      ALPHABET\r\n *      isInteger                       |  isBigNumber\r\n *      isLessThan               lt     |  maximum              max\r\n *      isLessThanOrEqualTo      lte    |  minimum              min\r\n *      isNaN                           |  random\r\n *      isNegative                      |  sum\r\n *      isPositive                      |\r\n *      isZero                          |\r\n *      minus                           |\r\n *      modulo                   mod    |\r\n *      multipliedBy             times  |\r\n *      negated                         |\r\n *      plus                            |\r\n *      precision                sd     |\r\n *      shiftedBy                       |\r\n *      squareRoot               sqrt   |\r\n *      toExponential                   |\r\n *      toFixed                         |\r\n *      toFormat                        |\r\n *      toFraction                      |\r\n *      toJSON                          |\r\n *      toNumber                        |\r\n *      toPrecision                     |\r\n *      toString                        |\r\n *      valueOf                         |\r\n *\r\n */ var BigNumber, isNumeric = /^-?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?$/i, mathceil = Math.ceil, mathfloor = Math.floor, bignumberError = \"[BigNumber Error] \", tooManyDigits = bignumberError + \"Number primitive has more than 15 significant digits: \", BASE = 1e14, LOG_BASE = 14, MAX_SAFE_INTEGER = 0x1fffffffffffff, // MAX_INT32 = 0x7fffffff,                   // 2^31 - 1\n    POWS_TEN = [\n        1,\n        10,\n        100,\n        1e3,\n        1e4,\n        1e5,\n        1e6,\n        1e7,\n        1e8,\n        1e9,\n        1e10,\n        1e11,\n        1e12,\n        1e13\n    ], SQRT_BASE = 1e7, // EDITABLE\n    // The limit on the value of DECIMAL_PLACES, TO_EXP_NEG, TO_EXP_POS, MIN_EXP, MAX_EXP, and\n    // the arguments to toExponential, toFixed, toFormat, and toPrecision.\n    MAX = 1E9; // 0 to MAX_INT32\n    /*\r\n   * Create and return a BigNumber constructor.\r\n   */ function clone(configObject) {\n        var div, convertBase, parseNumeric, P = BigNumber.prototype = {\n            constructor: BigNumber,\n            toString: null,\n            valueOf: null\n        }, ONE = new BigNumber(1), //----------------------------- EDITABLE CONFIG DEFAULTS -------------------------------\n        // The default values below must be integers within the inclusive ranges stated.\n        // The values can also be changed at run-time using BigNumber.set.\n        // The maximum number of decimal places for operations involving division.\n        DECIMAL_PLACES = 20, // The rounding mode used when rounding to the above decimal places, and when using\n        // toExponential, toFixed, toFormat and toPrecision, and round (default value).\n        // UP         0 Away from zero.\n        // DOWN       1 Towards zero.\n        // CEIL       2 Towards +Infinity.\n        // FLOOR      3 Towards -Infinity.\n        // HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n        // HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n        // HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n        // HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n        // HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n        ROUNDING_MODE = 4, // EXPONENTIAL_AT : [TO_EXP_NEG , TO_EXP_POS]\n        // The exponent value at and beneath which toString returns exponential notation.\n        // Number type: -7\n        TO_EXP_NEG = -7, // The exponent value at and above which toString returns exponential notation.\n        // Number type: 21\n        TO_EXP_POS = 21, // RANGE : [MIN_EXP, MAX_EXP]\n        // The minimum exponent value, beneath which underflow to zero occurs.\n        // Number type: -324  (5e-324)\n        MIN_EXP = -1e7, // The maximum exponent value, above which overflow to Infinity occurs.\n        // Number type:  308  (1.7976931348623157e+308)\n        // For MAX_EXP > 1e7, e.g. new BigNumber('1e100000000').plus(1) may be slow.\n        MAX_EXP = 1e7, // Whether to use cryptographically-secure random number generation, if available.\n        CRYPTO = false, // The modulo mode used when calculating the modulus: a mod n.\n        // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\n        // The remainder (r) is calculated as: r = a - n * q.\n        //\n        // UP        0 The remainder is positive if the dividend is negative, else is negative.\n        // DOWN      1 The remainder has the same sign as the dividend.\n        //             This modulo mode is commonly known as 'truncated division' and is\n        //             equivalent to (a % n) in JavaScript.\n        // FLOOR     3 The remainder has the same sign as the divisor (Python %).\n        // HALF_EVEN 6 This modulo mode implements the IEEE 754 remainder function.\n        // EUCLID    9 Euclidian division. q = sign(n) * floor(a / abs(n)).\n        //             The remainder is always positive.\n        //\n        // The truncated division, floored division, Euclidian division and IEEE 754 remainder\n        // modes are commonly used for the modulus operation.\n        // Although the other rounding modes can also be used, they may not give useful results.\n        MODULO_MODE = 1, // The maximum number of significant digits of the result of the exponentiatedBy operation.\n        // If POW_PRECISION is 0, there will be unlimited significant digits.\n        POW_PRECISION = 0, // The format specification used by the BigNumber.prototype.toFormat method.\n        FORMAT = {\n            prefix: \"\",\n            groupSize: 3,\n            secondaryGroupSize: 0,\n            groupSeparator: \",\",\n            decimalSeparator: \".\",\n            fractionGroupSize: 0,\n            fractionGroupSeparator: \"\\xa0\",\n            suffix: \"\"\n        }, // The alphabet used for base conversion. It must be at least 2 characters long, with no '+',\n        // '-', '.', whitespace, or repeated character.\n        // '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'\n        ALPHABET = \"0123456789abcdefghijklmnopqrstuvwxyz\", alphabetHasNormalDecimalDigits = true;\n        //------------------------------------------------------------------------------------------\n        // CONSTRUCTOR\n        /*\r\n     * The BigNumber constructor and exported function.\r\n     * Create and return a new instance of a BigNumber object.\r\n     *\r\n     * v {number|string|BigNumber} A numeric value.\r\n     * [b] {number} The base of v. Integer, 2 to ALPHABET.length inclusive.\r\n     */ function BigNumber(v, b) {\n            var alphabet, c, caseChanged, e, i, isNum, len, str, x = this;\n            // Enable constructor call without `new`.\n            if (!(x instanceof BigNumber)) return new BigNumber(v, b);\n            if (b == null) {\n                if (v && v._isBigNumber === true) {\n                    x.s = v.s;\n                    if (!v.c || v.e > MAX_EXP) {\n                        x.c = x.e = null;\n                    } else if (v.e < MIN_EXP) {\n                        x.c = [\n                            x.e = 0\n                        ];\n                    } else {\n                        x.e = v.e;\n                        x.c = v.c.slice();\n                    }\n                    return;\n                }\n                if ((isNum = typeof v == \"number\") && v * 0 == 0) {\n                    // Use `1 / n` to handle minus zero also.\n                    x.s = 1 / v < 0 ? (v = -v, -1) : 1;\n                    // Fast path for integers, where n < 2147483648 (2**31).\n                    if (v === ~~v) {\n                        for(e = 0, i = v; i >= 10; i /= 10, e++);\n                        if (e > MAX_EXP) {\n                            x.c = x.e = null;\n                        } else {\n                            x.e = e;\n                            x.c = [\n                                v\n                            ];\n                        }\n                        return;\n                    }\n                    str = String(v);\n                } else {\n                    if (!isNumeric.test(str = String(v))) return parseNumeric(x, str, isNum);\n                    x.s = str.charCodeAt(0) == 45 ? (str = str.slice(1), -1) : 1;\n                }\n                // Decimal point?\n                if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n                // Exponential form?\n                if ((i = str.search(/e/i)) > 0) {\n                    // Determine exponent.\n                    if (e < 0) e = i;\n                    e += +str.slice(i + 1);\n                    str = str.substring(0, i);\n                } else if (e < 0) {\n                    // Integer.\n                    e = str.length;\n                }\n            } else {\n                // '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\n                intCheck(b, 2, ALPHABET.length, \"Base\");\n                // Allow exponential notation to be used with base 10 argument, while\n                // also rounding to DECIMAL_PLACES as with other bases.\n                if (b == 10 && alphabetHasNormalDecimalDigits) {\n                    x = new BigNumber(v);\n                    return round(x, DECIMAL_PLACES + x.e + 1, ROUNDING_MODE);\n                }\n                str = String(v);\n                if (isNum = typeof v == \"number\") {\n                    // Avoid potential interpretation of Infinity and NaN as base 44+ values.\n                    if (v * 0 != 0) return parseNumeric(x, str, isNum, b);\n                    x.s = 1 / v < 0 ? (str = str.slice(1), -1) : 1;\n                    // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\n                    if (BigNumber.DEBUG && str.replace(/^0\\.0*|\\./, \"\").length > 15) {\n                        throw Error(tooManyDigits + v);\n                    }\n                } else {\n                    x.s = str.charCodeAt(0) === 45 ? (str = str.slice(1), -1) : 1;\n                }\n                alphabet = ALPHABET.slice(0, b);\n                e = i = 0;\n                // Check that str is a valid base b number.\n                // Don't use RegExp, so alphabet can contain special characters.\n                for(len = str.length; i < len; i++){\n                    if (alphabet.indexOf(c = str.charAt(i)) < 0) {\n                        if (c == \".\") {\n                            // If '.' is not the first character and it has not be found before.\n                            if (i > e) {\n                                e = len;\n                                continue;\n                            }\n                        } else if (!caseChanged) {\n                            // Allow e.g. hexadecimal 'FF' as well as 'ff'.\n                            if (str == str.toUpperCase() && (str = str.toLowerCase()) || str == str.toLowerCase() && (str = str.toUpperCase())) {\n                                caseChanged = true;\n                                i = -1;\n                                e = 0;\n                                continue;\n                            }\n                        }\n                        return parseNumeric(x, String(v), isNum, b);\n                    }\n                }\n                // Prevent later check for length on converted number.\n                isNum = false;\n                str = convertBase(str, b, 10, x.s);\n                // Decimal point?\n                if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n                else e = str.length;\n            }\n            // Determine leading zeros.\n            for(i = 0; str.charCodeAt(i) === 48; i++);\n            // Determine trailing zeros.\n            for(len = str.length; str.charCodeAt(--len) === 48;);\n            if (str = str.slice(i, ++len)) {\n                len -= i;\n                // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\n                if (isNum && BigNumber.DEBUG && len > 15 && (v > MAX_SAFE_INTEGER || v !== mathfloor(v))) {\n                    throw Error(tooManyDigits + x.s * v);\n                }\n                // Overflow?\n                if ((e = e - i - 1) > MAX_EXP) {\n                    // Infinity.\n                    x.c = x.e = null;\n                // Underflow?\n                } else if (e < MIN_EXP) {\n                    // Zero.\n                    x.c = [\n                        x.e = 0\n                    ];\n                } else {\n                    x.e = e;\n                    x.c = [];\n                    // Transform base\n                    // e is the base 10 exponent.\n                    // i is where to slice str to get the first element of the coefficient array.\n                    i = (e + 1) % LOG_BASE;\n                    if (e < 0) i += LOG_BASE; // i < 1\n                    if (i < len) {\n                        if (i) x.c.push(+str.slice(0, i));\n                        for(len -= LOG_BASE; i < len;){\n                            x.c.push(+str.slice(i, i += LOG_BASE));\n                        }\n                        i = LOG_BASE - (str = str.slice(i)).length;\n                    } else {\n                        i -= len;\n                    }\n                    for(; i--; str += \"0\");\n                    x.c.push(+str);\n                }\n            } else {\n                // Zero.\n                x.c = [\n                    x.e = 0\n                ];\n            }\n        }\n        // CONSTRUCTOR PROPERTIES\n        BigNumber.clone = clone;\n        BigNumber.ROUND_UP = 0;\n        BigNumber.ROUND_DOWN = 1;\n        BigNumber.ROUND_CEIL = 2;\n        BigNumber.ROUND_FLOOR = 3;\n        BigNumber.ROUND_HALF_UP = 4;\n        BigNumber.ROUND_HALF_DOWN = 5;\n        BigNumber.ROUND_HALF_EVEN = 6;\n        BigNumber.ROUND_HALF_CEIL = 7;\n        BigNumber.ROUND_HALF_FLOOR = 8;\n        BigNumber.EUCLID = 9;\n        /*\r\n     * Configure infrequently-changing library-wide settings.\r\n     *\r\n     * Accept an object with the following optional properties (if the value of a property is\r\n     * a number, it must be an integer within the inclusive range stated):\r\n     *\r\n     *   DECIMAL_PLACES   {number}           0 to MAX\r\n     *   ROUNDING_MODE    {number}           0 to 8\r\n     *   EXPONENTIAL_AT   {number|number[]}  -MAX to MAX  or  [-MAX to 0, 0 to MAX]\r\n     *   RANGE            {number|number[]}  -MAX to MAX (not zero)  or  [-MAX to -1, 1 to MAX]\r\n     *   CRYPTO           {boolean}          true or false\r\n     *   MODULO_MODE      {number}           0 to 9\r\n     *   POW_PRECISION       {number}           0 to MAX\r\n     *   ALPHABET         {string}           A string of two or more unique characters which does\r\n     *                                       not contain '.'.\r\n     *   FORMAT           {object}           An object with some of the following properties:\r\n     *     prefix                 {string}\r\n     *     groupSize              {number}\r\n     *     secondaryGroupSize     {number}\r\n     *     groupSeparator         {string}\r\n     *     decimalSeparator       {string}\r\n     *     fractionGroupSize      {number}\r\n     *     fractionGroupSeparator {string}\r\n     *     suffix                 {string}\r\n     *\r\n     * (The values assigned to the above FORMAT object properties are not checked for validity.)\r\n     *\r\n     * E.g.\r\n     * BigNumber.config({ DECIMAL_PLACES : 20, ROUNDING_MODE : 4 })\r\n     *\r\n     * Ignore properties/parameters set to null or undefined, except for ALPHABET.\r\n     *\r\n     * Return an object with the properties current values.\r\n     */ BigNumber.config = BigNumber.set = function(obj) {\n            var p, v;\n            if (obj != null) {\n                if (typeof obj == \"object\") {\n                    // DECIMAL_PLACES {number} Integer, 0 to MAX inclusive.\n                    // '[BigNumber Error] DECIMAL_PLACES {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"DECIMAL_PLACES\")) {\n                        v = obj[p];\n                        intCheck(v, 0, MAX, p);\n                        DECIMAL_PLACES = v;\n                    }\n                    // ROUNDING_MODE {number} Integer, 0 to 8 inclusive.\n                    // '[BigNumber Error] ROUNDING_MODE {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"ROUNDING_MODE\")) {\n                        v = obj[p];\n                        intCheck(v, 0, 8, p);\n                        ROUNDING_MODE = v;\n                    }\n                    // EXPONENTIAL_AT {number|number[]}\n                    // Integer, -MAX to MAX inclusive or\n                    // [integer -MAX to 0 inclusive, 0 to MAX inclusive].\n                    // '[BigNumber Error] EXPONENTIAL_AT {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"EXPONENTIAL_AT\")) {\n                        v = obj[p];\n                        if (v && v.pop) {\n                            intCheck(v[0], -MAX, 0, p);\n                            intCheck(v[1], 0, MAX, p);\n                            TO_EXP_NEG = v[0];\n                            TO_EXP_POS = v[1];\n                        } else {\n                            intCheck(v, -MAX, MAX, p);\n                            TO_EXP_NEG = -(TO_EXP_POS = v < 0 ? -v : v);\n                        }\n                    }\n                    // RANGE {number|number[]} Non-zero integer, -MAX to MAX inclusive or\n                    // [integer -MAX to -1 inclusive, integer 1 to MAX inclusive].\n                    // '[BigNumber Error] RANGE {not a primitive number|not an integer|out of range|cannot be zero}: {v}'\n                    if (obj.hasOwnProperty(p = \"RANGE\")) {\n                        v = obj[p];\n                        if (v && v.pop) {\n                            intCheck(v[0], -MAX, -1, p);\n                            intCheck(v[1], 1, MAX, p);\n                            MIN_EXP = v[0];\n                            MAX_EXP = v[1];\n                        } else {\n                            intCheck(v, -MAX, MAX, p);\n                            if (v) {\n                                MIN_EXP = -(MAX_EXP = v < 0 ? -v : v);\n                            } else {\n                                throw Error(bignumberError + p + \" cannot be zero: \" + v);\n                            }\n                        }\n                    }\n                    // CRYPTO {boolean} true or false.\n                    // '[BigNumber Error] CRYPTO not true or false: {v}'\n                    // '[BigNumber Error] crypto unavailable'\n                    if (obj.hasOwnProperty(p = \"CRYPTO\")) {\n                        v = obj[p];\n                        if (v === !!v) {\n                            if (v) {\n                                if (typeof crypto != \"undefined\" && crypto && (crypto.getRandomValues || crypto.randomBytes)) {\n                                    CRYPTO = v;\n                                } else {\n                                    CRYPTO = !v;\n                                    throw Error(bignumberError + \"crypto unavailable\");\n                                }\n                            } else {\n                                CRYPTO = v;\n                            }\n                        } else {\n                            throw Error(bignumberError + p + \" not true or false: \" + v);\n                        }\n                    }\n                    // MODULO_MODE {number} Integer, 0 to 9 inclusive.\n                    // '[BigNumber Error] MODULO_MODE {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"MODULO_MODE\")) {\n                        v = obj[p];\n                        intCheck(v, 0, 9, p);\n                        MODULO_MODE = v;\n                    }\n                    // POW_PRECISION {number} Integer, 0 to MAX inclusive.\n                    // '[BigNumber Error] POW_PRECISION {not a primitive number|not an integer|out of range}: {v}'\n                    if (obj.hasOwnProperty(p = \"POW_PRECISION\")) {\n                        v = obj[p];\n                        intCheck(v, 0, MAX, p);\n                        POW_PRECISION = v;\n                    }\n                    // FORMAT {object}\n                    // '[BigNumber Error] FORMAT not an object: {v}'\n                    if (obj.hasOwnProperty(p = \"FORMAT\")) {\n                        v = obj[p];\n                        if (typeof v == \"object\") FORMAT = v;\n                        else throw Error(bignumberError + p + \" not an object: \" + v);\n                    }\n                    // ALPHABET {string}\n                    // '[BigNumber Error] ALPHABET invalid: {v}'\n                    if (obj.hasOwnProperty(p = \"ALPHABET\")) {\n                        v = obj[p];\n                        // Disallow if less than two characters,\n                        // or if it contains '+', '-', '.', whitespace, or a repeated character.\n                        if (typeof v == \"string\" && !/^.?$|[+\\-.\\s]|(.).*\\1/.test(v)) {\n                            alphabetHasNormalDecimalDigits = v.slice(0, 10) == \"0123456789\";\n                            ALPHABET = v;\n                        } else {\n                            throw Error(bignumberError + p + \" invalid: \" + v);\n                        }\n                    }\n                } else {\n                    // '[BigNumber Error] Object expected: {v}'\n                    throw Error(bignumberError + \"Object expected: \" + obj);\n                }\n            }\n            return {\n                DECIMAL_PLACES: DECIMAL_PLACES,\n                ROUNDING_MODE: ROUNDING_MODE,\n                EXPONENTIAL_AT: [\n                    TO_EXP_NEG,\n                    TO_EXP_POS\n                ],\n                RANGE: [\n                    MIN_EXP,\n                    MAX_EXP\n                ],\n                CRYPTO: CRYPTO,\n                MODULO_MODE: MODULO_MODE,\n                POW_PRECISION: POW_PRECISION,\n                FORMAT: FORMAT,\n                ALPHABET: ALPHABET\n            };\n        };\n        /*\r\n     * Return true if v is a BigNumber instance, otherwise return false.\r\n     *\r\n     * If BigNumber.DEBUG is true, throw if a BigNumber instance is not well-formed.\r\n     *\r\n     * v {any}\r\n     *\r\n     * '[BigNumber Error] Invalid BigNumber: {v}'\r\n     */ BigNumber.isBigNumber = function(v) {\n            if (!v || v._isBigNumber !== true) return false;\n            if (!BigNumber.DEBUG) return true;\n            var i, n, c = v.c, e = v.e, s = v.s;\n            out: if (({}).toString.call(c) == \"[object Array]\") {\n                if ((s === 1 || s === -1) && e >= -MAX && e <= MAX && e === mathfloor(e)) {\n                    // If the first element is zero, the BigNumber value must be zero.\n                    if (c[0] === 0) {\n                        if (e === 0 && c.length === 1) return true;\n                        break out;\n                    }\n                    // Calculate number of digits that c[0] should have, based on the exponent.\n                    i = (e + 1) % LOG_BASE;\n                    if (i < 1) i += LOG_BASE;\n                    // Calculate number of digits of c[0].\n                    //if (Math.ceil(Math.log(c[0] + 1) / Math.LN10) == i) {\n                    if (String(c[0]).length == i) {\n                        for(i = 0; i < c.length; i++){\n                            n = c[i];\n                            if (n < 0 || n >= BASE || n !== mathfloor(n)) break out;\n                        }\n                        // Last element cannot be zero, unless it is the only element.\n                        if (n !== 0) return true;\n                    }\n                }\n            // Infinity/NaN\n            } else if (c === null && e === null && (s === null || s === 1 || s === -1)) {\n                return true;\n            }\n            throw Error(bignumberError + \"Invalid BigNumber: \" + v);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the maximum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */ BigNumber.maximum = BigNumber.max = function() {\n            return maxOrMin(arguments, -1);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the minimum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */ BigNumber.minimum = BigNumber.min = function() {\n            return maxOrMin(arguments, 1);\n        };\n        /*\r\n     * Return a new BigNumber with a random value equal to or greater than 0 and less than 1,\r\n     * and with dp, or DECIMAL_PLACES if dp is omitted, decimal places (or less if trailing\r\n     * zeros are produced).\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp}'\r\n     * '[BigNumber Error] crypto unavailable'\r\n     */ BigNumber.random = function() {\n            var pow2_53 = 0x20000000000000;\n            // Return a 53 bit integer n, where 0 <= n < 9007199254740992.\n            // Check if Math.random() produces more than 32 bits of randomness.\n            // If it does, assume at least 53 bits are produced, otherwise assume at least 30 bits.\n            // 0x40000000 is 2^30, 0x800000 is 2^23, 0x1fffff is 2^21 - 1.\n            var random53bitInt = Math.random() * pow2_53 & 0x1fffff ? function() {\n                return mathfloor(Math.random() * pow2_53);\n            } : function() {\n                return (Math.random() * 0x40000000 | 0) * 0x800000 + (Math.random() * 0x800000 | 0);\n            };\n            return function(dp) {\n                var a, b, e, k, v, i = 0, c = [], rand = new BigNumber(ONE);\n                if (dp == null) dp = DECIMAL_PLACES;\n                else intCheck(dp, 0, MAX);\n                k = mathceil(dp / LOG_BASE);\n                if (CRYPTO) {\n                    // Browsers supporting crypto.getRandomValues.\n                    if (crypto.getRandomValues) {\n                        a = crypto.getRandomValues(new Uint32Array(k *= 2));\n                        for(; i < k;){\n                            // 53 bits:\n                            // ((Math.pow(2, 32) - 1) * Math.pow(2, 21)).toString(2)\n                            // 11111 11111111 11111111 11111111 11100000 00000000 00000000\n                            // ((Math.pow(2, 32) - 1) >>> 11).toString(2)\n                            //                                     11111 11111111 11111111\n                            // 0x20000 is 2^21.\n                            v = a[i] * 0x20000 + (a[i + 1] >>> 11);\n                            // Rejection sampling:\n                            // 0 <= v < 9007199254740992\n                            // Probability that v >= 9e15, is\n                            // 7199254740992 / 9007199254740992 ~= 0.0008, i.e. 1 in 1251\n                            if (v >= 9e15) {\n                                b = crypto.getRandomValues(new Uint32Array(2));\n                                a[i] = b[0];\n                                a[i + 1] = b[1];\n                            } else {\n                                // 0 <= v <= 8999999999999999\n                                // 0 <= (v % 1e14) <= 99999999999999\n                                c.push(v % 1e14);\n                                i += 2;\n                            }\n                        }\n                        i = k / 2;\n                    // Node.js supporting crypto.randomBytes.\n                    } else if (crypto.randomBytes) {\n                        // buffer\n                        a = crypto.randomBytes(k *= 7);\n                        for(; i < k;){\n                            // 0x1000000000000 is 2^48, 0x10000000000 is 2^40\n                            // 0x100000000 is 2^32, 0x1000000 is 2^24\n                            // 11111 11111111 11111111 11111111 11111111 11111111 11111111\n                            // 0 <= v < 9007199254740992\n                            v = (a[i] & 31) * 0x1000000000000 + a[i + 1] * 0x10000000000 + a[i + 2] * 0x100000000 + a[i + 3] * 0x1000000 + (a[i + 4] << 16) + (a[i + 5] << 8) + a[i + 6];\n                            if (v >= 9e15) {\n                                crypto.randomBytes(7).copy(a, i);\n                            } else {\n                                // 0 <= (v % 1e14) <= 99999999999999\n                                c.push(v % 1e14);\n                                i += 7;\n                            }\n                        }\n                        i = k / 7;\n                    } else {\n                        CRYPTO = false;\n                        throw Error(bignumberError + \"crypto unavailable\");\n                    }\n                }\n                // Use Math.random.\n                if (!CRYPTO) {\n                    for(; i < k;){\n                        v = random53bitInt();\n                        if (v < 9e15) c[i++] = v % 1e14;\n                    }\n                }\n                k = c[--i];\n                dp %= LOG_BASE;\n                // Convert trailing digits to zeros according to dp.\n                if (k && dp) {\n                    v = POWS_TEN[LOG_BASE - dp];\n                    c[i] = mathfloor(k / v) * v;\n                }\n                // Remove trailing elements which are zero.\n                for(; c[i] === 0; c.pop(), i--);\n                // Zero?\n                if (i < 0) {\n                    c = [\n                        e = 0\n                    ];\n                } else {\n                    // Remove leading elements which are zero and adjust exponent accordingly.\n                    for(e = -1; c[0] === 0; c.splice(0, 1), e -= LOG_BASE);\n                    // Count the digits of the first element of c to determine leading zeros, and...\n                    for(i = 1, v = c[0]; v >= 10; v /= 10, i++);\n                    // adjust the exponent accordingly.\n                    if (i < LOG_BASE) e -= LOG_BASE - i;\n                }\n                rand.e = e;\n                rand.c = c;\n                return rand;\n            };\n        }();\n        /*\r\n     * Return a BigNumber whose value is the sum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */ BigNumber.sum = function() {\n            var i = 1, args = arguments, sum = new BigNumber(args[0]);\n            for(; i < args.length;)sum = sum.plus(args[i++]);\n            return sum;\n        };\n        // PRIVATE FUNCTIONS\n        // Called by BigNumber and BigNumber.prototype.toString.\n        convertBase = function() {\n            var decimal = \"0123456789\";\n            /*\r\n       * Convert string of baseIn to an array of numbers of baseOut.\r\n       * Eg. toBaseOut('255', 10, 16) returns [15, 15].\r\n       * Eg. toBaseOut('ff', 16, 10) returns [2, 5, 5].\r\n       */ function toBaseOut(str, baseIn, baseOut, alphabet) {\n                var j, arr = [\n                    0\n                ], arrL, i = 0, len = str.length;\n                for(; i < len;){\n                    for(arrL = arr.length; arrL--; arr[arrL] *= baseIn);\n                    arr[0] += alphabet.indexOf(str.charAt(i++));\n                    for(j = 0; j < arr.length; j++){\n                        if (arr[j] > baseOut - 1) {\n                            if (arr[j + 1] == null) arr[j + 1] = 0;\n                            arr[j + 1] += arr[j] / baseOut | 0;\n                            arr[j] %= baseOut;\n                        }\n                    }\n                }\n                return arr.reverse();\n            }\n            // Convert a numeric string of baseIn to a numeric string of baseOut.\n            // If the caller is toString, we are converting from base 10 to baseOut.\n            // If the caller is BigNumber, we are converting from baseIn to base 10.\n            return function(str, baseIn, baseOut, sign, callerIsToString) {\n                var alphabet, d, e, k, r, x, xc, y, i = str.indexOf(\".\"), dp = DECIMAL_PLACES, rm = ROUNDING_MODE;\n                // Non-integer.\n                if (i >= 0) {\n                    k = POW_PRECISION;\n                    // Unlimited precision.\n                    POW_PRECISION = 0;\n                    str = str.replace(\".\", \"\");\n                    y = new BigNumber(baseIn);\n                    x = y.pow(str.length - i);\n                    POW_PRECISION = k;\n                    // Convert str as if an integer, then restore the fraction part by dividing the\n                    // result by its base raised to a power.\n                    y.c = toBaseOut(toFixedPoint(coeffToString(x.c), x.e, \"0\"), 10, baseOut, decimal);\n                    y.e = y.c.length;\n                }\n                // Convert the number as integer.\n                xc = toBaseOut(str, baseIn, baseOut, callerIsToString ? (alphabet = ALPHABET, decimal) : (alphabet = decimal, ALPHABET));\n                // xc now represents str as an integer and converted to baseOut. e is the exponent.\n                e = k = xc.length;\n                // Remove trailing zeros.\n                for(; xc[--k] == 0; xc.pop());\n                // Zero?\n                if (!xc[0]) return alphabet.charAt(0);\n                // Does str represent an integer? If so, no need for the division.\n                if (i < 0) {\n                    --e;\n                } else {\n                    x.c = xc;\n                    x.e = e;\n                    // The sign is needed for correct rounding.\n                    x.s = sign;\n                    x = div(x, y, dp, rm, baseOut);\n                    xc = x.c;\n                    r = x.r;\n                    e = x.e;\n                }\n                // xc now represents str converted to baseOut.\n                // The index of the rounding digit.\n                d = e + dp + 1;\n                // The rounding digit: the digit to the right of the digit that may be rounded up.\n                i = xc[d];\n                // Look at the rounding digits and mode to determine whether to round up.\n                k = baseOut / 2;\n                r = r || d < 0 || xc[d + 1] != null;\n                r = rm < 4 ? (i != null || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : i > k || i == k && (rm == 4 || r || rm == 6 && xc[d - 1] & 1 || rm == (x.s < 0 ? 8 : 7));\n                // If the index of the rounding digit is not greater than zero, or xc represents\n                // zero, then the result of the base conversion is zero or, if rounding up, a value\n                // such as 0.00001.\n                if (d < 1 || !xc[0]) {\n                    // 1^-dp or 0\n                    str = r ? toFixedPoint(alphabet.charAt(1), -dp, alphabet.charAt(0)) : alphabet.charAt(0);\n                } else {\n                    // Truncate xc to the required number of decimal places.\n                    xc.length = d;\n                    // Round up?\n                    if (r) {\n                        // Rounding up may mean the previous digit has to be rounded up and so on.\n                        for(--baseOut; ++xc[--d] > baseOut;){\n                            xc[d] = 0;\n                            if (!d) {\n                                ++e;\n                                xc = [\n                                    1\n                                ].concat(xc);\n                            }\n                        }\n                    }\n                    // Determine trailing zeros.\n                    for(k = xc.length; !xc[--k];);\n                    // E.g. [4, 11, 15] becomes 4bf.\n                    for(i = 0, str = \"\"; i <= k; str += alphabet.charAt(xc[i++]));\n                    // Add leading zeros, decimal point and trailing zeros as required.\n                    str = toFixedPoint(str, e, alphabet.charAt(0));\n                }\n                // The caller will add the sign.\n                return str;\n            };\n        }();\n        // Perform division in the specified base. Called by div and convertBase.\n        div = function() {\n            // Assume non-zero x and k.\n            function multiply(x, k, base) {\n                var m, temp, xlo, xhi, carry = 0, i = x.length, klo = k % SQRT_BASE, khi = k / SQRT_BASE | 0;\n                for(x = x.slice(); i--;){\n                    xlo = x[i] % SQRT_BASE;\n                    xhi = x[i] / SQRT_BASE | 0;\n                    m = khi * xlo + xhi * klo;\n                    temp = klo * xlo + m % SQRT_BASE * SQRT_BASE + carry;\n                    carry = (temp / base | 0) + (m / SQRT_BASE | 0) + khi * xhi;\n                    x[i] = temp % base;\n                }\n                if (carry) x = [\n                    carry\n                ].concat(x);\n                return x;\n            }\n            function compare(a, b, aL, bL) {\n                var i, cmp;\n                if (aL != bL) {\n                    cmp = aL > bL ? 1 : -1;\n                } else {\n                    for(i = cmp = 0; i < aL; i++){\n                        if (a[i] != b[i]) {\n                            cmp = a[i] > b[i] ? 1 : -1;\n                            break;\n                        }\n                    }\n                }\n                return cmp;\n            }\n            function subtract(a, b, aL, base) {\n                var i = 0;\n                // Subtract b from a.\n                for(; aL--;){\n                    a[aL] -= i;\n                    i = a[aL] < b[aL] ? 1 : 0;\n                    a[aL] = i * base + a[aL] - b[aL];\n                }\n                // Remove leading zeros.\n                for(; !a[0] && a.length > 1; a.splice(0, 1));\n            }\n            // x: dividend, y: divisor.\n            return function(x, y, dp, rm, base) {\n                var cmp, e, i, more, n, prod, prodL, q, qc, rem, remL, rem0, xi, xL, yc0, yL, yz, s = x.s == y.s ? 1 : -1, xc = x.c, yc = y.c;\n                // Either NaN, Infinity or 0?\n                if (!xc || !xc[0] || !yc || !yc[0]) {\n                    return new BigNumber(// Return NaN if either NaN, or both Infinity or 0.\n                    !x.s || !y.s || (xc ? yc && xc[0] == yc[0] : !yc) ? NaN : // Return ±0 if x is ±0 or y is ±Infinity, or return ±Infinity as y is ±0.\n                    xc && xc[0] == 0 || !yc ? s * 0 : s / 0);\n                }\n                q = new BigNumber(s);\n                qc = q.c = [];\n                e = x.e - y.e;\n                s = dp + e + 1;\n                if (!base) {\n                    base = BASE;\n                    e = bitFloor(x.e / LOG_BASE) - bitFloor(y.e / LOG_BASE);\n                    s = s / LOG_BASE | 0;\n                }\n                // Result exponent may be one less then the current value of e.\n                // The coefficients of the BigNumbers from convertBase may have trailing zeros.\n                for(i = 0; yc[i] == (xc[i] || 0); i++);\n                if (yc[i] > (xc[i] || 0)) e--;\n                if (s < 0) {\n                    qc.push(1);\n                    more = true;\n                } else {\n                    xL = xc.length;\n                    yL = yc.length;\n                    i = 0;\n                    s += 2;\n                    // Normalise xc and yc so highest order digit of yc is >= base / 2.\n                    n = mathfloor(base / (yc[0] + 1));\n                    // Not necessary, but to handle odd bases where yc[0] == (base / 2) - 1.\n                    // if (n > 1 || n++ == 1 && yc[0] < base / 2) {\n                    if (n > 1) {\n                        yc = multiply(yc, n, base);\n                        xc = multiply(xc, n, base);\n                        yL = yc.length;\n                        xL = xc.length;\n                    }\n                    xi = yL;\n                    rem = xc.slice(0, yL);\n                    remL = rem.length;\n                    // Add zeros to make remainder as long as divisor.\n                    for(; remL < yL; rem[remL++] = 0);\n                    yz = yc.slice();\n                    yz = [\n                        0\n                    ].concat(yz);\n                    yc0 = yc[0];\n                    if (yc[1] >= base / 2) yc0++;\n                    // Not necessary, but to prevent trial digit n > base, when using base 3.\n                    // else if (base == 3 && yc0 == 1) yc0 = 1 + 1e-15;\n                    do {\n                        n = 0;\n                        // Compare divisor and remainder.\n                        cmp = compare(yc, rem, yL, remL);\n                        // If divisor < remainder.\n                        if (cmp < 0) {\n                            // Calculate trial digit, n.\n                            rem0 = rem[0];\n                            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\n                            // n is how many times the divisor goes into the current remainder.\n                            n = mathfloor(rem0 / yc0);\n                            //  Algorithm:\n                            //  product = divisor multiplied by trial digit (n).\n                            //  Compare product and remainder.\n                            //  If product is greater than remainder:\n                            //    Subtract divisor from product, decrement trial digit.\n                            //  Subtract product from remainder.\n                            //  If product was less than remainder at the last compare:\n                            //    Compare new remainder and divisor.\n                            //    If remainder is greater than divisor:\n                            //      Subtract divisor from remainder, increment trial digit.\n                            if (n > 1) {\n                                // n may be > base only when base is 3.\n                                if (n >= base) n = base - 1;\n                                // product = divisor * trial digit.\n                                prod = multiply(yc, n, base);\n                                prodL = prod.length;\n                                remL = rem.length;\n                                // Compare product and remainder.\n                                // If product > remainder then trial digit n too high.\n                                // n is 1 too high about 5% of the time, and is not known to have\n                                // ever been more than 1 too high.\n                                while(compare(prod, rem, prodL, remL) == 1){\n                                    n--;\n                                    // Subtract divisor from product.\n                                    subtract(prod, yL < prodL ? yz : yc, prodL, base);\n                                    prodL = prod.length;\n                                    cmp = 1;\n                                }\n                            } else {\n                                // n is 0 or 1, cmp is -1.\n                                // If n is 0, there is no need to compare yc and rem again below,\n                                // so change cmp to 1 to avoid it.\n                                // If n is 1, leave cmp as -1, so yc and rem are compared again.\n                                if (n == 0) {\n                                    // divisor < remainder, so n must be at least 1.\n                                    cmp = n = 1;\n                                }\n                                // product = divisor\n                                prod = yc.slice();\n                                prodL = prod.length;\n                            }\n                            if (prodL < remL) prod = [\n                                0\n                            ].concat(prod);\n                            // Subtract product from remainder.\n                            subtract(rem, prod, remL, base);\n                            remL = rem.length;\n                            // If product was < remainder.\n                            if (cmp == -1) {\n                                // Compare divisor and new remainder.\n                                // If divisor < new remainder, subtract divisor from remainder.\n                                // Trial digit n too low.\n                                // n is 1 too low about 5% of the time, and very rarely 2 too low.\n                                while(compare(yc, rem, yL, remL) < 1){\n                                    n++;\n                                    // Subtract divisor from remainder.\n                                    subtract(rem, yL < remL ? yz : yc, remL, base);\n                                    remL = rem.length;\n                                }\n                            }\n                        } else if (cmp === 0) {\n                            n++;\n                            rem = [\n                                0\n                            ];\n                        } // else cmp === 1 and n will be 0\n                        // Add the next digit, n, to the result array.\n                        qc[i++] = n;\n                        // Update the remainder.\n                        if (rem[0]) {\n                            rem[remL++] = xc[xi] || 0;\n                        } else {\n                            rem = [\n                                xc[xi]\n                            ];\n                            remL = 1;\n                        }\n                    }while ((xi++ < xL || rem[0] != null) && s--);\n                    more = rem[0] != null;\n                    // Leading zero?\n                    if (!qc[0]) qc.splice(0, 1);\n                }\n                if (base == BASE) {\n                    // To calculate q.e, first get the number of digits of qc[0].\n                    for(i = 1, s = qc[0]; s >= 10; s /= 10, i++);\n                    round(q, dp + (q.e = i + e * LOG_BASE - 1) + 1, rm, more);\n                // Caller is convertBase.\n                } else {\n                    q.e = e;\n                    q.r = +more;\n                }\n                return q;\n            };\n        }();\n        /*\r\n     * Return a string representing the value of BigNumber n in fixed-point or exponential\r\n     * notation rounded to the specified decimal places or significant digits.\r\n     *\r\n     * n: a BigNumber.\r\n     * i: the index of the last digit required (i.e. the digit that may be rounded up).\r\n     * rm: the rounding mode.\r\n     * id: 1 (toExponential) or 2 (toPrecision).\r\n     */ function format(n, i, rm, id) {\n            var c0, e, ne, len, str;\n            if (rm == null) rm = ROUNDING_MODE;\n            else intCheck(rm, 0, 8);\n            if (!n.c) return n.toString();\n            c0 = n.c[0];\n            ne = n.e;\n            if (i == null) {\n                str = coeffToString(n.c);\n                str = id == 1 || id == 2 && (ne <= TO_EXP_NEG || ne >= TO_EXP_POS) ? toExponential(str, ne) : toFixedPoint(str, ne, \"0\");\n            } else {\n                n = round(new BigNumber(n), i, rm);\n                // n.e may have changed if the value was rounded up.\n                e = n.e;\n                str = coeffToString(n.c);\n                len = str.length;\n                // toPrecision returns exponential notation if the number of significant digits\n                // specified is less than the number of digits necessary to represent the integer\n                // part of the value in fixed-point notation.\n                // Exponential notation.\n                if (id == 1 || id == 2 && (i <= e || e <= TO_EXP_NEG)) {\n                    // Append zeros?\n                    for(; len < i; str += \"0\", len++);\n                    str = toExponential(str, e);\n                // Fixed-point notation.\n                } else {\n                    i -= ne + (id === 2 && e > ne);\n                    str = toFixedPoint(str, e, \"0\");\n                    // Append zeros?\n                    if (e + 1 > len) {\n                        if (--i > 0) for(str += \".\"; i--; str += \"0\");\n                    } else {\n                        i += e - len;\n                        if (i > 0) {\n                            if (e + 1 == len) str += \".\";\n                            for(; i--; str += \"0\");\n                        }\n                    }\n                }\n            }\n            return n.s < 0 && c0 ? \"-\" + str : str;\n        }\n        // Handle BigNumber.max and BigNumber.min.\n        // If any number is NaN, return NaN.\n        function maxOrMin(args, n) {\n            var k, y, i = 1, x = new BigNumber(args[0]);\n            for(; i < args.length; i++){\n                y = new BigNumber(args[i]);\n                if (!y.s || (k = compare(x, y)) === n || k === 0 && x.s === n) {\n                    x = y;\n                }\n            }\n            return x;\n        }\n        /*\r\n     * Strip trailing zeros, calculate base 10 exponent and check against MIN_EXP and MAX_EXP.\r\n     * Called by minus, plus and times.\r\n     */ function normalise(n, c, e) {\n            var i = 1, j = c.length;\n            // Remove trailing zeros.\n            for(; !c[--j]; c.pop());\n            // Calculate the base 10 exponent. First get the number of digits of c[0].\n            for(j = c[0]; j >= 10; j /= 10, i++);\n            // Overflow?\n            if ((e = i + e * LOG_BASE - 1) > MAX_EXP) {\n                // Infinity.\n                n.c = n.e = null;\n            // Underflow?\n            } else if (e < MIN_EXP) {\n                // Zero.\n                n.c = [\n                    n.e = 0\n                ];\n            } else {\n                n.e = e;\n                n.c = c;\n            }\n            return n;\n        }\n        // Handle values that fail the validity test in BigNumber.\n        parseNumeric = function() {\n            var basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i, dotAfter = /^([^.]+)\\.$/, dotBefore = /^\\.([^.]+)$/, isInfinityOrNaN = /^-?(Infinity|NaN)$/, whitespaceOrPlus = /^\\s*\\+(?=[\\w.])|^\\s+|\\s+$/g;\n            return function(x, str, isNum, b) {\n                var base, s = isNum ? str : str.replace(whitespaceOrPlus, \"\");\n                // No exception on ±Infinity or NaN.\n                if (isInfinityOrNaN.test(s)) {\n                    x.s = isNaN(s) ? null : s < 0 ? -1 : 1;\n                } else {\n                    if (!isNum) {\n                        // basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i\n                        s = s.replace(basePrefix, function(m, p1, p2) {\n                            base = (p2 = p2.toLowerCase()) == \"x\" ? 16 : p2 == \"b\" ? 2 : 8;\n                            return !b || b == base ? p1 : m;\n                        });\n                        if (b) {\n                            base = b;\n                            // E.g. '1.' to '1', '.1' to '0.1'\n                            s = s.replace(dotAfter, \"$1\").replace(dotBefore, \"0.$1\");\n                        }\n                        if (str != s) return new BigNumber(s, base);\n                    }\n                    // '[BigNumber Error] Not a number: {n}'\n                    // '[BigNumber Error] Not a base {b} number: {n}'\n                    if (BigNumber.DEBUG) {\n                        throw Error(bignumberError + \"Not a\" + (b ? \" base \" + b : \"\") + \" number: \" + str);\n                    }\n                    // NaN\n                    x.s = null;\n                }\n                x.c = x.e = null;\n            };\n        }();\n        /*\r\n     * Round x to sd significant digits using rounding mode rm. Check for over/under-flow.\r\n     * If r is truthy, it is known that there are more digits after the rounding digit.\r\n     */ function round(x, sd, rm, r) {\n            var d, i, j, k, n, ni, rd, xc = x.c, pows10 = POWS_TEN;\n            // if x is not Infinity or NaN...\n            if (xc) {\n                // rd is the rounding digit, i.e. the digit after the digit that may be rounded up.\n                // n is a base 1e14 number, the value of the element of array x.c containing rd.\n                // ni is the index of n within x.c.\n                // d is the number of digits of n.\n                // i is the index of rd within n including leading zeros.\n                // j is the actual index of rd within n (if < 0, rd is a leading zero).\n                out: {\n                    // Get the number of digits of the first element of xc.\n                    for(d = 1, k = xc[0]; k >= 10; k /= 10, d++);\n                    i = sd - d;\n                    // If the rounding digit is in the first element of xc...\n                    if (i < 0) {\n                        i += LOG_BASE;\n                        j = sd;\n                        n = xc[ni = 0];\n                        // Get the rounding digit at index j of n.\n                        rd = mathfloor(n / pows10[d - j - 1] % 10);\n                    } else {\n                        ni = mathceil((i + 1) / LOG_BASE);\n                        if (ni >= xc.length) {\n                            if (r) {\n                                // Needed by sqrt.\n                                for(; xc.length <= ni; xc.push(0));\n                                n = rd = 0;\n                                d = 1;\n                                i %= LOG_BASE;\n                                j = i - LOG_BASE + 1;\n                            } else {\n                                break out;\n                            }\n                        } else {\n                            n = k = xc[ni];\n                            // Get the number of digits of n.\n                            for(d = 1; k >= 10; k /= 10, d++);\n                            // Get the index of rd within n.\n                            i %= LOG_BASE;\n                            // Get the index of rd within n, adjusted for leading zeros.\n                            // The number of leading zeros of n is given by LOG_BASE - d.\n                            j = i - LOG_BASE + d;\n                            // Get the rounding digit at index j of n.\n                            rd = j < 0 ? 0 : mathfloor(n / pows10[d - j - 1] % 10);\n                        }\n                    }\n                    r = r || sd < 0 || // Are there any non-zero digits after the rounding digit?\n                    // The expression  n % pows10[d - j - 1]  returns all digits of n to the right\n                    // of the digit at j, e.g. if n is 908714 and j is 2, the expression gives 714.\n                    xc[ni + 1] != null || (j < 0 ? n : n % pows10[d - j - 1]);\n                    r = rm < 4 ? (rd || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || r || rm == 6 && (i > 0 ? j > 0 ? n / pows10[d - j] : 0 : xc[ni - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n                    if (sd < 1 || !xc[0]) {\n                        xc.length = 0;\n                        if (r) {\n                            // Convert sd to decimal places.\n                            sd -= x.e + 1;\n                            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n                            xc[0] = pows10[(LOG_BASE - sd % LOG_BASE) % LOG_BASE];\n                            x.e = -sd || 0;\n                        } else {\n                            // Zero.\n                            xc[0] = x.e = 0;\n                        }\n                        return x;\n                    }\n                    // Remove excess digits.\n                    if (i == 0) {\n                        xc.length = ni;\n                        k = 1;\n                        ni--;\n                    } else {\n                        xc.length = ni + 1;\n                        k = pows10[LOG_BASE - i];\n                        // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n                        // j > 0 means i > number of leading zeros of n.\n                        xc[ni] = j > 0 ? mathfloor(n / pows10[d - j] % pows10[j]) * k : 0;\n                    }\n                    // Round up?\n                    if (r) {\n                        for(;;){\n                            // If the digit to be rounded up is in the first element of xc...\n                            if (ni == 0) {\n                                // i will be the length of xc[0] before k is added.\n                                for(i = 1, j = xc[0]; j >= 10; j /= 10, i++);\n                                j = xc[0] += k;\n                                for(k = 1; j >= 10; j /= 10, k++);\n                                // if i != k the length has increased.\n                                if (i != k) {\n                                    x.e++;\n                                    if (xc[0] == BASE) xc[0] = 1;\n                                }\n                                break;\n                            } else {\n                                xc[ni] += k;\n                                if (xc[ni] != BASE) break;\n                                xc[ni--] = 0;\n                                k = 1;\n                            }\n                        }\n                    }\n                    // Remove trailing zeros.\n                    for(i = xc.length; xc[--i] === 0; xc.pop());\n                }\n                // Overflow? Infinity.\n                if (x.e > MAX_EXP) {\n                    x.c = x.e = null;\n                // Underflow? Zero.\n                } else if (x.e < MIN_EXP) {\n                    x.c = [\n                        x.e = 0\n                    ];\n                }\n            }\n            return x;\n        }\n        function valueOf(n) {\n            var str, e = n.e;\n            if (e === null) return n.toString();\n            str = coeffToString(n.c);\n            str = e <= TO_EXP_NEG || e >= TO_EXP_POS ? toExponential(str, e) : toFixedPoint(str, e, \"0\");\n            return n.s < 0 ? \"-\" + str : str;\n        }\n        // PROTOTYPE/INSTANCE METHODS\n        /*\r\n     * Return a new BigNumber whose value is the absolute value of this BigNumber.\r\n     */ P.absoluteValue = P.abs = function() {\n            var x = new BigNumber(this);\n            if (x.s < 0) x.s = 1;\n            return x;\n        };\n        /*\r\n     * Return\r\n     *   1 if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n     *   -1 if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n     *   0 if they have the same value,\r\n     *   or null if the value of either is NaN.\r\n     */ P.comparedTo = function(y, b) {\n            return compare(this, new BigNumber(y, b));\n        };\n        /*\r\n     * If dp is undefined or null or true or false, return the number of decimal places of the\r\n     * value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n     *\r\n     * Otherwise, if dp is a number, return a new BigNumber whose value is the value of this\r\n     * BigNumber rounded to a maximum of dp decimal places using rounding mode rm, or\r\n     * ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * [dp] {number} Decimal places: integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */ P.decimalPlaces = P.dp = function(dp, rm) {\n            var c, n, v, x = this;\n            if (dp != null) {\n                intCheck(dp, 0, MAX);\n                if (rm == null) rm = ROUNDING_MODE;\n                else intCheck(rm, 0, 8);\n                return round(new BigNumber(x), dp + x.e + 1, rm);\n            }\n            if (!(c = x.c)) return null;\n            n = ((v = c.length - 1) - bitFloor(this.e / LOG_BASE)) * LOG_BASE;\n            // Subtract the number of trailing zeros of the last number.\n            if (v = c[v]) for(; v % 10 == 0; v /= 10, n--);\n            if (n < 0) n = 0;\n            return n;\n        };\n        /*\r\n     *  n / 0 = I\r\n     *  n / N = N\r\n     *  n / I = 0\r\n     *  0 / n = 0\r\n     *  0 / 0 = N\r\n     *  0 / N = N\r\n     *  0 / I = 0\r\n     *  N / n = N\r\n     *  N / 0 = N\r\n     *  N / N = N\r\n     *  N / I = N\r\n     *  I / n = I\r\n     *  I / 0 = I\r\n     *  I / N = N\r\n     *  I / I = N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber divided by the value of\r\n     * BigNumber(y, b), rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     */ P.dividedBy = P.div = function(y, b) {\n            return div(this, new BigNumber(y, b), DECIMAL_PLACES, ROUNDING_MODE);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the integer part of dividing the value of this\r\n     * BigNumber by the value of BigNumber(y, b).\r\n     */ P.dividedToIntegerBy = P.idiv = function(y, b) {\n            return div(this, new BigNumber(y, b), 0, 1);\n        };\n        /*\r\n     * Return a BigNumber whose value is the value of this BigNumber exponentiated by n.\r\n     *\r\n     * If m is present, return the result modulo m.\r\n     * If n is negative round according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     * If POW_PRECISION is non-zero and m is not present, round to POW_PRECISION using ROUNDING_MODE.\r\n     *\r\n     * The modular power operation works efficiently when x, n, and m are integers, otherwise it\r\n     * is equivalent to calculating x.exponentiatedBy(n).modulo(m) with a POW_PRECISION of 0.\r\n     *\r\n     * n {number|string|BigNumber} The exponent. An integer.\r\n     * [m] {number|string|BigNumber} The modulus.\r\n     *\r\n     * '[BigNumber Error] Exponent not an integer: {n}'\r\n     */ P.exponentiatedBy = P.pow = function(n, m) {\n            var half, isModExp, i, k, more, nIsBig, nIsNeg, nIsOdd, y, x = this;\n            n = new BigNumber(n);\n            // Allow NaN and ±Infinity, but not other non-integers.\n            if (n.c && !n.isInteger()) {\n                throw Error(bignumberError + \"Exponent not an integer: \" + valueOf(n));\n            }\n            if (m != null) m = new BigNumber(m);\n            // Exponent of MAX_SAFE_INTEGER is 15.\n            nIsBig = n.e > 14;\n            // If x is NaN, ±Infinity, ±0 or ±1, or n is ±Infinity, NaN or ±0.\n            if (!x.c || !x.c[0] || x.c[0] == 1 && !x.e && x.c.length == 1 || !n.c || !n.c[0]) {\n                // The sign of the result of pow when x is negative depends on the evenness of n.\n                // If +n overflows to ±Infinity, the evenness of n would be not be known.\n                y = new BigNumber(Math.pow(+valueOf(x), nIsBig ? n.s * (2 - isOdd(n)) : +valueOf(n)));\n                return m ? y.mod(m) : y;\n            }\n            nIsNeg = n.s < 0;\n            if (m) {\n                // x % m returns NaN if abs(m) is zero, or m is NaN.\n                if (m.c ? !m.c[0] : !m.s) return new BigNumber(NaN);\n                isModExp = !nIsNeg && x.isInteger() && m.isInteger();\n                if (isModExp) x = x.mod(m);\n            // Overflow to ±Infinity: >=2**1e10 or >=1.0000024**1e15.\n            // Underflow to ±0: <=0.79**1e10 or <=0.9999975**1e15.\n            } else if (n.e > 9 && (x.e > 0 || x.e < -1 || (x.e == 0 ? x.c[0] > 1 || nIsBig && x.c[1] >= 24e7 : x.c[0] < 8e13 || nIsBig && x.c[0] <= 9999975e7))) {\n                // If x is negative and n is odd, k = -0, else k = 0.\n                k = x.s < 0 && isOdd(n) ? -0 : 0;\n                // If x >= 1, k = ±Infinity.\n                if (x.e > -1) k = 1 / k;\n                // If n is negative return ±0, else return ±Infinity.\n                return new BigNumber(nIsNeg ? 1 / k : k);\n            } else if (POW_PRECISION) {\n                // Truncating each coefficient array to a length of k after each multiplication\n                // equates to truncating significant digits to POW_PRECISION + [28, 41],\n                // i.e. there will be a minimum of 28 guard digits retained.\n                k = mathceil(POW_PRECISION / LOG_BASE + 2);\n            }\n            if (nIsBig) {\n                half = new BigNumber(0.5);\n                if (nIsNeg) n.s = 1;\n                nIsOdd = isOdd(n);\n            } else {\n                i = Math.abs(+valueOf(n));\n                nIsOdd = i % 2;\n            }\n            y = new BigNumber(ONE);\n            // Performs 54 loop iterations for n of 9007199254740991.\n            for(;;){\n                if (nIsOdd) {\n                    y = y.times(x);\n                    if (!y.c) break;\n                    if (k) {\n                        if (y.c.length > k) y.c.length = k;\n                    } else if (isModExp) {\n                        y = y.mod(m); //y = y.minus(div(y, m, 0, MODULO_MODE).times(m));\n                    }\n                }\n                if (i) {\n                    i = mathfloor(i / 2);\n                    if (i === 0) break;\n                    nIsOdd = i % 2;\n                } else {\n                    n = n.times(half);\n                    round(n, n.e + 1, 1);\n                    if (n.e > 14) {\n                        nIsOdd = isOdd(n);\n                    } else {\n                        i = +valueOf(n);\n                        if (i === 0) break;\n                        nIsOdd = i % 2;\n                    }\n                }\n                x = x.times(x);\n                if (k) {\n                    if (x.c && x.c.length > k) x.c.length = k;\n                } else if (isModExp) {\n                    x = x.mod(m); //x = x.minus(div(x, m, 0, MODULO_MODE).times(m));\n                }\n            }\n            if (isModExp) return y;\n            if (nIsNeg) y = ONE.div(y);\n            return m ? y.mod(m) : k ? round(y, POW_PRECISION, ROUNDING_MODE, more) : y;\n        };\n        /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber rounded to an integer\r\n     * using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {rm}'\r\n     */ P.integerValue = function(rm) {\n            var n = new BigNumber(this);\n            if (rm == null) rm = ROUNDING_MODE;\n            else intCheck(rm, 0, 8);\n            return round(n, n.e + 1, rm);\n        };\n        /*\r\n     * Return true if the value of this BigNumber is equal to the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */ P.isEqualTo = P.eq = function(y, b) {\n            return compare(this, new BigNumber(y, b)) === 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is a finite number, otherwise return false.\r\n     */ P.isFinite = function() {\n            return !!this.c;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */ P.isGreaterThan = P.gt = function(y, b) {\n            return compare(this, new BigNumber(y, b)) > 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is greater than or equal to the value of\r\n     * BigNumber(y, b), otherwise return false.\r\n     */ P.isGreaterThanOrEqualTo = P.gte = function(y, b) {\n            return (b = compare(this, new BigNumber(y, b))) === 1 || b === 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is an integer, otherwise return false.\r\n     */ P.isInteger = function() {\n            return !!this.c && bitFloor(this.e / LOG_BASE) > this.c.length - 2;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */ P.isLessThan = P.lt = function(y, b) {\n            return compare(this, new BigNumber(y, b)) < 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is less than or equal to the value of\r\n     * BigNumber(y, b), otherwise return false.\r\n     */ P.isLessThanOrEqualTo = P.lte = function(y, b) {\n            return (b = compare(this, new BigNumber(y, b))) === -1 || b === 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is NaN, otherwise return false.\r\n     */ P.isNaN = function() {\n            return !this.s;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is negative, otherwise return false.\r\n     */ P.isNegative = function() {\n            return this.s < 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is positive, otherwise return false.\r\n     */ P.isPositive = function() {\n            return this.s > 0;\n        };\n        /*\r\n     * Return true if the value of this BigNumber is 0 or -0, otherwise return false.\r\n     */ P.isZero = function() {\n            return !!this.c && this.c[0] == 0;\n        };\n        /*\r\n     *  n - 0 = n\r\n     *  n - N = N\r\n     *  n - I = -I\r\n     *  0 - n = -n\r\n     *  0 - 0 = 0\r\n     *  0 - N = N\r\n     *  0 - I = -I\r\n     *  N - n = N\r\n     *  N - 0 = N\r\n     *  N - N = N\r\n     *  N - I = N\r\n     *  I - n = I\r\n     *  I - 0 = I\r\n     *  I - N = N\r\n     *  I - I = N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber minus the value of\r\n     * BigNumber(y, b).\r\n     */ P.minus = function(y, b) {\n            var i, j, t, xLTy, x = this, a = x.s;\n            y = new BigNumber(y, b);\n            b = y.s;\n            // Either NaN?\n            if (!a || !b) return new BigNumber(NaN);\n            // Signs differ?\n            if (a != b) {\n                y.s = -b;\n                return x.plus(y);\n            }\n            var xe = x.e / LOG_BASE, ye = y.e / LOG_BASE, xc = x.c, yc = y.c;\n            if (!xe || !ye) {\n                // Either Infinity?\n                if (!xc || !yc) return xc ? (y.s = -b, y) : new BigNumber(yc ? x : NaN);\n                // Either zero?\n                if (!xc[0] || !yc[0]) {\n                    // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\n                    return yc[0] ? (y.s = -b, y) : new BigNumber(xc[0] ? x : // IEEE 754 (2008) 6.3: n - n = -0 when rounding to -Infinity\n                    ROUNDING_MODE == 3 ? -0 : 0);\n                }\n            }\n            xe = bitFloor(xe);\n            ye = bitFloor(ye);\n            xc = xc.slice();\n            // Determine which is the bigger number.\n            if (a = xe - ye) {\n                if (xLTy = a < 0) {\n                    a = -a;\n                    t = xc;\n                } else {\n                    ye = xe;\n                    t = yc;\n                }\n                t.reverse();\n                // Prepend zeros to equalise exponents.\n                for(b = a; b--; t.push(0));\n                t.reverse();\n            } else {\n                // Exponents equal. Check digit by digit.\n                j = (xLTy = (a = xc.length) < (b = yc.length)) ? a : b;\n                for(a = b = 0; b < j; b++){\n                    if (xc[b] != yc[b]) {\n                        xLTy = xc[b] < yc[b];\n                        break;\n                    }\n                }\n            }\n            // x < y? Point xc to the array of the bigger number.\n            if (xLTy) {\n                t = xc;\n                xc = yc;\n                yc = t;\n                y.s = -y.s;\n            }\n            b = (j = yc.length) - (i = xc.length);\n            // Append zeros to xc if shorter.\n            // No need to add zeros to yc if shorter as subtract only needs to start at yc.length.\n            if (b > 0) for(; b--; xc[i++] = 0);\n            b = BASE - 1;\n            // Subtract yc from xc.\n            for(; j > a;){\n                if (xc[--j] < yc[j]) {\n                    for(i = j; i && !xc[--i]; xc[i] = b);\n                    --xc[i];\n                    xc[j] += BASE;\n                }\n                xc[j] -= yc[j];\n            }\n            // Remove leading zeros and adjust exponent accordingly.\n            for(; xc[0] == 0; xc.splice(0, 1), --ye);\n            // Zero?\n            if (!xc[0]) {\n                // Following IEEE 754 (2008) 6.3,\n                // n - n = +0  but  n - n = -0  when rounding towards -Infinity.\n                y.s = ROUNDING_MODE == 3 ? -1 : 1;\n                y.c = [\n                    y.e = 0\n                ];\n                return y;\n            }\n            // No need to check for Infinity as +x - +y != Infinity && -x - -y != Infinity\n            // for finite x and y.\n            return normalise(y, xc, ye);\n        };\n        /*\r\n     *   n % 0 =  N\r\n     *   n % N =  N\r\n     *   n % I =  n\r\n     *   0 % n =  0\r\n     *  -0 % n = -0\r\n     *   0 % 0 =  N\r\n     *   0 % N =  N\r\n     *   0 % I =  0\r\n     *   N % n =  N\r\n     *   N % 0 =  N\r\n     *   N % N =  N\r\n     *   N % I =  N\r\n     *   I % n =  N\r\n     *   I % 0 =  N\r\n     *   I % N =  N\r\n     *   I % I =  N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber modulo the value of\r\n     * BigNumber(y, b). The result depends on the value of MODULO_MODE.\r\n     */ P.modulo = P.mod = function(y, b) {\n            var q, s, x = this;\n            y = new BigNumber(y, b);\n            // Return NaN if x is Infinity or NaN, or y is NaN or zero.\n            if (!x.c || !y.s || y.c && !y.c[0]) {\n                return new BigNumber(NaN);\n            // Return x if y is Infinity or x is zero.\n            } else if (!y.c || x.c && !x.c[0]) {\n                return new BigNumber(x);\n            }\n            if (MODULO_MODE == 9) {\n                // Euclidian division: q = sign(y) * floor(x / abs(y))\n                // r = x - qy    where  0 <= r < abs(y)\n                s = y.s;\n                y.s = 1;\n                q = div(x, y, 0, 3);\n                y.s = s;\n                q.s *= s;\n            } else {\n                q = div(x, y, 0, MODULO_MODE);\n            }\n            y = x.minus(q.times(y));\n            // To match JavaScript %, ensure sign of zero is sign of dividend.\n            if (!y.c[0] && MODULO_MODE == 1) y.s = x.s;\n            return y;\n        };\n        /*\r\n     *  n * 0 = 0\r\n     *  n * N = N\r\n     *  n * I = I\r\n     *  0 * n = 0\r\n     *  0 * 0 = 0\r\n     *  0 * N = N\r\n     *  0 * I = N\r\n     *  N * n = N\r\n     *  N * 0 = N\r\n     *  N * N = N\r\n     *  N * I = N\r\n     *  I * n = I\r\n     *  I * 0 = N\r\n     *  I * N = N\r\n     *  I * I = I\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber multiplied by the value\r\n     * of BigNumber(y, b).\r\n     */ P.multipliedBy = P.times = function(y, b) {\n            var c, e, i, j, k, m, xcL, xlo, xhi, ycL, ylo, yhi, zc, base, sqrtBase, x = this, xc = x.c, yc = (y = new BigNumber(y, b)).c;\n            // Either NaN, ±Infinity or ±0?\n            if (!xc || !yc || !xc[0] || !yc[0]) {\n                // Return NaN if either is NaN, or one is 0 and the other is Infinity.\n                if (!x.s || !y.s || xc && !xc[0] && !yc || yc && !yc[0] && !xc) {\n                    y.c = y.e = y.s = null;\n                } else {\n                    y.s *= x.s;\n                    // Return ±Infinity if either is ±Infinity.\n                    if (!xc || !yc) {\n                        y.c = y.e = null;\n                    // Return ±0 if either is ±0.\n                    } else {\n                        y.c = [\n                            0\n                        ];\n                        y.e = 0;\n                    }\n                }\n                return y;\n            }\n            e = bitFloor(x.e / LOG_BASE) + bitFloor(y.e / LOG_BASE);\n            y.s *= x.s;\n            xcL = xc.length;\n            ycL = yc.length;\n            // Ensure xc points to longer array and xcL to its length.\n            if (xcL < ycL) {\n                zc = xc;\n                xc = yc;\n                yc = zc;\n                i = xcL;\n                xcL = ycL;\n                ycL = i;\n            }\n            // Initialise the result array with zeros.\n            for(i = xcL + ycL, zc = []; i--; zc.push(0));\n            base = BASE;\n            sqrtBase = SQRT_BASE;\n            for(i = ycL; --i >= 0;){\n                c = 0;\n                ylo = yc[i] % sqrtBase;\n                yhi = yc[i] / sqrtBase | 0;\n                for(k = xcL, j = i + k; j > i;){\n                    xlo = xc[--k] % sqrtBase;\n                    xhi = xc[k] / sqrtBase | 0;\n                    m = yhi * xlo + xhi * ylo;\n                    xlo = ylo * xlo + m % sqrtBase * sqrtBase + zc[j] + c;\n                    c = (xlo / base | 0) + (m / sqrtBase | 0) + yhi * xhi;\n                    zc[j--] = xlo % base;\n                }\n                zc[j] = c;\n            }\n            if (c) {\n                ++e;\n            } else {\n                zc.splice(0, 1);\n            }\n            return normalise(y, zc, e);\n        };\n        /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber negated,\r\n     * i.e. multiplied by -1.\r\n     */ P.negated = function() {\n            var x = new BigNumber(this);\n            x.s = -x.s || null;\n            return x;\n        };\n        /*\r\n     *  n + 0 = n\r\n     *  n + N = N\r\n     *  n + I = I\r\n     *  0 + n = n\r\n     *  0 + 0 = 0\r\n     *  0 + N = N\r\n     *  0 + I = I\r\n     *  N + n = N\r\n     *  N + 0 = N\r\n     *  N + N = N\r\n     *  N + I = N\r\n     *  I + n = I\r\n     *  I + 0 = I\r\n     *  I + N = N\r\n     *  I + I = I\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber plus the value of\r\n     * BigNumber(y, b).\r\n     */ P.plus = function(y, b) {\n            var t, x = this, a = x.s;\n            y = new BigNumber(y, b);\n            b = y.s;\n            // Either NaN?\n            if (!a || !b) return new BigNumber(NaN);\n            // Signs differ?\n            if (a != b) {\n                y.s = -b;\n                return x.minus(y);\n            }\n            var xe = x.e / LOG_BASE, ye = y.e / LOG_BASE, xc = x.c, yc = y.c;\n            if (!xe || !ye) {\n                // Return ±Infinity if either ±Infinity.\n                if (!xc || !yc) return new BigNumber(a / 0);\n                // Either zero?\n                // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\n                if (!xc[0] || !yc[0]) return yc[0] ? y : new BigNumber(xc[0] ? x : a * 0);\n            }\n            xe = bitFloor(xe);\n            ye = bitFloor(ye);\n            xc = xc.slice();\n            // Prepend zeros to equalise exponents. Faster to use reverse then do unshifts.\n            if (a = xe - ye) {\n                if (a > 0) {\n                    ye = xe;\n                    t = yc;\n                } else {\n                    a = -a;\n                    t = xc;\n                }\n                t.reverse();\n                for(; a--; t.push(0));\n                t.reverse();\n            }\n            a = xc.length;\n            b = yc.length;\n            // Point xc to the longer array, and b to the shorter length.\n            if (a - b < 0) {\n                t = yc;\n                yc = xc;\n                xc = t;\n                b = a;\n            }\n            // Only start adding at yc.length - 1 as the further digits of xc can be ignored.\n            for(a = 0; b;){\n                a = (xc[--b] = xc[b] + yc[b] + a) / BASE | 0;\n                xc[b] = BASE === xc[b] ? 0 : xc[b] % BASE;\n            }\n            if (a) {\n                xc = [\n                    a\n                ].concat(xc);\n                ++ye;\n            }\n            // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n            // ye = MAX_EXP + 1 possible\n            return normalise(y, xc, ye);\n        };\n        /*\r\n     * If sd is undefined or null or true or false, return the number of significant digits of\r\n     * the value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n     * If sd is true include integer-part trailing zeros in the count.\r\n     *\r\n     * Otherwise, if sd is a number, return a new BigNumber whose value is the value of this\r\n     * BigNumber rounded to a maximum of sd significant digits using rounding mode rm, or\r\n     * ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * sd {number|boolean} number: significant digits: integer, 1 to MAX inclusive.\r\n     *                     boolean: whether to count integer-part trailing zeros: true or false.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n     */ P.precision = P.sd = function(sd, rm) {\n            var c, n, v, x = this;\n            if (sd != null && sd !== !!sd) {\n                intCheck(sd, 1, MAX);\n                if (rm == null) rm = ROUNDING_MODE;\n                else intCheck(rm, 0, 8);\n                return round(new BigNumber(x), sd, rm);\n            }\n            if (!(c = x.c)) return null;\n            v = c.length - 1;\n            n = v * LOG_BASE + 1;\n            if (v = c[v]) {\n                // Subtract the number of trailing zeros of the last element.\n                for(; v % 10 == 0; v /= 10, n--);\n                // Add the number of digits of the first element.\n                for(v = c[0]; v >= 10; v /= 10, n++);\n            }\n            if (sd && x.e + 1 > n) n = x.e + 1;\n            return n;\n        };\n        /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber shifted by k places\r\n     * (powers of 10). Shift to the right if n > 0, and to the left if n < 0.\r\n     *\r\n     * k {number} Integer, -MAX_SAFE_INTEGER to MAX_SAFE_INTEGER inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {k}'\r\n     */ P.shiftedBy = function(k) {\n            intCheck(k, -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);\n            return this.times(\"1e\" + k);\n        };\n        /*\r\n     *  sqrt(-n) =  N\r\n     *  sqrt(N) =  N\r\n     *  sqrt(-I) =  N\r\n     *  sqrt(I) =  I\r\n     *  sqrt(0) =  0\r\n     *  sqrt(-0) = -0\r\n     *\r\n     * Return a new BigNumber whose value is the square root of the value of this BigNumber,\r\n     * rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     */ P.squareRoot = P.sqrt = function() {\n            var m, n, r, rep, t, x = this, c = x.c, s = x.s, e = x.e, dp = DECIMAL_PLACES + 4, half = new BigNumber(\"0.5\");\n            // Negative/NaN/Infinity/zero?\n            if (s !== 1 || !c || !c[0]) {\n                return new BigNumber(!s || s < 0 && (!c || c[0]) ? NaN : c ? x : 1 / 0);\n            }\n            // Initial estimate.\n            s = Math.sqrt(+valueOf(x));\n            // Math.sqrt underflow/overflow?\n            // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n            if (s == 0 || s == 1 / 0) {\n                n = coeffToString(c);\n                if ((n.length + e) % 2 == 0) n += \"0\";\n                s = Math.sqrt(+n);\n                e = bitFloor((e + 1) / 2) - (e < 0 || e % 2);\n                if (s == 1 / 0) {\n                    n = \"5e\" + e;\n                } else {\n                    n = s.toExponential();\n                    n = n.slice(0, n.indexOf(\"e\") + 1) + e;\n                }\n                r = new BigNumber(n);\n            } else {\n                r = new BigNumber(s + \"\");\n            }\n            // Check for zero.\n            // r could be zero if MIN_EXP is changed after the this value was created.\n            // This would cause a division by zero (x/t) and hence Infinity below, which would cause\n            // coeffToString to throw.\n            if (r.c[0]) {\n                e = r.e;\n                s = e + dp;\n                if (s < 3) s = 0;\n                // Newton-Raphson iteration.\n                for(;;){\n                    t = r;\n                    r = half.times(t.plus(div(x, t, dp, 1)));\n                    if (coeffToString(t.c).slice(0, s) === (n = coeffToString(r.c)).slice(0, s)) {\n                        // The exponent of r may here be one less than the final result exponent,\n                        // e.g 0.0009999 (e-4) --> 0.001 (e-3), so adjust s so the rounding digits\n                        // are indexed correctly.\n                        if (r.e < e) --s;\n                        n = n.slice(s - 3, s + 1);\n                        // The 4th rounding digit may be in error by -1 so if the 4 rounding digits\n                        // are 9999 or 4999 (i.e. approaching a rounding boundary) continue the\n                        // iteration.\n                        if (n == \"9999\" || !rep && n == \"4999\") {\n                            // On the first iteration only, check to see if rounding up gives the\n                            // exact result as the nines may infinitely repeat.\n                            if (!rep) {\n                                round(t, t.e + DECIMAL_PLACES + 2, 0);\n                                if (t.times(t).eq(x)) {\n                                    r = t;\n                                    break;\n                                }\n                            }\n                            dp += 4;\n                            s += 4;\n                            rep = 1;\n                        } else {\n                            // If rounding digits are null, 0{0,4} or 50{0,3}, check for exact\n                            // result. If not, then there are further digits and m will be truthy.\n                            if (!+n || !+n.slice(1) && n.charAt(0) == \"5\") {\n                                // Truncate to the first rounding digit.\n                                round(r, r.e + DECIMAL_PLACES + 2, 1);\n                                m = !r.times(r).eq(x);\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            return round(r, r.e + DECIMAL_PLACES + 1, ROUNDING_MODE, m);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in exponential notation and\r\n     * rounded using ROUNDING_MODE to dp fixed decimal places.\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */ P.toExponential = function(dp, rm) {\n            if (dp != null) {\n                intCheck(dp, 0, MAX);\n                dp++;\n            }\n            return format(this, dp, rm, 1);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in fixed-point notation rounding\r\n     * to dp fixed decimal places using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * Note: as with JavaScript's number type, (-0).toFixed(0) is '0',\r\n     * but e.g. (-0.00001).toFixed(0) is '-0'.\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */ P.toFixed = function(dp, rm) {\n            if (dp != null) {\n                intCheck(dp, 0, MAX);\n                dp = dp + this.e + 1;\n            }\n            return format(this, dp, rm);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in fixed-point notation rounded\r\n     * using rm or ROUNDING_MODE to dp decimal places, and formatted according to the properties\r\n     * of the format or FORMAT object (see BigNumber.set).\r\n     *\r\n     * The formatting object may contain some or all of the properties shown below.\r\n     *\r\n     * FORMAT = {\r\n     *   prefix: '',\r\n     *   groupSize: 3,\r\n     *   secondaryGroupSize: 0,\r\n     *   groupSeparator: ',',\r\n     *   decimalSeparator: '.',\r\n     *   fractionGroupSize: 0,\r\n     *   fractionGroupSeparator: '\\xA0',      // non-breaking space\r\n     *   suffix: ''\r\n     * };\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     * [format] {object} Formatting options. See FORMAT pbject above.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     * '[BigNumber Error] Argument not an object: {format}'\r\n     */ P.toFormat = function(dp, rm, format) {\n            var str, x = this;\n            if (format == null) {\n                if (dp != null && rm && typeof rm == \"object\") {\n                    format = rm;\n                    rm = null;\n                } else if (dp && typeof dp == \"object\") {\n                    format = dp;\n                    dp = rm = null;\n                } else {\n                    format = FORMAT;\n                }\n            } else if (typeof format != \"object\") {\n                throw Error(bignumberError + \"Argument not an object: \" + format);\n            }\n            str = x.toFixed(dp, rm);\n            if (x.c) {\n                var i, arr = str.split(\".\"), g1 = +format.groupSize, g2 = +format.secondaryGroupSize, groupSeparator = format.groupSeparator || \"\", intPart = arr[0], fractionPart = arr[1], isNeg = x.s < 0, intDigits = isNeg ? intPart.slice(1) : intPart, len = intDigits.length;\n                if (g2) {\n                    i = g1;\n                    g1 = g2;\n                    g2 = i;\n                    len -= i;\n                }\n                if (g1 > 0 && len > 0) {\n                    i = len % g1 || g1;\n                    intPart = intDigits.substr(0, i);\n                    for(; i < len; i += g1)intPart += groupSeparator + intDigits.substr(i, g1);\n                    if (g2 > 0) intPart += groupSeparator + intDigits.slice(i);\n                    if (isNeg) intPart = \"-\" + intPart;\n                }\n                str = fractionPart ? intPart + (format.decimalSeparator || \"\") + ((g2 = +format.fractionGroupSize) ? fractionPart.replace(new RegExp(\"\\\\d{\" + g2 + \"}\\\\B\", \"g\"), \"$&\" + (format.fractionGroupSeparator || \"\")) : fractionPart) : intPart;\n            }\n            return (format.prefix || \"\") + str + (format.suffix || \"\");\n        };\n        /*\r\n     * Return an array of two BigNumbers representing the value of this BigNumber as a simple\r\n     * fraction with an integer numerator and an integer denominator.\r\n     * The denominator will be a positive non-zero value less than or equal to the specified\r\n     * maximum denominator. If a maximum denominator is not specified, the denominator will be\r\n     * the lowest value necessary to represent the number exactly.\r\n     *\r\n     * [md] {number|string|BigNumber} Integer >= 1, or Infinity. The maximum denominator.\r\n     *\r\n     * '[BigNumber Error] Argument {not an integer|out of range} : {md}'\r\n     */ P.toFraction = function(md) {\n            var d, d0, d1, d2, e, exp, n, n0, n1, q, r, s, x = this, xc = x.c;\n            if (md != null) {\n                n = new BigNumber(md);\n                // Throw if md is less than one or is not an integer, unless it is Infinity.\n                if (!n.isInteger() && (n.c || n.s !== 1) || n.lt(ONE)) {\n                    throw Error(bignumberError + \"Argument \" + (n.isInteger() ? \"out of range: \" : \"not an integer: \") + valueOf(n));\n                }\n            }\n            if (!xc) return new BigNumber(x);\n            d = new BigNumber(ONE);\n            n1 = d0 = new BigNumber(ONE);\n            d1 = n0 = new BigNumber(ONE);\n            s = coeffToString(xc);\n            // Determine initial denominator.\n            // d is a power of 10 and the minimum max denominator that specifies the value exactly.\n            e = d.e = s.length - x.e - 1;\n            d.c[0] = POWS_TEN[(exp = e % LOG_BASE) < 0 ? LOG_BASE + exp : exp];\n            md = !md || n.comparedTo(d) > 0 ? e > 0 ? d : n1 : n;\n            exp = MAX_EXP;\n            MAX_EXP = 1 / 0;\n            n = new BigNumber(s);\n            // n0 = d1 = 0\n            n0.c[0] = 0;\n            for(;;){\n                q = div(n, d, 0, 1);\n                d2 = d0.plus(q.times(d1));\n                if (d2.comparedTo(md) == 1) break;\n                d0 = d1;\n                d1 = d2;\n                n1 = n0.plus(q.times(d2 = n1));\n                n0 = d2;\n                d = n.minus(q.times(d2 = d));\n                n = d2;\n            }\n            d2 = div(md.minus(d0), d1, 0, 1);\n            n0 = n0.plus(d2.times(n1));\n            d0 = d0.plus(d2.times(d1));\n            n0.s = n1.s = x.s;\n            e = e * 2;\n            // Determine which fraction is closer to x, n0/d0 or n1/d1\n            r = div(n1, d1, e, ROUNDING_MODE).minus(x).abs().comparedTo(div(n0, d0, e, ROUNDING_MODE).minus(x).abs()) < 1 ? [\n                n1,\n                d1\n            ] : [\n                n0,\n                d0\n            ];\n            MAX_EXP = exp;\n            return r;\n        };\n        /*\r\n     * Return the value of this BigNumber converted to a number primitive.\r\n     */ P.toNumber = function() {\n            return +valueOf(this);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber rounded to sd significant digits\r\n     * using rounding mode rm or ROUNDING_MODE. If sd is less than the number of digits\r\n     * necessary to represent the integer part of the value in fixed-point notation, then use\r\n     * exponential notation.\r\n     *\r\n     * [sd] {number} Significant digits. Integer, 1 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n     */ P.toPrecision = function(sd, rm) {\n            if (sd != null) intCheck(sd, 1, MAX);\n            return format(this, sd, rm, 2);\n        };\n        /*\r\n     * Return a string representing the value of this BigNumber in base b, or base 10 if b is\r\n     * omitted. If a base is specified, including base 10, round according to DECIMAL_PLACES and\r\n     * ROUNDING_MODE. If a base is not specified, and this BigNumber has a positive exponent\r\n     * that is equal to or greater than TO_EXP_POS, or a negative exponent equal to or less than\r\n     * TO_EXP_NEG, return exponential notation.\r\n     *\r\n     * [b] {number} Integer, 2 to ALPHABET.length inclusive.\r\n     *\r\n     * '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n     */ P.toString = function(b) {\n            var str, n = this, s = n.s, e = n.e;\n            // Infinity or NaN?\n            if (e === null) {\n                if (s) {\n                    str = \"Infinity\";\n                    if (s < 0) str = \"-\" + str;\n                } else {\n                    str = \"NaN\";\n                }\n            } else {\n                if (b == null) {\n                    str = e <= TO_EXP_NEG || e >= TO_EXP_POS ? toExponential(coeffToString(n.c), e) : toFixedPoint(coeffToString(n.c), e, \"0\");\n                } else if (b === 10 && alphabetHasNormalDecimalDigits) {\n                    n = round(new BigNumber(n), DECIMAL_PLACES + e + 1, ROUNDING_MODE);\n                    str = toFixedPoint(coeffToString(n.c), n.e, \"0\");\n                } else {\n                    intCheck(b, 2, ALPHABET.length, \"Base\");\n                    str = convertBase(toFixedPoint(coeffToString(n.c), e, \"0\"), 10, b, s, true);\n                }\n                if (s < 0 && n.c[0]) str = \"-\" + str;\n            }\n            return str;\n        };\n        /*\r\n     * Return as toString, but do not accept a base argument, and include the minus sign for\r\n     * negative zero.\r\n     */ P.valueOf = P.toJSON = function() {\n            return valueOf(this);\n        };\n        P._isBigNumber = true;\n        if (configObject != null) BigNumber.set(configObject);\n        return BigNumber;\n    }\n    // PRIVATE HELPER FUNCTIONS\n    // These functions don't need access to variables,\n    // e.g. DECIMAL_PLACES, in the scope of the `clone` function above.\n    function bitFloor(n) {\n        var i = n | 0;\n        return n > 0 || n === i ? i : i - 1;\n    }\n    // Return a coefficient array as a string of base 10 digits.\n    function coeffToString(a) {\n        var s, z, i = 1, j = a.length, r = a[0] + \"\";\n        for(; i < j;){\n            s = a[i++] + \"\";\n            z = LOG_BASE - s.length;\n            for(; z--; s = \"0\" + s);\n            r += s;\n        }\n        // Determine trailing zeros.\n        for(j = r.length; r.charCodeAt(--j) === 48;);\n        return r.slice(0, j + 1 || 1);\n    }\n    // Compare the value of BigNumbers x and y.\n    function compare(x, y) {\n        var a, b, xc = x.c, yc = y.c, i = x.s, j = y.s, k = x.e, l = y.e;\n        // Either NaN?\n        if (!i || !j) return null;\n        a = xc && !xc[0];\n        b = yc && !yc[0];\n        // Either zero?\n        if (a || b) return a ? b ? 0 : -j : i;\n        // Signs differ?\n        if (i != j) return i;\n        a = i < 0;\n        b = k == l;\n        // Either Infinity?\n        if (!xc || !yc) return b ? 0 : !xc ^ a ? 1 : -1;\n        // Compare exponents.\n        if (!b) return k > l ^ a ? 1 : -1;\n        j = (k = xc.length) < (l = yc.length) ? k : l;\n        // Compare digit by digit.\n        for(i = 0; i < j; i++)if (xc[i] != yc[i]) return xc[i] > yc[i] ^ a ? 1 : -1;\n        // Compare lengths.\n        return k == l ? 0 : k > l ^ a ? 1 : -1;\n    }\n    /*\r\n   * Check that n is a primitive number, an integer, and in range, otherwise throw.\r\n   */ function intCheck(n, min, max, name) {\n        if (n < min || n > max || n !== mathfloor(n)) {\n            throw Error(bignumberError + (name || \"Argument\") + (typeof n == \"number\" ? n < min || n > max ? \" out of range: \" : \" not an integer: \" : \" not a primitive number: \") + String(n));\n        }\n    }\n    // Assumes finite n.\n    function isOdd(n) {\n        var k = n.c.length - 1;\n        return bitFloor(n.e / LOG_BASE) == k && n.c[k] % 2 != 0;\n    }\n    function toExponential(str, e) {\n        return (str.length > 1 ? str.charAt(0) + \".\" + str.slice(1) : str) + (e < 0 ? \"e\" : \"e+\") + e;\n    }\n    function toFixedPoint(str, e, z) {\n        var len, zs;\n        // Negative exponent?\n        if (e < 0) {\n            // Prepend zeros.\n            for(zs = z + \".\"; ++e; zs += z);\n            str = zs + str;\n        // Positive exponent\n        } else {\n            len = str.length;\n            // Append zeros.\n            if (++e > len) {\n                for(zs = z, e -= len; --e; zs += z);\n                str += zs;\n            } else if (e < len) {\n                str = str.slice(0, e) + \".\" + str.slice(e);\n            }\n        }\n        return str;\n    }\n    // EXPORT\n    BigNumber = clone();\n    BigNumber[\"default\"] = BigNumber.BigNumber = BigNumber;\n    // AMD.\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_RESULT__ = (function() {\n            return BigNumber;\n        }).call(exports, __webpack_require__, exports, module),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    // Node.js and other environments that support module.exports.\n    } else {}\n})(void 0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bignumber.js/bignumber.js\n");

/***/ })

};
;