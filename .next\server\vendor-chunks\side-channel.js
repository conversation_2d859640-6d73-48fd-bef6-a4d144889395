"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel";
exports.ids = ["vendor-chunks/side-channel"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel/index.js":
/*!********************************************!*\
  !*** ./node_modules/side-channel/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelList = __webpack_require__(/*! side-channel-list */ \"(rsc)/./node_modules/side-channel-list/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\nvar getSideChannelWeakMap = __webpack_require__(/*! side-channel-weakmap */ \"(rsc)/./node_modules/side-channel-weakmap/index.js\");\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n/** @type {import('.')} */ module.exports = function getSideChannel() {\n    /** @typedef {ReturnType<typeof getSideChannel>} Channel */ /** @type {Channel | undefined} */ var $channelData;\n    /** @type {Channel} */ var channel = {\n        assert: function(key) {\n            if (!channel.has(key)) {\n                throw new $TypeError(\"Side channel does not contain \" + inspect(key));\n            }\n        },\n        \"delete\": function(key) {\n            return !!$channelData && $channelData[\"delete\"](key);\n        },\n        get: function(key) {\n            return $channelData && $channelData.get(key);\n        },\n        has: function(key) {\n            return !!$channelData && $channelData.has(key);\n        },\n        set: function(key, value) {\n            if (!$channelData) {\n                $channelData = makeChannel();\n            }\n            $channelData.set(key, value);\n        }\n    };\n    // @ts-expect-error TODO: figure out why this is erroring\n    return channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel/index.js\n");

/***/ })

};
;