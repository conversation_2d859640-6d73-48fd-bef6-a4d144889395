"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Archive,Check,Edit,Inbox,Mail,MoreVertical,Plus,Search,Send,Settings,Star,Trash,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gmail-client */ \"(app-pages-browser)/./src/lib/gmail-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    var _currentEmails_find, _currentEmails_find1, _currentEmails_find2;\n    _s();\n    var _s1 = $RefreshSig$();\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAccountMenu, setShowAccountMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    const [gmailClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>new _lib_gmail_client__WEBPACK_IMPORTED_MODULE_2__[\"default\"]());\n    const [isLoadingEmails, setIsLoadingEmails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 从localStorage加载账户\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const loadAccounts = ()=>{\n            try {\n                const saved = localStorage.getItem(\"emailAccounts\");\n                if (saved) {\n                    const accounts = JSON.parse(saved);\n                    setEmailAccounts(accounts);\n                    if (accounts.length > 0 && !activeAccountId) {\n                        setActiveAccountId(accounts[0].id);\n                    }\n                } else {\n                    // 默认示例账户\n                    const defaultAccounts = [\n                        {\n                            id: \"1\",\n                            email: \"<EMAIL>\",\n                            provider: \"gmail\",\n                            name: \"个人Gmail\",\n                            isActive: true,\n                            unreadCount: 3\n                        }\n                    ];\n                    setEmailAccounts(defaultAccounts);\n                    setActiveAccountId(\"1\");\n                }\n            } catch (error) {\n                console.error(\"Error loading accounts:\", error);\n            }\n        };\n        loadAccounts();\n    }, []);\n    // 保存账户到localStorage\n    const saveAccounts = (accounts)=>{\n        try {\n            localStorage.setItem(\"emailAccounts\", JSON.stringify(accounts));\n            setEmailAccounts(accounts);\n        } catch (error) {\n            console.error(\"Error saving accounts:\", error);\n        }\n    };\n    // 模拟邮件数据\n    const emails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 获取当前账户的邮件\n    const currentEmails = emails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n        setShowAccountMenu(null) // 关闭菜单\n        ;\n    };\n    // 删除邮箱账户\n    const deleteAccount = (accountId)=>{\n        if (emailAccounts.length <= 1) {\n            alert(\"至少需要保留一个邮箱账户\");\n            return;\n        }\n        if (confirm(\"确定要删除这个邮箱账户吗？\")) {\n            const updatedAccounts = emailAccounts.filter((acc)=>acc.id !== accountId);\n            saveAccounts(updatedAccounts);\n            // 如果删除的是当前活跃账户，切换到第一个可用账户\n            if (accountId === activeAccountId) {\n                if (updatedAccounts.length > 0) {\n                    setActiveAccountId(updatedAccounts[0].id);\n                }\n            }\n            setShowAccountMenu(null);\n            setSelectedEmail(null);\n        }\n    };\n    // 点击外部关闭菜单\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (showAccountMenu) {\n                setShowAccountMenu(null);\n            }\n        };\n        document.addEventListener(\"click\", handleClickOutside);\n        return ()=>document.removeEventListener(\"click\", handleClickOutside);\n    }, [\n        showAccountMenu\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-800\",\n                                        children: \"邮箱客户端\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative p-2 rounded text-sm transition-colors group \".concat(account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    onClick: ()=>switchAccount(account.id),\n                                                    className: \"flex items-center justify-between cursor-pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: getProviderIcon(account.provider)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: account.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 229,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs opacity-75\",\n                                                                            children: account.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 230,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                                    children: account.unreadCount\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: (e)=>{\n                                                                        e.stopPropagation();\n                                                                        setShowAccountMenu(showAccountMenu === account.id ? null : account.id);\n                                                                    },\n                                                                    className: \"p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 246,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this),\n                                                showAccountMenu === account.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // TODO: 实现编辑功能\n                                                                setShowAccountMenu(null);\n                                                            },\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"编辑\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>deleteAccount(account.id),\n                                                            className: \"w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"删除\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, account.id, true, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name) || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.unreadCount) && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: \"p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 \".concat(selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\", \" \").concat(email.unread ? \"bg-white\" : \"bg-gray-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"),\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm mb-1 \".concat(email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"),\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: (_currentEmails_find = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find === void 0 ? void 0 : _currentEmails_find.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: (_currentEmails_find1 = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find1 === void 0 ? void 0 : _currentEmails_find1.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: (_currentEmails_find2 = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find2 === void 0 ? void 0 : _currentEmails_find2.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"来自: \",\n                                                            activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name,\n                                                            \" (\",\n                                                            activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: [\n                                    \"当前账户: \",\n                                    activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 388,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"发件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: account.id,\n                                                    children: [\n                                                        account.name,\n                                                        \" (\",\n                                                        account.email,\n                                                        \")\"\n                                                    ]\n                                                }, account.id, true, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 446,\n                columnNumber: 9\n            }, this),\n            showAddAccount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddAccountModal, {}, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 514,\n                columnNumber: 26\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n    // 添加邮箱账户弹窗组件\n    function AddAccountModal() {\n        _s1();\n        const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n            name: \"\",\n            email: \"\",\n            provider: \"gmail\",\n            password: \"\"\n        });\n        const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n        const handleSubmit = async (e)=>{\n            e.preventDefault();\n            setIsSubmitting(true);\n            setError(\"\");\n            // 验证表单\n            if (!formData.name || !formData.email || !formData.password) {\n                setError(\"请填写所有必填字段\");\n                setIsSubmitting(false);\n                return;\n            }\n            // 检查邮箱是否已存在\n            if (emailAccounts.some((acc)=>acc.email === formData.email)) {\n                setError(\"该邮箱账户已存在\");\n                setIsSubmitting(false);\n                return;\n            }\n            try {\n                // 模拟添加账户的过程\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                const newAccount = {\n                    id: Date.now().toString(),\n                    email: formData.email,\n                    provider: formData.provider,\n                    name: formData.name,\n                    isActive: false,\n                    unreadCount: 0\n                };\n                setEmailAccounts((prev)=>[\n                        ...prev,\n                        newAccount\n                    ]);\n                setShowAddAccount(false);\n                // 重置表单\n                setFormData({\n                    name: \"\",\n                    email: \"\",\n                    provider: \"gmail\",\n                    password: \"\"\n                });\n            } catch (err) {\n                setError(\"添加账户失败，请重试\");\n            } finally{\n                setIsSubmitting(false);\n            }\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg w-full max-w-md mx-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold\",\n                                children: \"添加邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 582,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowAddAccount(false),\n                                className: \"text-gray-400 hover:text-gray-600\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 space-y-4\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"账户名称 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: formData.name,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    name: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"例如：个人邮箱、工作邮箱\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"邮箱服务商 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: formData.provider,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    provider: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"gmail\",\n                                                children: \"Gmail (Google)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"outlook\",\n                                                children: \"Outlook (Microsoft)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"163\",\n                                                children: \"163邮箱 (网易)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"qq\",\n                                                children: \"QQ邮箱 (腾讯)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"other\",\n                                                children: \"其他\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 626,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"邮箱地址 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 22\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: formData.email,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    email: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"<EMAIL>\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: [\n                                            \"密码/应用专用密码 \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-500\",\n                                                children: \"*\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 27\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: formData.password,\n                                        onChange: (e)=>setFormData((prev)=>({\n                                                    ...prev,\n                                                    password: e.target.value\n                                                })),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"输入密码\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"建议使用应用专用密码以提高安全性\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 644,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>setShowAddAccount(false),\n                                        className: \"flex-1 px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                        disabled: isSubmitting,\n                                        children: \"取消\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 662,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                        children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"添加中...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Archive_Check_Edit_Inbox_Mail_MoreVertical_Plus_Search_Send_Settings_Star_Trash_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"添加账户\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 580,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 579,\n            columnNumber: 7\n        }, this);\n    }\n    _s1(AddAccountModal, \"Le4pLMZEobX1cUesYRCKZ54LCII=\");\n}\n_s(Home, \"AhwKIUa3tA2adS+WWMdyfotRcB0=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});