/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fetch-blob";
exports.ids = ["vendor-chunks/fetch-blob"];
exports.modules = {

/***/ "(rsc)/./node_modules/fetch-blob/streams.cjs":
/*!*********************************************!*\
  !*** ./node_modules/fetch-blob/streams.cjs ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("/* c8 ignore start */ // 64 KiB (same size chrome slice theirs blob into Uint8array's)\nconst POOL_SIZE = 65536;\nif (!globalThis.ReadableStream) {\n    // `node:stream/web` got introduced in v16.5.0 as experimental\n    // and it's preferred over the polyfilled version. So we also\n    // suppress the warning that gets emitted by NodeJS for using it.\n    try {\n        const process = __webpack_require__(/*! node:process */ \"node:process\");\n        const { emitWarning } = process;\n        try {\n            process.emitWarning = ()=>{};\n            Object.assign(globalThis, __webpack_require__(/*! node:stream/web */ \"node:stream/web\"));\n            process.emitWarning = emitWarning;\n        } catch (error) {\n            process.emitWarning = emitWarning;\n            throw error;\n        }\n    } catch (error) {\n        // fallback to polyfill implementation\n        Object.assign(globalThis, __webpack_require__(/*! web-streams-polyfill/dist/ponyfill.es2018.js */ \"(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.es2018.js\"));\n    }\n}\ntry {\n    // Don't use node: prefix for this, require+node: is not supported until node v14.14\n    // Only `import()` can use prefix in 12.20 and later\n    const { Blob } = __webpack_require__(/*! buffer */ \"buffer\");\n    if (Blob && !Blob.prototype.stream) {\n        Blob.prototype.stream = function name(params) {\n            let position = 0;\n            const blob = this;\n            return new ReadableStream({\n                type: \"bytes\",\n                async pull (ctrl) {\n                    const chunk = blob.slice(position, Math.min(blob.size, position + POOL_SIZE));\n                    const buffer = await chunk.arrayBuffer();\n                    position += buffer.byteLength;\n                    ctrl.enqueue(new Uint8Array(buffer));\n                    if (position === blob.size) {\n                        ctrl.close();\n                    }\n                }\n            });\n        };\n    }\n} catch (error) {} /* c8 ignore end */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fetch-blob/streams.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/fetch-blob/file.js":
/*!*****************************************!*\
  !*** ./node_modules/fetch-blob/file.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(rsc)/./node_modules/fetch-blob/index.js\");\n\nconst _File = class File extends _index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n    #lastModified;\n    #name;\n    /**\n   * @param {*[]} fileBits\n   * @param {string} fileName\n   * @param {{lastModified?: number, type?: string}} options\n   */ // @ts-ignore\n    constructor(fileBits, fileName, options = {}){\n        if (arguments.length < 2) {\n            throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);\n        }\n        super(fileBits, options);\n        this.#lastModified = 0;\n        this.#name = \"\";\n        if (options === null) options = {};\n        // Simulate WebIDL type casting for NaN value in lastModified option.\n        const lastModified = options.lastModified === undefined ? Date.now() : Number(options.lastModified);\n        if (!Number.isNaN(lastModified)) {\n            this.#lastModified = lastModified;\n        }\n        this.#name = String(fileName);\n    }\n    get name() {\n        return this.#name;\n    }\n    get lastModified() {\n        return this.#lastModified;\n    }\n    get [Symbol.toStringTag]() {\n        return \"File\";\n    }\n    static [Symbol.hasInstance](object) {\n        return !!object && object instanceof _index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] && /^(File)$/.test(object[Symbol.toStringTag]);\n    }\n};\n/** @type {typeof globalThis.File} */ // @ts-ignore\nconst File = _File;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (File);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fetch-blob/file.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fetch-blob/from.js":
/*!*****************************************!*\
  !*** ./node_modules/fetch-blob/from.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   File: () => (/* reexport safe */ _file_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   blobFrom: () => (/* binding */ blobFrom),\n/* harmony export */   blobFromSync: () => (/* binding */ blobFromSync),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fileFrom: () => (/* binding */ fileFrom),\n/* harmony export */   fileFromSync: () => (/* binding */ fileFromSync)\n/* harmony export */ });\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:path */ \"node:path\");\n/* harmony import */ var node_domexception__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node-domexception */ \"(rsc)/./node_modules/node-domexception/index.js\");\n/* harmony import */ var _file_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./file.js */ \"(rsc)/./node_modules/fetch-blob/file.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.js */ \"(rsc)/./node_modules/fetch-blob/index.js\");\n\n\n\n\n\nconst { stat } = node_fs__WEBPACK_IMPORTED_MODULE_0__.promises;\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n */ const blobFromSync = (path, type)=>fromBlob((0,node_fs__WEBPACK_IMPORTED_MODULE_0__.statSync)(path), path, type);\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n * @returns {Promise<Blob>}\n */ const blobFrom = (path, type)=>stat(path).then((stat)=>fromBlob(stat, path, type));\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n * @returns {Promise<File>}\n */ const fileFrom = (path, type)=>stat(path).then((stat)=>fromFile(stat, path, type));\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n */ const fileFromSync = (path, type)=>fromFile((0,node_fs__WEBPACK_IMPORTED_MODULE_0__.statSync)(path), path, type);\n// @ts-ignore\nconst fromBlob = (stat, path, type = \"\")=>new _index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]([\n        new BlobDataItem({\n            path,\n            size: stat.size,\n            lastModified: stat.mtimeMs,\n            start: 0\n        })\n    ], {\n        type\n    });\n// @ts-ignore\nconst fromFile = (stat, path, type = \"\")=>new _file_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([\n        new BlobDataItem({\n            path,\n            size: stat.size,\n            lastModified: stat.mtimeMs,\n            start: 0\n        })\n    ], (0,node_path__WEBPACK_IMPORTED_MODULE_1__.basename)(path), {\n        type,\n        lastModified: stat.mtimeMs\n    });\n/**\n * This is a blob backed up by a file on the disk\n * with minium requirement. Its wrapped around a Blob as a blobPart\n * so you have no direct access to this.\n *\n * @private\n */ class BlobDataItem {\n    #path;\n    #start;\n    constructor(options){\n        this.#path = options.path;\n        this.#start = options.start;\n        this.size = options.size;\n        this.lastModified = options.lastModified;\n    }\n    /**\n   * Slicing arguments is first validated and formatted\n   * to not be out of range by Blob.prototype.slice\n   */ slice(start, end) {\n        return new BlobDataItem({\n            path: this.#path,\n            lastModified: this.lastModified,\n            size: end - start,\n            start: this.#start + start\n        });\n    }\n    async *stream() {\n        const { mtimeMs } = await stat(this.#path);\n        if (mtimeMs > this.lastModified) {\n            throw new node_domexception__WEBPACK_IMPORTED_MODULE_2__(\"The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.\", \"NotReadableError\");\n        }\n        yield* (0,node_fs__WEBPACK_IMPORTED_MODULE_0__.createReadStream)(this.#path, {\n            start: this.#start,\n            end: this.#start + this.size - 1\n        });\n    }\n    get [Symbol.toStringTag]() {\n        return \"Blob\";\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (blobFromSync);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fetch-blob/from.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fetch-blob/index.js":
/*!******************************************!*\
  !*** ./node_modules/fetch-blob/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _streams_cjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./streams.cjs */ \"(rsc)/./node_modules/fetch-blob/streams.cjs\");\n/*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */ // TODO (jimmywarting): in the feature use conditional loading with top level await (requires 14.x)\n// Node has recently added whatwg stream into core\n\n// 64 KiB (same size chrome slice theirs blob into Uint8array's)\nconst POOL_SIZE = 65536;\n/** @param {(Blob | Uint8Array)[]} parts */ async function* toIterator(parts, clone = true) {\n    for (const part of parts){\n        if (\"stream\" in part) {\n            yield* /** @type {AsyncIterableIterator<Uint8Array>} */ part.stream();\n        } else if (ArrayBuffer.isView(part)) {\n            if (clone) {\n                let position = part.byteOffset;\n                const end = part.byteOffset + part.byteLength;\n                while(position !== end){\n                    const size = Math.min(end - position, POOL_SIZE);\n                    const chunk = part.buffer.slice(position, position + size);\n                    position += chunk.byteLength;\n                    yield new Uint8Array(chunk);\n                }\n            } else {\n                yield part;\n            }\n        /* c8 ignore next 10 */ } else {\n            // For blobs that have arrayBuffer but no stream method (nodes buffer.Blob)\n            let position = 0, b = /** @type {Blob} */ part;\n            while(position !== b.size){\n                const chunk = b.slice(position, Math.min(b.size, position + POOL_SIZE));\n                const buffer = await chunk.arrayBuffer();\n                position += buffer.byteLength;\n                yield new Uint8Array(buffer);\n            }\n        }\n    }\n}\nconst _Blob = class Blob {\n    /** @type {Array.<(Blob|Uint8Array)>} */ #parts;\n    #type;\n    #size;\n    #endings;\n    /**\n   * The Blob() constructor returns a new Blob object. The content\n   * of the blob consists of the concatenation of the values given\n   * in the parameter array.\n   *\n   * @param {*} blobParts\n   * @param {{ type?: string, endings?: string }} [options]\n   */ constructor(blobParts = [], options = {}){\n        this.#parts = [];\n        this.#type = \"\";\n        this.#size = 0;\n        this.#endings = \"transparent\";\n        if (typeof blobParts !== \"object\" || blobParts === null) {\n            throw new TypeError(\"Failed to construct 'Blob': The provided value cannot be converted to a sequence.\");\n        }\n        if (typeof blobParts[Symbol.iterator] !== \"function\") {\n            throw new TypeError(\"Failed to construct 'Blob': The object must have a callable @@iterator property.\");\n        }\n        if (typeof options !== \"object\" && typeof options !== \"function\") {\n            throw new TypeError(\"Failed to construct 'Blob': parameter 2 cannot convert to dictionary.\");\n        }\n        if (options === null) options = {};\n        const encoder = new TextEncoder();\n        for (const element of blobParts){\n            let part;\n            if (ArrayBuffer.isView(element)) {\n                part = new Uint8Array(element.buffer.slice(element.byteOffset, element.byteOffset + element.byteLength));\n            } else if (element instanceof ArrayBuffer) {\n                part = new Uint8Array(element.slice(0));\n            } else if (element instanceof Blob) {\n                part = element;\n            } else {\n                part = encoder.encode(`${element}`);\n            }\n            this.#size += ArrayBuffer.isView(part) ? part.byteLength : part.size;\n            this.#parts.push(part);\n        }\n        this.#endings = `${options.endings === undefined ? \"transparent\" : options.endings}`;\n        const type = options.type === undefined ? \"\" : String(options.type);\n        this.#type = /^[\\x20-\\x7E]*$/.test(type) ? type : \"\";\n    }\n    /**\n   * The Blob interface's size property returns the\n   * size of the Blob in bytes.\n   */ get size() {\n        return this.#size;\n    }\n    /**\n   * The type property of a Blob object returns the MIME type of the file.\n   */ get type() {\n        return this.#type;\n    }\n    /**\n   * The text() method in the Blob interface returns a Promise\n   * that resolves with a string containing the contents of\n   * the blob, interpreted as UTF-8.\n   *\n   * @return {Promise<string>}\n   */ async text() {\n        // More optimized than using this.arrayBuffer()\n        // that requires twice as much ram\n        const decoder = new TextDecoder();\n        let str = \"\";\n        for await (const part of toIterator(this.#parts, false)){\n            str += decoder.decode(part, {\n                stream: true\n            });\n        }\n        // Remaining\n        str += decoder.decode();\n        return str;\n    }\n    /**\n   * The arrayBuffer() method in the Blob interface returns a\n   * Promise that resolves with the contents of the blob as\n   * binary data contained in an ArrayBuffer.\n   *\n   * @return {Promise<ArrayBuffer>}\n   */ async arrayBuffer() {\n        // Easier way... Just a unnecessary overhead\n        // const view = new Uint8Array(this.size);\n        // await this.stream().getReader({mode: 'byob'}).read(view);\n        // return view.buffer;\n        const data = new Uint8Array(this.size);\n        let offset = 0;\n        for await (const chunk of toIterator(this.#parts, false)){\n            data.set(chunk, offset);\n            offset += chunk.length;\n        }\n        return data.buffer;\n    }\n    stream() {\n        const it = toIterator(this.#parts, true);\n        return new globalThis.ReadableStream({\n            // @ts-ignore\n            type: \"bytes\",\n            async pull (ctrl) {\n                const chunk = await it.next();\n                chunk.done ? ctrl.close() : ctrl.enqueue(chunk.value);\n            },\n            async cancel () {\n                await it.return();\n            }\n        });\n    }\n    /**\n   * The Blob interface's slice() method creates and returns a\n   * new Blob object which contains data from a subset of the\n   * blob on which it's called.\n   *\n   * @param {number} [start]\n   * @param {number} [end]\n   * @param {string} [type]\n   */ slice(start = 0, end = this.size, type = \"\") {\n        const { size } = this;\n        let relativeStart = start < 0 ? Math.max(size + start, 0) : Math.min(start, size);\n        let relativeEnd = end < 0 ? Math.max(size + end, 0) : Math.min(end, size);\n        const span = Math.max(relativeEnd - relativeStart, 0);\n        const parts = this.#parts;\n        const blobParts = [];\n        let added = 0;\n        for (const part of parts){\n            // don't add the overflow to new blobParts\n            if (added >= span) {\n                break;\n            }\n            const size = ArrayBuffer.isView(part) ? part.byteLength : part.size;\n            if (relativeStart && size <= relativeStart) {\n                // Skip the beginning and change the relative\n                // start & end position as we skip the unwanted parts\n                relativeStart -= size;\n                relativeEnd -= size;\n            } else {\n                let chunk;\n                if (ArrayBuffer.isView(part)) {\n                    chunk = part.subarray(relativeStart, Math.min(size, relativeEnd));\n                    added += chunk.byteLength;\n                } else {\n                    chunk = part.slice(relativeStart, Math.min(size, relativeEnd));\n                    added += chunk.size;\n                }\n                relativeEnd -= size;\n                blobParts.push(chunk);\n                relativeStart = 0 // All next sequential parts should start at 0\n                ;\n            }\n        }\n        const blob = new Blob([], {\n            type: String(type).toLowerCase()\n        });\n        blob.#size = span;\n        blob.#parts = blobParts;\n        return blob;\n    }\n    get [Symbol.toStringTag]() {\n        return \"Blob\";\n    }\n    static [Symbol.hasInstance](object) {\n        return object && typeof object === \"object\" && typeof object.constructor === \"function\" && (typeof object.stream === \"function\" || typeof object.arrayBuffer === \"function\") && /^(Blob|File)$/.test(object[Symbol.toStringTag]);\n    }\n};\nObject.defineProperties(_Blob.prototype, {\n    size: {\n        enumerable: true\n    },\n    type: {\n        enumerable: true\n    },\n    slice: {\n        enumerable: true\n    }\n});\n/** @type {typeof globalThis.Blob} */ const Blob = _Blob;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Blob);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fetch-blob/index.js\n");

/***/ })

};
;