import { NextRequest, NextResponse } from 'next/server'
import GmailService from '@/lib/gmail-api'

// 邮件操作 (标记已读、加星标等)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    const accessToken = request.headers.get('authorization')?.replace('Bearer ', '')
    const { action, value } = await request.json()
    const { messageId } = params

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Access token is required' },
        { status: 401 }
      )
    }

    if (!messageId) {
      return NextResponse.json(
        { error: 'Message ID is required' },
        { status: 400 }
      )
    }

    const gmailService = new GmailService(accessToken)

    switch (action) {
      case 'markAsRead':
        await gmailService.markAsRead(messageId)
        break
      
      case 'toggleStar':
        await gmailService.toggleStar(messageId, value)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error performing message action:', error)
    return NextResponse.json(
      { error: 'Failed to perform action' },
      { status: 500 }
    )
  }
}
