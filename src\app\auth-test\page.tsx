'use client'

import { useState } from 'react'
import { <PERSON>ertCircle, CheckCircle, Loader2, Settings, Wifi } from 'lucide-react'

export default function AuthTestPage() {
  const [testData, setTestData] = useState({
    email: '',
    password: '',
    imapHost: '',
    imapPort: 993,
    useSSL: true
  })
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<any[]>([])

  const runAuthTest = async () => {
    setTesting(true)
    setResults([])

    const testConfigs = [
      { name: 'SSL + PLAIN认证', ssl: true, port: 993, auth: ['PLAIN'] },
      { name: 'SSL + LOGIN认证', ssl: true, port: 993, auth: ['LOGIN'] },
      { name: 'STARTTLS + PLAIN认证', ssl: false, port: 143, auth: ['PLAIN'] },
      { name: 'STARTTLS + LOGIN认证', ssl: false, port: 143, auth: ['LOGIN'] },
      { name: '无加密 + PLAIN认证', ssl: false, port: 143, auth: ['PLAIN'] }
    ]

    for (const config of testConfigs) {
      try {
        const response = await fetch('/api/test-imap-auth', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: testData.email,
            password: testData.password,
            host: testData.imapHost,
            port: config.port,
            ssl: config.ssl,
            authMethods: config.auth
          })
        })

        const result = await response.json()
        setResults(prev => [...prev, {
          config: config.name,
          success: result.success,
          message: result.message || result.error,
          details: result.details
        }])
      } catch (error) {
        setResults(prev => [...prev, {
          config: config.name,
          success: false,
          message: `测试失败: ${error}`,
          details: null
        }])
      }

      // 短暂延迟避免服务器压力
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    setTesting(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              🔧 IMAP认证方法测试工具
            </h1>
            <p className="text-gray-600">
              测试不同的IMAP认证配置，找到最适合的连接方式
            </p>
          </div>

          {/* 测试配置 */}
          <div className="mb-8 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                邮箱地址
              </label>
              <input
                type="email"
                value={testData.email}
                onChange={(e) => setTestData(prev => ({ ...prev, email: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                密码
              </label>
              <input
                type="password"
                value={testData.password}
                onChange={(e) => setTestData(prev => ({ ...prev, password: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="密码或应用专用密码"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                IMAP服务器地址
              </label>
              <input
                type="text"
                value={testData.imapHost}
                onChange={(e) => setTestData(prev => ({ ...prev, imapHost: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="imap.example.com"
              />
            </div>

            <button
              onClick={runAuthTest}
              disabled={testing || !testData.email || !testData.password || !testData.imapHost}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {testing ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  正在测试认证方法...
                </>
              ) : (
                <>
                  <Settings className="w-4 h-4" />
                  开始测试
                </>
              )}
            </button>
          </div>

          {/* 测试结果 */}
          {results.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">测试结果</h3>
              
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    result.success
                      ? 'bg-green-50 border-green-200'
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    {result.success ? (
                      <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
                    )}
                    <div className="flex-1">
                      <div className={`font-medium ${
                        result.success ? 'text-green-900' : 'text-red-900'
                      }`}>
                        {result.config}
                      </div>
                      <div className={`text-sm mt-1 ${
                        result.success ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {result.message}
                      </div>
                      {result.details && (
                        <div className="text-xs mt-2 text-gray-600">
                          {result.details}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* 成功配置建议 */}
              {results.some(r => r.success) && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">✅ 推荐配置</h4>
                  <div className="text-blue-800 text-sm">
                    使用测试成功的配置添加邮箱账户。通常第一个成功的配置是最佳选择。
                  </div>
                </div>
              )}

              {/* 全部失败的建议 */}
              {results.length > 0 && !results.some(r => r.success) && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-900 mb-2">⚠️ 所有测试都失败了</h4>
                  <div className="text-yellow-800 text-sm space-y-1">
                    <div>可能的原因：</div>
                    <div>• 邮箱地址或密码错误</div>
                    <div>• IMAP服务器地址不正确</div>
                    <div>• 邮箱未开启IMAP服务</div>
                    <div>• 需要使用应用专用密码</div>
                    <div>• 网络连接问题</div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 返回按钮 */}
          <div className="mt-8 text-center">
            <a
              href="/"
              className="inline-flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              返回邮箱客户端
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
