'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import GmailClient from '@/lib/gmail-client'
import { Check, X, Loader2 } from 'lucide-react'

export default function GoogleAuthCallback() {
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code')
        const error = searchParams.get('error')

        if (error) {
          setStatus('error')
          setMessage(`授权失败: ${error}`)
          return
        }

        if (!code) {
          setStatus('error')
          setMessage('未收到授权码')
          return
        }

        const gmailClient = new GmailClient()
        const result = await gmailClient.handleAuthCallback(code)

        if (result.success) {
          // 保存认证信息到localStorage
          const accountData = {
            id: Date.now().toString(),
            email: result.userProfile.emailAddress,
            name: `Gmail - ${result.userProfile.emailAddress}`,
            provider: 'gmail',
            accessToken: result.tokens.access_token,
            refreshToken: result.tokens.refresh_token,
            expiresAt: result.tokens.expiry_date
          }

          // 获取现有账户列表
          const existingAccounts = JSON.parse(
            localStorage.getItem('emailAccounts') || '[]'
          )

          // 检查是否已存在相同邮箱
          const existingIndex = existingAccounts.findIndex(
            (acc: any) => acc.email === accountData.email && acc.provider === 'gmail'
          )

          if (existingIndex >= 0) {
            // 更新现有账户
            existingAccounts[existingIndex] = accountData
          } else {
            // 添加新账户
            existingAccounts.push(accountData)
          }

          localStorage.setItem('emailAccounts', JSON.stringify(existingAccounts))

          setStatus('success')
          setMessage('Gmail账户授权成功！')

          // 3秒后关闭窗口
          setTimeout(() => {
            window.close()
          }, 3000)
        } else {
          setStatus('error')
          setMessage('授权处理失败')
        }
      } catch (error) {
        console.error('Auth callback error:', error)
        setStatus('error')
        setMessage('授权过程中发生错误')
      }
    }

    handleCallback()
  }, [searchParams])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full mx-4">
        <div className="bg-white rounded-lg shadow-lg p-6 text-center">
          {status === 'loading' && (
            <>
              <Loader2 className="w-12 h-12 mx-auto mb-4 text-blue-600 animate-spin" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                正在处理授权...
              </h2>
              <p className="text-gray-600">
                请稍候，我们正在验证您的Gmail账户
              </p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="w-12 h-12 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
                <Check className="w-6 h-6 text-green-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                授权成功！
              </h2>
              <p className="text-gray-600 mb-4">{message}</p>
              <p className="text-sm text-gray-500">
                窗口将在3秒后自动关闭...
              </p>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-12 h-12 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
                <X className="w-6 h-6 text-red-600" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                授权失败
              </h2>
              <p className="text-gray-600 mb-4">{message}</p>
              <button
                onClick={() => window.close()}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                关闭窗口
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
