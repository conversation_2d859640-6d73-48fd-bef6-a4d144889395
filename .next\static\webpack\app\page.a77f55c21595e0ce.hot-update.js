"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/inbox.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Inbox,Mail,Plus,Search,Send,Settings,Star,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Home() {\n    var _currentEmails_find, _currentEmails_find1, _currentEmails_find2;\n    _s();\n    const [selectedEmail, setSelectedEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCompose, setShowCompose] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddAccount, setShowAddAccount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeAccountId, setActiveAccountId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"1\");\n    // 邮箱账户数据\n    const [emailAccounts, setEmailAccounts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            email: \"<EMAIL>\",\n            provider: \"gmail\",\n            name: \"个人Gmail\",\n            isActive: true,\n            unreadCount: 3\n        },\n        {\n            id: \"2\",\n            email: \"<EMAIL>\",\n            provider: \"outlook\",\n            name: \"工作邮箱\",\n            isActive: false,\n            unreadCount: 1\n        }\n    ]);\n    // 模拟邮件数据\n    const emails = [\n        {\n            id: 1,\n            from: \"<EMAIL>\",\n            subject: \"项目进度更新\",\n            preview: \"关于本周项目进展的详细报告...\",\n            time: \"10:30\",\n            unread: true,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 2,\n            from: \"<EMAIL>\",\n            subject: \"系统维护通知\",\n            preview: \"我们将在今晚进行系统维护...\",\n            time: \"09:15\",\n            unread: false,\n            starred: true,\n            accountId: \"1\"\n        },\n        {\n            id: 3,\n            from: \"<EMAIL>\",\n            subject: \"本周技术资讯\",\n            preview: \"最新的技术趋势和开发工具推荐...\",\n            time: \"昨天\",\n            unread: false,\n            starred: false,\n            accountId: \"1\"\n        },\n        {\n            id: 4,\n            from: \"<EMAIL>\",\n            subject: \"会议安排\",\n            preview: \"明天下午2点开会讨论新项目...\",\n            time: \"11:20\",\n            unread: true,\n            starred: false,\n            accountId: \"2\"\n        }\n    ];\n    // 获取当前活跃账户\n    const activeAccount = emailAccounts.find((acc)=>acc.id === activeAccountId);\n    // 获取当前账户的邮件\n    const currentEmails = emails.filter((email)=>email.accountId === activeAccountId);\n    // 获取邮箱提供商图标\n    const getProviderIcon = (provider)=>{\n        switch(provider){\n            case \"gmail\":\n                return \"\\uD83D\\uDCE7\";\n            case \"outlook\":\n                return \"\\uD83D\\uDCEE\";\n            case \"163\":\n                return \"\\uD83D\\uDCEC\";\n            case \"qq\":\n                return \"\\uD83D\\uDCED\";\n            default:\n                return \"✉️\";\n        }\n    };\n    // 切换邮箱账户\n    const switchAccount = (accountId)=>{\n        setActiveAccountId(accountId);\n        setSelectedEmail(null) // 清除选中的邮件\n        ;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-100 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-semibold text-gray-800\",\n                                        children: \"邮箱客户端\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowCompose(true),\n                                className: \"w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"写邮件\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-medium text-gray-600 mb-2\",\n                                children: \"邮箱账户\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    emailAccounts.map((account)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>switchAccount(account.id),\n                                            className: \"p-2 rounded text-sm cursor-pointer transition-colors \".concat(account.id === activeAccountId ? \"bg-blue-50 text-blue-700 border border-blue-200\" : \"text-gray-600 hover:bg-gray-50\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: getProviderIcon(account.provider)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: account.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs opacity-75\",\n                                                                        children: account.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    account.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full\",\n                                                        children: account.unreadCount\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, account.id, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddAccount(true),\n                                        className: \"w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"添加邮箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 mb-1\",\n                                        children: \"当前账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name) || \"未选择账户\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"收件箱\",\n                                            (activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.unreadCount) && activeAccount.unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: activeAccount.unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已加星标\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"已发送\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"归档\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"垃圾箱\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this),\n                                \"设置\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-96 border-r border-gray-200 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"搜索邮件...\",\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: currentEmails.length > 0 ? currentEmails.map((email)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onClick: ()=>setSelectedEmail(email.id),\n                                className: \"p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 \".concat(selectedEmail === email.id ? \"bg-blue-50 border-blue-200\" : \"\", \" \").concat(email.unread ? \"bg-white\" : \"bg-gray-50\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm \".concat(email.unread ? \"font-semibold text-gray-900\" : \"text-gray-700\"),\n                                                children: email.from\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-500\",\n                                                children: email.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm mb-1 \".concat(email.unread ? \"font-medium text-gray-900\" : \"text-gray-700\"),\n                                        children: email.subject\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-gray-500 line-clamp-2\",\n                                        children: email.preview\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2\",\n                                        children: [\n                                            email.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, this),\n                                            email.starred && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-3 h-3 text-yellow-500 fill-current\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, email.id, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-full text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-12 h-12 mb-2 text-gray-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"此账户暂无邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: selectedEmail ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-4\",\n                                children: (_currentEmails_find = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find === void 0 ? void 0 : _currentEmails_find.subject\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: (_currentEmails_find1 = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find1 === void 0 ? void 0 : _currentEmails_find1.from\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: (_currentEmails_find2 = currentEmails.find((e)=>e.id === selectedEmail)) === null || _currentEmails_find2 === void 0 ? void 0 : _currentEmails_find2.time\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: [\n                                                            \"来自: \",\n                                                            activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name,\n                                                            \" (\",\n                                                            activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-2 text-gray-400 hover:text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: \"这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文， 包括HTML格式、附件等。目前这只是一个演示界面。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed mt-4\",\n                                                children: \"你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。 界面设计简洁明了，便于阅读和操作。\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center text-gray-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Inbox_Mail_Plus_Search_Send_Settings_Star_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-16 h-16 mx-auto mb-4 text-gray-300\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"选择一封邮件来查看内容\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: [\n                                    \"当前账户: \",\n                                    activeAccount === null || activeAccount === void 0 ? void 0 : activeAccount.name\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            showCompose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"写邮件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCompose(false),\n                                    className: \"text-gray-400 hover:text-gray-600\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"收件人\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入收件人邮箱地址\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"邮件主题\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"内容\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            rows: 8,\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"输入邮件内容...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"添加附件\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompose(false),\n                                            className: \"px-4 py-2 text-gray-600 hover:text-gray-800\",\n                                            children: \"取消\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\",\n                                            children: \"发送\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"8Oj2PpNPY/1uOEqHmdxFDc73Aig=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});