"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/url-template";
exports.ids = ["vendor-chunks/url-template"];
exports.modules = {

/***/ "(rsc)/./node_modules/url-template/lib/url-template.js":
/*!*******************************************************!*\
  !*** ./node_modules/url-template/lib/url-template.js ***!
  \*******************************************************/
/***/ ((module) => {

eval("\n(function(root, factory) {\n    if (true) {\n        module.exports = factory();\n    } else {}\n})(void 0, function() {\n    /**\n   * @constructor\n   */ function UrlTemplate() {}\n    /**\n   * @private\n   * @param {string} str\n   * @return {string}\n   */ UrlTemplate.prototype.encodeReserved = function(str) {\n        return str.split(/(%[0-9A-Fa-f]{2})/g).map(function(part) {\n            if (!/%[0-9A-Fa-f]/.test(part)) {\n                part = encodeURI(part).replace(/%5B/g, \"[\").replace(/%5D/g, \"]\");\n            }\n            return part;\n        }).join(\"\");\n    };\n    /**\n   * @private\n   * @param {string} str\n   * @return {string}\n   */ UrlTemplate.prototype.encodeUnreserved = function(str) {\n        return encodeURIComponent(str).replace(/[!'()*]/g, function(c) {\n            return \"%\" + c.charCodeAt(0).toString(16).toUpperCase();\n        });\n    };\n    /**\n   * @private\n   * @param {string} operator\n   * @param {string} value\n   * @param {string} key\n   * @return {string}\n   */ UrlTemplate.prototype.encodeValue = function(operator, value, key) {\n        value = operator === \"+\" || operator === \"#\" ? this.encodeReserved(value) : this.encodeUnreserved(value);\n        if (key) {\n            return this.encodeUnreserved(key) + \"=\" + value;\n        } else {\n            return value;\n        }\n    };\n    /**\n   * @private\n   * @param {*} value\n   * @return {boolean}\n   */ UrlTemplate.prototype.isDefined = function(value) {\n        return value !== undefined && value !== null;\n    };\n    /**\n   * @private\n   * @param {string}\n   * @return {boolean}\n   */ UrlTemplate.prototype.isKeyOperator = function(operator) {\n        return operator === \";\" || operator === \"&\" || operator === \"?\";\n    };\n    /**\n   * @private\n   * @param {Object} context\n   * @param {string} operator\n   * @param {string} key\n   * @param {string} modifier\n   */ UrlTemplate.prototype.getValues = function(context, operator, key, modifier) {\n        var value = context[key], result = [];\n        if (this.isDefined(value) && value !== \"\") {\n            if (typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n                value = value.toString();\n                if (modifier && modifier !== \"*\") {\n                    value = value.substring(0, parseInt(modifier, 10));\n                }\n                result.push(this.encodeValue(operator, value, this.isKeyOperator(operator) ? key : null));\n            } else {\n                if (modifier === \"*\") {\n                    if (Array.isArray(value)) {\n                        value.filter(this.isDefined).forEach(function(value) {\n                            result.push(this.encodeValue(operator, value, this.isKeyOperator(operator) ? key : null));\n                        }, this);\n                    } else {\n                        Object.keys(value).forEach(function(k) {\n                            if (this.isDefined(value[k])) {\n                                result.push(this.encodeValue(operator, value[k], k));\n                            }\n                        }, this);\n                    }\n                } else {\n                    var tmp = [];\n                    if (Array.isArray(value)) {\n                        value.filter(this.isDefined).forEach(function(value) {\n                            tmp.push(this.encodeValue(operator, value));\n                        }, this);\n                    } else {\n                        Object.keys(value).forEach(function(k) {\n                            if (this.isDefined(value[k])) {\n                                tmp.push(this.encodeUnreserved(k));\n                                tmp.push(this.encodeValue(operator, value[k].toString()));\n                            }\n                        }, this);\n                    }\n                    if (this.isKeyOperator(operator)) {\n                        result.push(this.encodeUnreserved(key) + \"=\" + tmp.join(\",\"));\n                    } else if (tmp.length !== 0) {\n                        result.push(tmp.join(\",\"));\n                    }\n                }\n            }\n        } else {\n            if (operator === \";\") {\n                if (this.isDefined(value)) {\n                    result.push(this.encodeUnreserved(key));\n                }\n            } else if (value === \"\" && (operator === \"&\" || operator === \"?\")) {\n                result.push(this.encodeUnreserved(key) + \"=\");\n            } else if (value === \"\") {\n                result.push(\"\");\n            }\n        }\n        return result;\n    };\n    /**\n   * @param {string} template\n   * @return {function(Object):string}\n   */ UrlTemplate.prototype.parse = function(template) {\n        var that = this;\n        var operators = [\n            \"+\",\n            \"#\",\n            \".\",\n            \"/\",\n            \";\",\n            \"?\",\n            \"&\"\n        ];\n        return {\n            expand: function(context) {\n                return template.replace(/\\{([^\\{\\}]+)\\}|([^\\{\\}]+)/g, function(_, expression, literal) {\n                    if (expression) {\n                        var operator = null, values = [];\n                        if (operators.indexOf(expression.charAt(0)) !== -1) {\n                            operator = expression.charAt(0);\n                            expression = expression.substr(1);\n                        }\n                        expression.split(/,/g).forEach(function(variable) {\n                            var tmp = /([^:\\*]*)(?::(\\d+)|(\\*))?/.exec(variable);\n                            values.push.apply(values, that.getValues(context, operator, tmp[1], tmp[2] || tmp[3]));\n                        });\n                        if (operator && operator !== \"+\") {\n                            var separator = \",\";\n                            if (operator === \"?\") {\n                                separator = \"&\";\n                            } else if (operator !== \"#\") {\n                                separator = operator;\n                            }\n                            return (values.length !== 0 ? operator : \"\") + values.join(separator);\n                        } else {\n                            return values.join(\",\");\n                        }\n                    } else {\n                        return that.encodeReserved(literal);\n                    }\n                });\n            }\n        };\n    };\n    return new UrlTemplate();\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/url-template/lib/url-template.js\n");

/***/ })

};
;