# 邮箱客户端

一个支持多邮箱的现代化网页版邮件客户端，使用 Next.js 和 TypeScript 构建。

## 功能特性

- 🎨 现代化的用户界面设计
- 📧 支持多个邮箱账户管理
- 📨 邮件收发功能
- 🔍 邮件搜索
- ⭐ 邮件标星和分类
- 📱 响应式设计

## 技术栈

- **前端框架**: Next.js 14 + React 18
- **样式**: Tailwind CSS
- **语言**: TypeScript
- **图标**: Lucide React
- **开发工具**: ESLint

## 快速开始

### 环境要求

- Node.js 18+ 
- npm 或 yarn

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 项目结构

```
email-client/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── globals.css     # 全局样式
│   │   ├── layout.tsx      # 根布局
│   │   └── page.tsx        # 首页
│   ├── components/         # 可复用组件
│   ├── lib/               # 工具函数
│   └── types/             # TypeScript 类型定义
├── public/                # 静态资源
├── package.json
├── tailwind.config.ts     # Tailwind 配置
├── tsconfig.json          # TypeScript 配置
└── next.config.js         # Next.js 配置
```

## 开发计划

### 第一阶段 ✅
- [x] 项目初始化和基础设置
- [x] 创建基础UI界面

### 第二阶段 🚧
- [ ] 实现邮箱账户管理
- [ ] 集成邮箱API服务
- [ ] 添加邮件功能

### 第三阶段 📋
- [ ] 优化和测试
- [ ] 添加更多邮箱服务支持
- [ ] 性能优化

## 支持的邮箱服务

计划支持以下邮箱服务：

- Gmail (Google)
- Outlook (Microsoft)
- 163邮箱 (网易)
- QQ邮箱 (腾讯)
- 其他支持IMAP/SMTP的邮箱服务

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
