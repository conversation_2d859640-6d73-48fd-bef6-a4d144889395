# Google OAuth2 配置
# 从 Google Cloud Console 获取这些值
# https://console.cloud.google.com/

# Google OAuth2 客户端ID (公开的，可以在前端使用)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id_here

# Google OAuth2 客户端密钥 (私密的，只能在服务器端使用)
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# OAuth2 重定向URI (必须在 Google Cloud Console 中配置)
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/google/callback

# 应用基础URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 其他邮箱服务配置 (可选)
# Microsoft Graph API (Outlook)
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here

# 163邮箱 IMAP/SMTP 配置
NETEASE_IMAP_HOST=imap.163.com
NETEASE_IMAP_PORT=993
NETEASE_SMTP_HOST=smtp.163.com
NETEASE_SMTP_PORT=465

# QQ邮箱 IMAP/SMTP 配置
QQ_IMAP_HOST=imap.qq.com
QQ_IMAP_PORT=993
QQ_SMTP_HOST=smtp.qq.com
QQ_SMTP_PORT=465
