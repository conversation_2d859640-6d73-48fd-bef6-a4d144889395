# 📧 邮箱配置完整指南

## 🚀 快速开始

```bash
npm run dev:integrated
```

访问 http://localhost:3000，点击"添加邮箱"开始配置。

## 📱 添加邮箱步骤

### 1. 输入邮箱地址
- 系统会自动识别邮箱类型
- 自动填充IMAP/SMTP配置
- 生成默认账户名称

### 2. 查看自动配置
系统已预配置以下邮箱服务：

| 邮箱服务 | IMAP服务器 | IMAP端口 | SMTP服务器 | SMTP端口 |
|---------|-----------|---------|-----------|---------|
| Gmail | imap.gmail.com | 993 (SSL) | smtp.gmail.com | 587 (STARTTLS) |
| Outlook | outlook.office365.com | 993 (SSL) | smtp-mail.outlook.com | 587 (STARTTLS) |
| QQ邮箱 | imap.qq.com | 993 (SSL) | smtp.qq.com | 587 (STARTTLS) |
| 163邮箱 | imap.163.com | 993 (SSL) | smtp.163.com | 465 (SSL) |
| 126邮箱 | imap.126.com | 993 (SSL) | smtp.126.com | 465 (SSL) |
| 新浪邮箱 | imap.sina.com | 993 (SSL) | smtp.sina.com | 587 (STARTTLS) |
| 139邮箱 | imap.139.com | 993 (SSL) | smtp.139.com | 587 (STARTTLS) |
| Yeah邮箱 | imap.yeah.net | 993 (SSL) | smtp.yeah.net | 465 (SSL) |

### 3. 自定义配置（高级设置）
对于企业邮箱或其他邮箱服务：

1. **点击"显示高级设置"**
2. **配置IMAP服务器**（接收邮件）：
   - 服务器地址：如 `mail.company.com`
   - 端口：通常是 `993` (SSL) 或 `143` (STARTTLS)
   - 加密方式：SSL/TLS 或 STARTTLS

3. **配置SMTP服务器**（发送邮件）：
   - 服务器地址：如 `smtp.company.com`
   - 端口：通常是 `465` (SSL) 或 `587` (STARTTLS)
   - 加密方式：SSL/TLS 或 STARTTLS

## 🔐 密码设置指南

### Gmail用户 📧
1. **开启两步验证**：
   - 访问 Google账户设置
   - 安全性 → 两步验证 → 开启

2. **生成应用专用密码**：
   - 安全性 → 应用专用密码
   - 选择"邮件"和设备
   - 复制生成的16位密码

3. **使用应用专用密码**：
   - 不要使用Google账户密码
   - 使用生成的16位应用专用密码

### QQ邮箱用户 📮
1. **开启IMAP/SMTP服务**：
   - 登录QQ邮箱网页版
   - 设置 → 账户 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务
   - 开启"IMAP/SMTP服务"

2. **获取授权码**：
   - 点击"生成授权码"
   - 按提示发送短信
   - 复制获得的授权码

3. **使用授权码**：
   - 不要使用QQ密码
   - 使用获得的授权码作为密码

### 163邮箱用户 📬
1. **开启IMAP/SMTP服务**：
   - 登录163邮箱网页版
   - 设置 → POP3/SMTP/IMAP
   - 开启"IMAP/SMTP服务"

2. **设置客户端授权密码**：
   - 点击"客户端授权密码"
   - 设置一个新密码
   - 记住这个密码

3. **使用客户端授权密码**：
   - 不要使用163登录密码
   - 使用设置的客户端授权密码

### 126邮箱用户 📭
- 设置方法与163邮箱相同
- 需要开启IMAP/SMTP服务
- 使用客户端授权密码

### Outlook用户 📫
- **个人账户**：可直接使用登录密码
- **企业账户**：可能需要应用专用密码
- **建议**：设置应用专用密码更安全

## 🏢 企业邮箱配置

### 常见企业邮箱服务商

#### 阿里云邮箱
- **IMAP**: `imap.mxhichina.com:993` (SSL)
- **SMTP**: `smtp.mxhichina.com:465` (SSL)

#### 腾讯企业邮箱
- **IMAP**: `imap.exmail.qq.com:993` (SSL)
- **SMTP**: `smtp.exmail.qq.com:465` (SSL)

#### 自建邮箱服务器
- 通常使用 `mail.domain.com` 或 `smtp.domain.com`
- 端口配置请咨询IT管理员
- 可能需要VPN或内网访问

### 配置步骤
1. 输入企业邮箱地址
2. 点击"显示高级设置"
3. 手动输入IMAP/SMTP配置
4. 输入邮箱密码
5. 点击"测试连接并添加"

## 🔧 常见问题解决

### 连接失败
1. **检查服务器地址**：确保IMAP/SMTP地址正确
2. **检查端口号**：确认端口号和加密方式匹配
3. **检查密码类型**：确保使用正确的密码类型
4. **检查网络**：确保能访问邮箱服务器

### 认证失败
1. **Gmail**: 确保使用应用专用密码
2. **QQ邮箱**: 确保使用授权码
3. **163邮箱**: 确保使用客户端授权密码
4. **企业邮箱**: 咨询IT管理员

### 无法收到邮件
1. **检查IMAP配置**：确保IMAP服务器配置正确
2. **检查权限**：确保邮箱开启了IMAP服务
3. **检查防火墙**：确保网络允许IMAP连接

### 无法发送邮件
1. **检查SMTP配置**：确保SMTP服务器配置正确
2. **检查认证**：确保SMTP需要认证时提供了正确密码
3. **检查端口**：确保SMTP端口和加密方式正确

## 💡 使用技巧

1. **测试连接**：添加前先测试连接，确保配置正确
2. **保存配置**：成功配置后，系统会自动保存设置
3. **多账户**：可以同时添加多个不同的邮箱账户
4. **实时监听**：添加成功后立即开始实时监听新邮件

## 🔐 安全建议

1. **使用应用专用密码**：比登录密码更安全
2. **定期更换密码**：定期更新邮箱密码
3. **本地存储**：所有密码仅在本地存储，不会上传
4. **加密连接**：使用SSL/TLS加密连接

---

**配置完成后，享受你的实时邮箱体验！** 🎉
