@echo off
title 邮箱客户端项目启动器
color 0A

echo ========================================
echo     邮箱客户端项目启动器
echo ========================================
echo.

echo 1. 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装！
    echo 请先安装Node.js：
    echo   - 运行 install-node.ps1 (需要管理员权限)
    echo   - 或手动访问 https://nodejs.org 下载安装
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm未安装！
    pause
    exit /b 1
)

echo ✅ Node.js环境正常

echo.
echo 2. 检查项目依赖...
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败！
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

echo.
echo 3. 启动开发服务器...
echo 🚀 正在启动邮箱客户端...
echo 📱 服务器启动后会自动打开浏览器
echo 🌐 访问地址: http://localhost:3000
echo.
echo 按 Ctrl+C 可以停止服务器
echo ========================================
echo.

npm run dev
