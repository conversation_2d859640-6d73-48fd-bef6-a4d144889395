// 集成服务器 - 将Next.js和邮件服务合并到一个端口
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')
const { Server } = require('socket.io')
const Imap = require('imap')
const { simpleParser } = require('mailparser')
const nodemailer = require('nodemailer')

const dev = process.env.NODE_ENV !== 'production'
const hostname = '0.0.0.0'  // 监听所有网络接口
const port = 3000

// 创建Next.js应用
const app = next({ dev, hostname, port })
const handle = app.getRequestHandler()

// 邮件服务商配置
const EMAIL_PROVIDERS = {
  gmail: {
    name: 'Gmail',
    imap: { host: 'imap.gmail.com', port: 993, secure: true },
    smtp: { host: 'smtp.gmail.com', port: 587, secure: false }
  },
  outlook: {
    name: 'Outlook',
    imap: { host: 'outlook.office365.com', port: 993, secure: true },
    smtp: { host: 'smtp-mail.outlook.com', port: 587, secure: false }
  },
  '163': {
    name: '163邮箱',
    imap: { host: 'imap.163.com', port: 993, secure: true },
    smtp: { host: 'smtp.163.com', port: 465, secure: true }
  },
  qq: {
    name: 'QQ邮箱',
    imap: { host: 'imap.qq.com', port: 993, secure: true },
    smtp: { host: 'smtp.qq.com', port: 587, secure: false }
  },
  '126': {
    name: '126邮箱',
    imap: { host: 'imap.126.com', port: 993, secure: true },
    smtp: { host: 'smtp.126.com', port: 465, secure: true }
  }
}

class IntegratedEmailServer {
  constructor() {
    this.accounts = new Map()
    this.imapConnections = new Map()
    this.emailCache = new Map()
  }

  async start() {
    try {
      // 准备Next.js应用
      await app.prepare()

      // 创建HTTP服务器
      const server = createServer(async (req, res) => {
        try {
          const parsedUrl = parse(req.url, true)
          await handle(req, res, parsedUrl)
        } catch (err) {
          console.error('Error occurred handling', req.url, err)
          res.statusCode = 500
          res.end('internal server error')
        }
      })

      // 创建Socket.IO服务器
      const io = new Server(server, {
        cors: {
          origin: "*",  // 允许所有来源
          methods: ["GET", "POST"],
          credentials: true
        }
      })

      // 设置Socket.IO事件处理
      this.setupSocketHandlers(io)

      // 启动服务器
      server.listen(port, hostname, (err) => {
        if (err) throw err

        // 获取本机IP地址
        const os = require('os')
        const networkInterfaces = os.networkInterfaces()
        let localIP = 'localhost'

        // 查找局域网IP
        for (const interfaceName in networkInterfaces) {
          const interfaces = networkInterfaces[interfaceName]
          for (const iface of interfaces) {
            if (iface.family === 'IPv4' && !iface.internal) {
              localIP = iface.address
              break
            }
          }
          if (localIP !== 'localhost') break
        }

        console.log(`🚀 集成服务器启动成功！`)
        console.log(`📱 本地访问: http://localhost:${port}`)
        console.log(`🌐 局域网访问: http://${localIP}:${port}`)
        console.log(`🔌 WebSocket: ws://${localIP}:${port}`)
        console.log(`📧 邮件服务: 已集成`)
        console.log(``)
        console.log(`📋 局域网内其他设备可通过以下地址访问：`)
        console.log(`   http://${localIP}:${port}`)
      })

    } catch (error) {
      console.error('启动服务器失败:', error)
      process.exit(1)
    }
  }

  setupSocketHandlers(io) {
    io.on('connection', (socket) => {
      console.log('✅ 客户端已连接:', socket.id)

      // 测试邮箱连接
      socket.on('test_email_connection', async (data) => {
        try {
          const result = await this.testEmailConnection(data)
          socket.emit('test_connection_result', result)
        } catch (error) {
          socket.emit('test_connection_result', {
            success: false,
            error: error.message
          })
        }
      })

      // 直接添加邮箱账户（跳过测试）
      socket.on('add_email_account_direct', async (data) => {
        try {
          console.log('🚀 收到直接添加请求，跳过测试:', data.email)
          const result = await this.addEmailAccountDirect(socket, data)
          socket.emit('add_account_result', result)
        } catch (error) {
          socket.emit('add_account_result', {
            success: false,
            error: error.message
          })
        }
      })

      // 添加邮箱账户
      socket.on('add_email_account', async (data) => {
        try {
          const result = await this.addEmailAccount(socket, data)
          socket.emit('add_account_result', result)
        } catch (error) {
          socket.emit('add_account_result', {
            success: false,
            error: error.message
          })
        }
      })

      // 获取邮件列表
      socket.on('get_emails', async (data) => {
        try {
          const emails = await this.getEmails(data.accountId, data.limit || 20)
          socket.emit('emails_response', { success: true, emails })
        } catch (error) {
          socket.emit('emails_response', { success: false, error: error.message })
        }
      })

      // 发送邮件
      socket.on('send_email', async (data) => {
        try {
          await this.sendEmail(data)
          socket.emit('send_email_response', { success: true })
        } catch (error) {
          socket.emit('send_email_response', { success: false, error: error.message })
        }
      })

      socket.on('disconnect', () => {
        console.log('❌ 客户端已断开:', socket.id)
      })
    })
  }

  // 测试邮箱连接 - 支持多种认证方法
  async testEmailConnection(data) {
    const { email, password, provider, customImap } = data

    let imapConfig
    if (customImap && customImap.host) {
      imapConfig = customImap
    } else if (EMAIL_PROVIDERS[provider]) {
      imapConfig = EMAIL_PROVIDERS[provider].imap
    } else {
      throw new Error('请提供IMAP服务器配置')
    }

    // 尝试不同的认证方法配置
    const authConfigs = [
      // 配置1: 标准SSL配置
      {
        tls: true,
        authMethods: ['PLAIN', 'LOGIN'],
        autotls: 'never',
        port: 993
      },
      // 配置2: 强制PLAIN认证
      {
        tls: true,
        authMethods: ['PLAIN'],
        autotls: 'never',
        port: 993
      },
      // 配置3: LOGIN认证
      {
        tls: true,
        authMethods: ['LOGIN'],
        autotls: 'never',
        port: 993
      },
      // 配置4: STARTTLS配置
      {
        tls: false,
        authMethods: ['PLAIN', 'LOGIN'],
        autotls: 'required',
        port: 143
      },
      // 配置5: 无加密配置（最后尝试）
      {
        tls: false,
        authMethods: ['PLAIN', 'LOGIN'],
        autotls: 'never',
        port: 143
      }
    ]

    // 逐个尝试配置
    for (let i = 0; i < authConfigs.length; i++) {
      const config = authConfigs[i]

      try {
        // 使用配置中的端口，如果有的话
        const testConfig = { ...imapConfig }
        if (config.port) {
          testConfig.port = config.port
        }

        const result = await this.tryImapConnection(email, password, testConfig, config, i + 1)
        if (result.success) {
          return result
        }
      } catch (error) {
        console.log(`配置${i + 1}失败:`, error.message)
        if (i === authConfigs.length - 1) {
          // 最后一个配置也失败了
          return {
            success: false,
            error: `所有认证方法都失败了。最后错误: ${error.message}`
          }
        }
      }
    }
  }

  // 尝试单个IMAP连接配置
  async tryImapConnection(email, password, imapConfig, authConfig, configNum) {
    console.log(`🔧 尝试配置${configNum}: ${imapConfig.host}:${imapConfig.port}, TLS: ${authConfig.tls}, 认证: ${authConfig.authMethods.join(', ')}`)

    return new Promise((resolve, reject) => {
      const imap = new Imap({
        user: email,
        password: password,
        host: imapConfig.host,
        port: imapConfig.port,
        tls: authConfig.tls,
        tlsOptions: {
          rejectUnauthorized: false,
          secureProtocol: 'TLSv1_2_method',
          ciphers: 'ALL',
          checkServerIdentity: () => undefined
        },
        authTimeout: 30000,
        connTimeout: 30000,
        keepalive: false,
        autotls: authConfig.autotls,
        authMethods: authConfig.authMethods
      })

      const timeout = setTimeout(() => {
        imap.end()
        reject(new Error(`配置${configNum}连接超时`))
      }, 35000)

      imap.once('ready', () => {
        console.log(`✅ 配置${configNum}连接成功`)
        clearTimeout(timeout)
        imap.end()
        resolve({
          success: true,
          message: `连接成功！使用配置${configNum} - IMAP: ${imapConfig.host}:${imapConfig.port} (${authConfig.authMethods.join(', ')})`,
          config: {
            imap: {
              host: imapConfig.host,
              port: imapConfig.port,
              secure: authConfig.tls
            },
            smtp: {
              host: imapConfig.host.replace('imap', 'smtp'),
              port: authConfig.tls ? 465 : 587,
              secure: authConfig.tls
            },
            authMethods: authConfig.authMethods,
            configNum: configNum
          }
        })
      })

      imap.once('error', (err) => {
        console.log(`❌ 配置${configNum}连接失败:`, err.message)
        clearTimeout(timeout)
        reject(new Error(`配置${configNum}: ${err.message}`))
      })

      imap.once('end', () => {
        console.log(`🔌 配置${configNum}连接已断开`)
      })

      console.log(`🔗 开始连接配置${configNum}...`)
      imap.connect()
    })
  }

  // 直接添加邮箱账户（跳过测试）
  async addEmailAccountDirect(socket, data) {
    const { accountId, email, password, provider, name, customImap, customSmtp } = data

    console.log('🎯 直接添加邮箱，跳过所有测试:', email)

    // 保存账户信息
    this.accounts.set(accountId, {
      id: accountId,
      email,
      password,
      provider,
      name,
      customImap,
      customSmtp,
      socket
    })

    // 直接开始监听邮箱，使用提供的配置
    this.startEmailMonitoringDirect(accountId, email, password, customImap)

    return {
      success: true,
      message: '邮箱账户添加成功！（使用测试成功的配置）'
    }
  }

  // 添加邮箱账户
  async addEmailAccount(socket, data) {
    const { accountId, email, password, provider, name, customImap, customSmtp } = data

    // 先测试连接
    const testResult = await this.testEmailConnection({ email, password, provider, customImap })
    if (!testResult.success) {
      return testResult
    }

    // 保存账户信息
    this.accounts.set(accountId, {
      id: accountId,
      email,
      password,
      provider,
      name,
      customImap,
      customSmtp,
      socket
    })

    // 开始监听邮箱
    this.startEmailMonitoring(accountId, email, password, provider, customImap)

    return {
      success: true,
      message: '邮箱账户添加成功！'
    }
  }

  // 直接开始监听邮箱（使用指定配置）
  startEmailMonitoringDirect(accountId, email, password, imapConfig) {
    console.log('🎯 直接开始监听，使用配置:', imapConfig)

    const imap = new Imap({
      user: email,
      password: password,
      host: imapConfig.host,
      port: imapConfig.port,
      tls: imapConfig.secure,
      tlsOptions: {
        rejectUnauthorized: false,
        secureProtocol: 'TLSv1_2_method',
        checkServerIdentity: () => undefined
      },
      authTimeout: 30000,
      connTimeout: 30000,
      keepalive: false,
      autotls: 'never',
      authMethods: ['PLAIN', 'LOGIN']
    })

    this.setupImapEventHandlers(imap, accountId, email)
    imap.connect()
  }

  // 开始监听邮箱
  startEmailMonitoring(accountId, email, password, provider, customImap) {
    let imapConfig
    if (customImap && customImap.host) {
      imapConfig = customImap
    } else if (EMAIL_PROVIDERS[provider]) {
      imapConfig = EMAIL_PROVIDERS[provider].imap
    } else {
      console.error(`无法获取 ${provider} 的IMAP配置`)
      return
    }

    const imap = new Imap({
      user: email,
      password: password,
      host: imapConfig.host,
      port: imapConfig.port,
      tls: imapConfig.secure,
      tlsOptions: {
        rejectUnauthorized: false,
        secureProtocol: 'TLSv1_2_method'
      },
      authTimeout: 10000,
      connTimeout: 10000,
      // 添加多种认证方法支持
      autotls: 'always',
      // 强制使用PLAIN认证（最兼容）
      authMethods: ['PLAIN', 'LOGIN', 'XOAUTH2']
    })

    imap.once('ready', () => {
      console.log(`📧 开始监听 ${email}`)
      
      imap.openBox('INBOX', false, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err)
          return
        }

        // 监听新邮件
        imap.on('mail', (numNewMsgs) => {
          console.log(`📬 ${email} 收到 ${numNewMsgs} 封新邮件`)
          this.fetchNewEmails(accountId, imap, numNewMsgs)
        })

        // 加载最近邮件
        this.fetchRecentEmails(accountId, imap)
      })
    })

    imap.once('error', (err) => {
      console.error(`❌ ${email} 连接失败:`, err.message)
    })

    this.imapConnections.set(accountId, imap)
    imap.connect()
  }

  // 获取新邮件
  fetchNewEmails(accountId, imap, count) {
    const fetch = imap.seq.fetch(`-${count}:*`, {
      bodies: '',
      struct: true
    })

    fetch.on('message', (msg, seqno) => {
      this.processMessage(accountId, msg, seqno, true)
    })
  }

  // 获取最近邮件
  fetchRecentEmails(accountId, imap, limit = 10) {
    imap.search(['ALL'], (err, results) => {
      if (err || results.length === 0) return

      const recent = results.slice(-limit)
      const fetch = imap.fetch(recent, {
        bodies: '',
        struct: true
      })

      fetch.on('message', (msg, seqno) => {
        this.processMessage(accountId, msg, seqno, false)
      })
    })
  }

  // 处理邮件消息
  processMessage(accountId, msg, seqno, isNew = false) {
    let buffer = ''
    
    msg.on('body', (stream, info) => {
      stream.on('data', (chunk) => {
        buffer += chunk.toString('utf8')
      })
    })

    msg.once('attributes', (attrs) => {
      const uid = attrs.uid
      const flags = attrs.flags || []
      
      msg.once('end', async () => {
        try {
          const parsed = await simpleParser(buffer)
          
          const email = {
            id: uid.toString(),
            from: parsed.from?.text || '',
            to: parsed.to?.text || '',
            subject: parsed.subject || '(无主题)',
            date: parsed.date?.toISOString() || new Date().toISOString(),
            body: parsed.text || parsed.html || '',
            snippet: (parsed.text || '').substring(0, 150) + '...',
            isUnread: !flags.includes('\\Seen'),
            isStarred: flags.includes('\\Flagged'),
            accountId: accountId
          }

          // 缓存邮件
          const cacheKey = `${accountId}:${uid}`
          this.emailCache.set(cacheKey, email)

          // 如果是新邮件，推送给客户端
          if (isNew && email.isUnread) {
            const account = this.accounts.get(accountId)
            if (account && account.socket) {
              account.socket.emit('new_email', email)
            }
          }

        } catch (error) {
          console.error('解析邮件失败:', error)
        }
      })
    })
  }

  // 获取邮件列表
  async getEmails(accountId, limit = 20) {
    const emails = []
    
    for (const [key, email] of this.emailCache.entries()) {
      if (key.startsWith(`${accountId}:`)) {
        emails.push(email)
      }
    }

    emails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    return emails.slice(0, limit)
  }

  // 发送邮件
  async sendEmail(data) {
    const { accountId, to, subject, body } = data
    const account = this.accounts.get(accountId)

    if (!account) {
      throw new Error('账户不存在')
    }

    // 获取SMTP配置
    let smtpConfig
    if (account.customSmtp && account.customSmtp.host) {
      smtpConfig = account.customSmtp
    } else if (EMAIL_PROVIDERS[account.provider]) {
      smtpConfig = EMAIL_PROVIDERS[account.provider].smtp
    } else {
      throw new Error('无法获取SMTP配置')
    }

    const transporter = nodemailer.createTransporter({
      host: smtpConfig.host,
      port: smtpConfig.port,
      secure: smtpConfig.secure,
      auth: {
        user: account.email,
        pass: account.password
      }
    })

    await transporter.sendMail({
      from: account.email,
      to: to,
      subject: subject,
      text: body,
      html: body.replace(/\n/g, '<br>')
    })
  }
}

// 启动集成服务器
const server = new IntegratedEmailServer()
server.start().catch(console.error)
