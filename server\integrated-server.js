// 集成服务器 - 将Next.js和邮件服务合并到一个端口
const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')
const { Server } = require('socket.io')
const Imap = require('imap')
const { simpleParser } = require('mailparser')
const nodemailer = require('nodemailer')

const dev = process.env.NODE_ENV !== 'production'
const hostname = 'localhost'
const port = 3000

// 创建Next.js应用
const app = next({ dev, hostname, port })
const handle = app.getRequestHandler()

// 邮件服务商配置
const EMAIL_PROVIDERS = {
  gmail: {
    name: 'Gmail',
    imap: { host: 'imap.gmail.com', port: 993, secure: true },
    smtp: { host: 'smtp.gmail.com', port: 587, secure: false }
  },
  outlook: {
    name: 'Outlook',
    imap: { host: 'outlook.office365.com', port: 993, secure: true },
    smtp: { host: 'smtp-mail.outlook.com', port: 587, secure: false }
  },
  '163': {
    name: '163邮箱',
    imap: { host: 'imap.163.com', port: 993, secure: true },
    smtp: { host: 'smtp.163.com', port: 465, secure: true }
  },
  qq: {
    name: 'QQ邮箱',
    imap: { host: 'imap.qq.com', port: 993, secure: true },
    smtp: { host: 'smtp.qq.com', port: 587, secure: false }
  },
  '126': {
    name: '126邮箱',
    imap: { host: 'imap.126.com', port: 993, secure: true },
    smtp: { host: 'smtp.126.com', port: 465, secure: true }
  }
}

class IntegratedEmailServer {
  constructor() {
    this.accounts = new Map()
    this.imapConnections = new Map()
    this.emailCache = new Map()
  }

  async start() {
    try {
      // 准备Next.js应用
      await app.prepare()

      // 创建HTTP服务器
      const server = createServer(async (req, res) => {
        try {
          const parsedUrl = parse(req.url, true)
          await handle(req, res, parsedUrl)
        } catch (err) {
          console.error('Error occurred handling', req.url, err)
          res.statusCode = 500
          res.end('internal server error')
        }
      })

      // 创建Socket.IO服务器
      const io = new Server(server, {
        cors: {
          origin: "*",
          methods: ["GET", "POST"]
        }
      })

      // 设置Socket.IO事件处理
      this.setupSocketHandlers(io)

      // 启动服务器
      server.listen(port, (err) => {
        if (err) throw err
        console.log(`🚀 集成服务器启动成功！`)
        console.log(`📱 Web界面: http://${hostname}:${port}`)
        console.log(`🔌 WebSocket: ws://${hostname}:${port}`)
        console.log(`📧 邮件服务: 已集成`)
      })

    } catch (error) {
      console.error('启动服务器失败:', error)
      process.exit(1)
    }
  }

  setupSocketHandlers(io) {
    io.on('connection', (socket) => {
      console.log('✅ 客户端已连接:', socket.id)

      // 测试邮箱连接
      socket.on('test_email_connection', async (data) => {
        try {
          const result = await this.testEmailConnection(data)
          socket.emit('test_connection_result', result)
        } catch (error) {
          socket.emit('test_connection_result', {
            success: false,
            error: error.message
          })
        }
      })

      // 添加邮箱账户
      socket.on('add_email_account', async (data) => {
        try {
          const result = await this.addEmailAccount(socket, data)
          socket.emit('add_account_result', result)
        } catch (error) {
          socket.emit('add_account_result', {
            success: false,
            error: error.message
          })
        }
      })

      // 获取邮件列表
      socket.on('get_emails', async (data) => {
        try {
          const emails = await this.getEmails(data.accountId, data.limit || 20)
          socket.emit('emails_response', { success: true, emails })
        } catch (error) {
          socket.emit('emails_response', { success: false, error: error.message })
        }
      })

      // 发送邮件
      socket.on('send_email', async (data) => {
        try {
          await this.sendEmail(data)
          socket.emit('send_email_response', { success: true })
        } catch (error) {
          socket.emit('send_email_response', { success: false, error: error.message })
        }
      })

      socket.on('disconnect', () => {
        console.log('❌ 客户端已断开:', socket.id)
      })
    })
  }

  // 测试邮箱连接
  async testEmailConnection(data) {
    const { email, password, provider } = data
    
    if (!EMAIL_PROVIDERS[provider]) {
      throw new Error('不支持的邮箱服务商')
    }

    const config = EMAIL_PROVIDERS[provider]
    
    return new Promise((resolve, reject) => {
      const imap = new Imap({
        user: email,
        password: password,
        host: config.imap.host,
        port: config.imap.port,
        tls: config.imap.secure,
        tlsOptions: { rejectUnauthorized: false }
      })

      const timeout = setTimeout(() => {
        imap.end()
        reject(new Error('连接超时'))
      }, 10000)

      imap.once('ready', () => {
        clearTimeout(timeout)
        imap.end()
        resolve({
          success: true,
          message: '连接成功！'
        })
      })

      imap.once('error', (err) => {
        clearTimeout(timeout)
        resolve({
          success: false,
          error: `连接失败: ${err.message}`
        })
      })

      imap.connect()
    })
  }

  // 添加邮箱账户
  async addEmailAccount(socket, data) {
    const { accountId, email, password, provider, name } = data
    
    if (!EMAIL_PROVIDERS[provider]) {
      throw new Error('不支持的邮箱服务商')
    }

    // 先测试连接
    const testResult = await this.testEmailConnection({ email, password, provider })
    if (!testResult.success) {
      return testResult
    }

    // 保存账户信息
    this.accounts.set(accountId, {
      id: accountId,
      email,
      password,
      provider,
      name,
      socket
    })

    // 开始监听邮箱
    this.startEmailMonitoring(accountId, email, password, provider)

    return {
      success: true,
      message: '邮箱账户添加成功！'
    }
  }

  // 开始监听邮箱
  startEmailMonitoring(accountId, email, password, provider) {
    const config = EMAIL_PROVIDERS[provider]
    
    const imap = new Imap({
      user: email,
      password: password,
      host: config.imap.host,
      port: config.imap.port,
      tls: config.imap.secure,
      tlsOptions: { rejectUnauthorized: false }
    })

    imap.once('ready', () => {
      console.log(`📧 开始监听 ${email}`)
      
      imap.openBox('INBOX', false, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err)
          return
        }

        // 监听新邮件
        imap.on('mail', (numNewMsgs) => {
          console.log(`📬 ${email} 收到 ${numNewMsgs} 封新邮件`)
          this.fetchNewEmails(accountId, imap, numNewMsgs)
        })

        // 加载最近邮件
        this.fetchRecentEmails(accountId, imap)
      })
    })

    imap.once('error', (err) => {
      console.error(`❌ ${email} 连接失败:`, err.message)
    })

    this.imapConnections.set(accountId, imap)
    imap.connect()
  }

  // 获取新邮件
  fetchNewEmails(accountId, imap, count) {
    const fetch = imap.seq.fetch(`-${count}:*`, {
      bodies: '',
      struct: true
    })

    fetch.on('message', (msg, seqno) => {
      this.processMessage(accountId, msg, seqno, true)
    })
  }

  // 获取最近邮件
  fetchRecentEmails(accountId, imap, limit = 10) {
    imap.search(['ALL'], (err, results) => {
      if (err || results.length === 0) return

      const recent = results.slice(-limit)
      const fetch = imap.fetch(recent, {
        bodies: '',
        struct: true
      })

      fetch.on('message', (msg, seqno) => {
        this.processMessage(accountId, msg, seqno, false)
      })
    })
  }

  // 处理邮件消息
  processMessage(accountId, msg, seqno, isNew = false) {
    let buffer = ''
    
    msg.on('body', (stream, info) => {
      stream.on('data', (chunk) => {
        buffer += chunk.toString('utf8')
      })
    })

    msg.once('attributes', (attrs) => {
      const uid = attrs.uid
      const flags = attrs.flags || []
      
      msg.once('end', async () => {
        try {
          const parsed = await simpleParser(buffer)
          
          const email = {
            id: uid.toString(),
            from: parsed.from?.text || '',
            to: parsed.to?.text || '',
            subject: parsed.subject || '(无主题)',
            date: parsed.date?.toISOString() || new Date().toISOString(),
            body: parsed.text || parsed.html || '',
            snippet: (parsed.text || '').substring(0, 150) + '...',
            isUnread: !flags.includes('\\Seen'),
            isStarred: flags.includes('\\Flagged'),
            accountId: accountId
          }

          // 缓存邮件
          const cacheKey = `${accountId}:${uid}`
          this.emailCache.set(cacheKey, email)

          // 如果是新邮件，推送给客户端
          if (isNew && email.isUnread) {
            const account = this.accounts.get(accountId)
            if (account && account.socket) {
              account.socket.emit('new_email', email)
            }
          }

        } catch (error) {
          console.error('解析邮件失败:', error)
        }
      })
    })
  }

  // 获取邮件列表
  async getEmails(accountId, limit = 20) {
    const emails = []
    
    for (const [key, email] of this.emailCache.entries()) {
      if (key.startsWith(`${accountId}:`)) {
        emails.push(email)
      }
    }

    emails.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    return emails.slice(0, limit)
  }

  // 发送邮件
  async sendEmail(data) {
    const { accountId, to, subject, body } = data
    const account = this.accounts.get(accountId)
    
    if (!account) {
      throw new Error('账户不存在')
    }

    const config = EMAIL_PROVIDERS[account.provider]
    if (!config) {
      throw new Error('不支持的邮件服务商')
    }

    const transporter = nodemailer.createTransporter({
      host: config.smtp.host,
      port: config.smtp.port,
      secure: config.smtp.secure,
      auth: {
        user: account.email,
        pass: account.password
      }
    })

    await transporter.sendMail({
      from: account.email,
      to: to,
      subject: subject,
      text: body,
      html: body.replace(/\n/g, '<br>')
    })
  }
}

// 启动集成服务器
const server = new IntegratedEmailServer()
server.start().catch(console.error)
