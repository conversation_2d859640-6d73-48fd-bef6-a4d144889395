// 网络连接测试工具
const net = require('net')
const dns = require('dns')

async function testNetworkConnection(host, port) {
  console.log(`🌐 测试网络连接: ${host}:${port}`)
  console.log('=====================================')

  // 1. DNS解析测试
  console.log('1. DNS解析测试...')
  try {
    const addresses = await new Promise((resolve, reject) => {
      dns.lookup(host, { all: true }, (err, addresses) => {
        if (err) reject(err)
        else resolve(addresses)
      })
    })
    
    console.log(`✅ DNS解析成功:`)
    addresses.forEach(addr => {
      console.log(`   ${addr.address} (${addr.family === 4 ? 'IPv4' : 'IPv6'})`)
    })
  } catch (error) {
    console.log(`❌ DNS解析失败: ${error.message}`)
    return false
  }

  // 2. TCP连接测试
  console.log('\n2. TCP连接测试...')
  try {
    const connected = await new Promise((resolve, reject) => {
      const socket = new net.Socket()
      const timeout = setTimeout(() => {
        socket.destroy()
        reject(new Error('连接超时'))
      }, 10000)

      socket.connect(port, host, () => {
        clearTimeout(timeout)
        console.log(`✅ TCP连接成功: ${host}:${port}`)
        socket.end()
        resolve(true)
      })

      socket.on('error', (err) => {
        clearTimeout(timeout)
        reject(err)
      })
    })
    
    return connected
  } catch (error) {
    console.log(`❌ TCP连接失败: ${error.message}`)
    return false
  }
}

async function testCommonPorts(host) {
  console.log(`\n🔍 测试常用邮箱端口: ${host}`)
  console.log('=====================================')
  
  const ports = [
    { port: 993, desc: 'IMAP SSL' },
    { port: 143, desc: 'IMAP STARTTLS' },
    { port: 465, desc: 'SMTP SSL' },
    { port: 587, desc: 'SMTP STARTTLS' },
    { port: 25, desc: 'SMTP' }
  ]

  for (const { port, desc } of ports) {
    try {
      const result = await testNetworkConnection(host, port)
      console.log(`${result ? '✅' : '❌'} ${desc} (${port}): ${result ? '可连接' : '无法连接'}`)
    } catch (error) {
      console.log(`❌ ${desc} (${port}): ${error.message}`)
    }
  }
}

async function main() {
  const args = process.argv.slice(2)
  
  if (args.length === 0) {
    console.log('用法: node test-network.js <邮箱服务器地址> [端口]')
    console.log('例如: node test-network.js mail2.serv00.com 993')
    console.log('或者: node test-network.js mail2.serv00.com (测试所有常用端口)')
    return
  }

  const host = args[0]
  const port = args[1]

  if (port) {
    // 测试指定端口
    await testNetworkConnection(host, parseInt(port))
  } else {
    // 测试所有常用端口
    await testCommonPorts(host)
  }

  console.log('\n🎯 建议:')
  console.log('- 如果DNS解析失败，检查域名是否正确')
  console.log('- 如果TCP连接失败，检查防火墙设置')
  console.log('- 如果连接超时，可能是网络问题或服务器不可达')
  console.log('- 确保使用正确的端口号')
}

if (require.main === module) {
  main().catch(console.error)
}

module.exports = { testNetworkConnection, testCommonPorts }
