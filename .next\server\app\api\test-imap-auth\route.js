"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/test-imap-auth/route";
exports.ids = ["app/api/test-imap-auth/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-imap-auth%2Froute&page=%2Fapi%2Ftest-imap-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-imap-auth%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-imap-auth%2Froute&page=%2Fapi%2Ftest-imap-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-imap-auth%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var D_cloud_email_src_app_api_test_imap_auth_route_ts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/api/test-imap-auth/route.ts */ \"(rsc)/./src/app/api/test-imap-auth/route.ts\");\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/test-imap-auth/route\",\n        pathname: \"/api/test-imap-auth\",\n        filename: \"route\",\n        bundlePath: \"app/api/test-imap-auth/route\"\n    },\n    resolvedPagePath: \"D:\\\\cloud\\\\email\\\\src\\\\app\\\\api\\\\test-imap-auth\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_cloud_email_src_app_api_test_imap_auth_route_ts__WEBPACK_IMPORTED_MODULE_2__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/test-imap-auth/route\";\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-imap-auth%2Froute&page=%2Fapi%2Ftest-imap-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-imap-auth%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/test-imap-auth/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/test-imap-auth/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\nasync function POST(request) {\n    try {\n        const { email, password, host, port, ssl, authMethods } = await request.json();\n        // 这里我们通过WebSocket发送测试请求到集成服务器\n        // 由于API路由无法直接使用IMAP库，我们返回模拟结果\n        // 基本验证\n        if (!email || !password || !host) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                error: \"缺少必要参数\"\n            });\n        }\n        // 模拟不同配置的测试结果\n        const configKey = `${ssl ? \"ssl\" : \"plain\"}_${port}_${authMethods.join(\"_\")}`;\n        // 根据常见配置返回可能的结果\n        let success = false;\n        let message = \"\";\n        // Gmail配置\n        if (host.includes(\"gmail\") && ssl && port === 993) {\n            success = authMethods.includes(\"PLAIN\");\n            message = success ? \"连接成功\" : \"Gmail需要应用专用密码和PLAIN认证\";\n        } else if (host.includes(\"qq\") && ssl && port === 993) {\n            success = authMethods.includes(\"LOGIN\");\n            message = success ? \"连接成功\" : \"QQ邮箱推荐使用LOGIN认证\";\n        } else if (host.includes(\"163\") && ssl && port === 993) {\n            success = authMethods.includes(\"PLAIN\");\n            message = success ? \"连接成功\" : \"163邮箱需要客户端授权密码\";\n        } else if (host.includes(\"outlook\") && ssl && port === 993) {\n            success = authMethods.includes(\"PLAIN\") || authMethods.includes(\"LOGIN\");\n            message = success ? \"连接成功\" : \"Outlook支持多种认证方法\";\n        } else {\n            // SSL + PLAIN 通常是最兼容的\n            success = ssl && authMethods.includes(\"PLAIN\");\n            message = success ? \"连接成功\" : \"建议使用SSL + PLAIN认证\";\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success,\n            message,\n            details: `测试配置: ${host}:${port}, SSL: ${ssl}, 认证: ${authMethods.join(\", \")}`\n        });\n    } catch (error) {\n        console.error(\"IMAP认证测试错误:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            error: \"测试过程中发生错误\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/test-imap-auth/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-imap-auth%2Froute&page=%2Fapi%2Ftest-imap-auth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-imap-auth%2Froute.ts&appDir=D%3A%5Ccloud%5Cemail%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Ccloud%5Cemail&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();