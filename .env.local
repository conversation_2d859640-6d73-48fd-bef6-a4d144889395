# Google OAuth2 配置 (示例配置，需要替换为真实值)
# 从 Google Cloud Console 获取这些值: https://console.cloud.google.com/

# Google OAuth2 客户端ID (公开的，可以在前端使用)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com

# Google OAuth2 客户端密钥 (私密的，只能在服务器端使用)
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# OAuth2 重定向URI (必须在 Google Cloud Console 中配置)
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# 应用基础URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
