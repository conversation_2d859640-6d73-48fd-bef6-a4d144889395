# Google OAuth2 配置
# 从 Google Cloud Console 获取这些值: https://console.cloud.google.com/

# Google OAuth2 客户端ID (公开的，可以在前端使用)
# 请将下面的值替换为你从Google Cloud Console获取的真实客户端ID
NEXT_PUBLIC_GOOGLE_CLIENT_ID=请替换为你的客户端ID.apps.googleusercontent.com

# Google OAuth2 客户端密钥 (私密的，只能在服务器端使用)
# 请将下面的值替换为你从Google Cloud Console获取的真实客户端密钥
GOOGLE_CLIENT_SECRET=请替换为你的客户端密钥

# OAuth2 重定向URI (必须在 Google Cloud Console 中配置)
NEXT_PUBLIC_GOOGLE_REDIRECT_URI=http://localhost:3000/auth/google/callback

# 应用基础URL
NEXT_PUBLIC_APP_URL=http://localhost:3000
