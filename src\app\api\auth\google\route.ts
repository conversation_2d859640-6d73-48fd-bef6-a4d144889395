import { NextRequest, NextResponse } from 'next/server'
import GmailService from '@/lib/gmail-api'

// 获取Google OAuth授权URL
export async function GET() {
  try {
    const gmailService = new GmailService()
    const authUrl = gmailService.getAuthUrl()
    
    return NextResponse.json({ authUrl })
  } catch (error) {
    console.error('Error generating auth URL:', error)
    return NextResponse.json(
      { error: 'Failed to generate authorization URL' },
      { status: 500 }
    )
  }
}

// 处理OAuth回调
export async function POST(request: NextRequest) {
  try {
    const { code } = await request.json()
    
    if (!code) {
      return NextResponse.json(
        { error: 'Authorization code is required' },
        { status: 400 }
      )
    }

    const gmailService = new GmailService()
    const tokens = await gmailService.getAccessToken(code)
    
    // 获取用户信息
    const userProfile = await gmailService.getUserProfile()
    
    return NextResponse.json({
      tokens,
      userProfile,
      success: true
    })
  } catch (error) {
    console.error('Error handling OAuth callback:', error)
    return NextResponse.json(
      { error: 'Failed to authenticate with Google' },
      { status: 500 }
    )
  }
}
