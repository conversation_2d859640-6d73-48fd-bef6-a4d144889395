"use strict";

module.exports = [
  {
    buf: [0],
    rfc4648: ["AA", "aa"],
    crock32: ["00", "0O", "0o"],
    crock32int: ["0", "O", "o"],
    base32hex: ["00"]
  },
  {
    buf: [1],
    rfc4648: ["AE"],
    crock32: ["04"],
    crock32int: ["1", "I", "i", "L", "l"],
    base32hex: ["04"]
  },
  {
    buf: [2],
    rfc4648: ["AI", "ai", "aI", "Ai"],
    crock32: ["08"],
    crock32int: ["2"],
    base32hex: ["08"]
  },
  {
    buf: [3],
    rfc4648: ["AM", "am", "aM", "Am"],
    crock32: ["0C"],
    crock32int: ["3"],
    base32hex: ["0C"]
  },
  {
    buf: [4],
    rfc4648: ["AQ", "aq", "aQ", "Aq"],
    crock32: ["0G"],
    crock32int: ["4"],
    base32hex: ["0G"]
  },
  {
    buf: [5],
    rfc4648: ["AU", "au", "aU", "Au"],
    crock32: ["0M"],
    crock32int: ["5"],
    base32hex: ["0K"]
  },
  {
    buf: [6],
    rfc4648: ["AY", "ay", "aY", "Ay"],
    crock32: ["0R"],
    crock32int: ["6"],
    base32hex: ["0O"]
  },
  {
    buf: [7],
    rfc4648: ["A4", "a4"],
    crock32: ["0W"],
    crock32int: ["7"],
    base32hex: ["0S"]
  },
  {
    buf: [8],
    rfc4648: ["BA", "ba", "bA", "Ba"],
    crock32: ["10"],
    crock32int: ["8"],
    base32hex: ["10"]
  },
  {
    buf: [9],
    rfc4648: ["BE", "be", "bE", "Be"],
    crock32: ["14"],
    crock32int: ["9"],
    base32hex: ["14"]
  },
  {
    buf: [10],
    rfc4648: ["BI", "bi", "bI", "Bi"],
    crock32: ["18"],
    crock32int: ["A", "a"],
    base32hex: ["18"]
  },
  {
    buf: [11],
    rfc4648: ["BM", "bm", "bM", "Bm"],
    crock32: ["1C"],
    crock32int: ["B", "b"],
    base32hex: ["1C"]
  },
  {
    buf: [12],
    rfc4648: ["BQ", "bq", "bQ", "Bq"],
    crock32: ["1G"],
    crock32int: ["C", "c"],
    base32hex: ["1G"]
  },
  {
    buf: [13],
    rfc4648: ["BU", "bu", "bU", "Bu"],
    crock32: ["1M"],
    crock32int: ["D", "d"],
    base32hex: ["1K"]
  },
  {
    buf: [14],
    rfc4648: ["BY", "by", "bY", "By"],
    crock32: ["1R"],
    crock32int: ["E", "e"],
    base32hex: ["1O"]
  },
  {
    buf: [15],
    rfc4648: ["B4", "b4"],
    crock32: ["1W"],
    crock32int: ["F", "f"],
    base32hex: ["1S"]
  },
  {
    buf: [16],
    rfc4648: ["CA", "ca", "cA", "Ca"],
    crock32: ["20"],
    crock32int: ["G", "g"],
    base32hex: ["20"]
  },
  {
    buf: [17],
    rfc4648: ["CE", "ce", "cE", "Ce"],
    crock32: ["24"],
    crock32int: ["H", "h"],
    base32hex: ["24"]
  },
  {
    buf: [18],
    rfc4648: ["CI", "ci", "cI", "Ci"],
    crock32: ["28"],
    crock32int: ["J", "j"],
    base32hex: ["28"]
  },
  {
    buf: [19],
    rfc4648: ["CM", "cm", "cM", "Cm"],
    crock32: ["2C"],
    crock32int: ["K", "k"],
    base32hex: ["2C"]
  },
  {
    buf: [20],
    rfc4648: ["CQ", "cq", "cQ", "Cq"],
    crock32: ["2G"],
    crock32int: ["M", "m"],
    base32hex: ["2G"]
  },
  {
    buf: [21],
    rfc4648: ["CU", "cu", "cU", "Cu"],
    crock32: ["2M"],
    crock32int: ["N", "n"],
    base32hex: ["2K"]
  },
  {
    buf: [22],
    rfc4648: ["CY", "cy", "cY", "Cy"],
    crock32: ["2R"],
    crock32int: ["P", "p"],
    base32hex: ["2O"]
  },
  {
    buf: [23],
    rfc4648: ["C4", "c4"],
    crock32: ["2W"],
    crock32int: ["Q", "q"],
    base32hex: ["2S"]
  },
  {
    buf: [24],
    rfc4648: ["DA", "da", "dA", "Da"],
    crock32: ["30"],
    crock32int: ["R", "r"],
    base32hex: ["30"]
  },
  {
    buf: [25],
    rfc4648: ["DE", "de", "dE", "De"],
    crock32: ["34"],
    crock32int: ["S", "s"],
    base32hex: ["34"]
  },
  {
    buf: [26],
    rfc4648: ["DI", "di", "dI", "Di"],
    crock32: ["38"],
    crock32int: ["T", "t"],
    base32hex: ["38"]
  },
  {
    buf: [27],
    rfc4648: ["DM", "dm", "dM", "Dm"],
    crock32: ["3C"],
    crock32int: ["V", "v"],
    base32hex: ["3C"]
  },
  {
    buf: [28],
    rfc4648: ["DQ", "dq", "dQ", "Dq"],
    crock32: ["3G"],
    crock32int: ["W", "w"],
    base32hex: ["3G"]
  },
  {
    buf: [29],
    rfc4648: ["DU", "du", "dU", "Du"],
    crock32: ["3M"],
    crock32int: ["X", "x"],
    base32hex: ["3k"]
  },
  {
    buf: [30],
    rfc4648: ["DY", "dy", "dY", "Dy"],
    crock32: ["3R"],
    crock32int: ["Y", "y"],
    base32hex: ["3O"]
  },
  {
    buf: [31],
    rfc4648: ["D4", "d4"],
    crock32: ["3W"],
    crock32int: ["Z", "z"],
    base32hex: ["3S"]
  },
  {
    buf: [0, 0],
    rfc4648: ["AAAA", "aaaa", "AaAa", "aAAa"],
    crock32: ["0000", "oooo", "OOOO", "0oO0"],
    base32hex: ["0000"]
  },
  {
    buf: [1, 0],
    rfc4648: ["AEAA", "aeaa", "AeAa", "aEAa"],
    crock32: ["0400", "o4oo", "O4OO", "04oO"],
    base32hex: ["0400"]
  },
  {
    buf: [0, 1],
    rfc4648: ["AAAQ", "aaaq", "AaAQ", "aAAq"],
    crock32: ["000G", "ooog", "OOOG", "0oOg"],
    base32hex: ["000G"]
  },
  {
    buf: [1, 1],
    rfc4648: ["AEAQ", "aeaq", "AeAQ", "aEAq"],
    crock32: ["040G", "o4og", "O4og", "04Og"],
    base32hex: ["040G"]
  },
  {
    buf: [136, 64],
    rfc4648: ["RBAA", "rbaa", "RbAA", "rBAa"],
    crock32: ["H100", "hio0", "HLOo"],
    base32hex: ["H100"]
  },
  {
    buf: [139, 188],
    rfc4648: ["RO6A", "r06a", "Ro6A", "r06A"],
    crock32: ["HEY0", "heyo", "HeYO"],
    base32hex: ["HEU0"]
  },
  {
    buf: [54, 31, 127],
    rfc4648: ["GYPX6", "gypx6"],
    crock32: ["6RFQY", "6rfqy"],
    base32hex: ["6OFNU"]
  },
  {
    buf: [72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33],
    rfc4648: ["JBSWY3DPEBLW64TMMQQQ", "jbswy3dpeblw64tmmqqq"],
    crock32: ["91JPRV3F41BPYWKCCGGG", "91jprv3f41bpywkccggg", "9Ljprv3f4ibpywkccggg"],
    base32hex: ["91IMOR3F41BMUSJCCGGG"]
  },
  {
    buf: [139, 130, 16, 112, 24, 11, 64],
    rfc4648: ["ROBBA4AYBNAA", "robba4aybnaa", "R0BBA4aybnaa"],
    crock32: ["HE110W0R1D00", "helloworld00", "heiiOw0RidoO"],
    base32hex: ["HE110S0O1D00"]
  },
  {
    buf: [139, 130, 16, 112, 24, 11],
    rfc4648: ["ROBBA4AYBM", "robba4aybm", "R0BBA4aybm"],
    crock32: ["HE110W0R1C", "helloworlc", "heiiOw0RiC"],
    base32hex: ["HE110S0O1C"]
  },
  {
    buf: [139, 130, 16, 112, 24, 11, 0],
    rfc4648: ["ROBBA4AYBMAA", "robba4aybmaa", "R0BBA4aybmaa"],
    crock32: ["HE110W0R1C00", "helloworlc00", "heiiOw0RiC00"],
    base32hex: ["HE110S0O1C00"]
  }
];
