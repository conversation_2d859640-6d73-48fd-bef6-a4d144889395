"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/gaxios";
exports.ids = ["vendor-chunks/gaxios"];
exports.modules = {

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/common.js":
/*!*****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/common.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaxiosError = exports.GAXIOS_ERROR_SYMBOL = void 0;\nexports.defaultErrorRedactor = defaultErrorRedactor;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\nconst util_cjs_1 = __importDefault(__webpack_require__(/*! ./util.cjs */ \"(rsc)/./node_modules/gaxios/build/cjs/src/util.cjs\"));\nconst pkg = util_cjs_1.default.pkg;\n/**\n * Support `instanceof` operator for `GaxiosError`s in different versions of this library.\n *\n * @see {@link GaxiosError[Symbol.hasInstance]}\n */ exports.GAXIOS_ERROR_SYMBOL = Symbol.for(`${pkg.name}-gaxios-error`);\nlet prop;\nclass GaxiosError extends Error {\n    static{\n        prop = exports.GAXIOS_ERROR_SYMBOL;\n    }\n    /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[GAXIOS_ERROR_SYMBOL]}\n     */ static [Symbol.hasInstance](instance) {\n        if (instance && typeof instance === \"object\" && exports.GAXIOS_ERROR_SYMBOL in instance && instance[exports.GAXIOS_ERROR_SYMBOL] === pkg.version) {\n            return true;\n        }\n        // fallback to native\n        return Function.prototype[Symbol.hasInstance].call(GaxiosError, instance);\n    }\n    constructor(message, config, response, cause){\n        super(message, {\n            cause\n        });\n        /**\n     * Support `instanceof` operator for `GaxiosError` across builds/duplicated files.\n     *\n     * @see {@link GAXIOS_ERROR_SYMBOL}\n     * @see {@link GaxiosError[Symbol.hasInstance]}\n     * @see {@link https://github.com/microsoft/TypeScript/issues/13965#issuecomment-278570200}\n     * @see {@link https://stackoverflow.com/questions/46618852/require-and-instanceof}\n     * @see {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/@@hasInstance#reverting_to_default_instanceof_behavior}\n     */ this[prop] = pkg.version;\n        this.config = config;\n        this.response = response;\n        this.error = cause instanceof Error ? cause : undefined;\n        // deep-copy config as we do not want to mutate\n        // the existing config for future retries/use\n        this.config = (0, extend_1.default)(true, {}, config);\n        if (this.response) {\n            this.response.config = (0, extend_1.default)(true, {}, this.response.config);\n        }\n        if (this.response) {\n            try {\n                this.response.data = translateData(this.config.responseType, // workaround for `node-fetch`'s `.data` deprecation...\n                this.response?.bodyUsed ? this.response?.data : undefined);\n            } catch  {\n            // best effort - don't throw an error within an error\n            // we could set `this.response.config.responseType = 'unknown'`, but\n            // that would mutate future calls with this config object.\n            }\n            this.status = this.response.status;\n        }\n        if (cause instanceof DOMException) {\n            // The DOMException's equivalent to code is its name\n            // E.g.: name = `TimeoutError`, code = number\n            // https://developer.mozilla.org/en-US/docs/Web/API/DOMException/name\n            this.code = cause.name;\n        } else if (cause && typeof cause === \"object\" && \"code\" in cause && (typeof cause.code === \"string\" || typeof cause.code === \"number\")) {\n            this.code = cause.code;\n        }\n    }\n    /**\n     * An AIP-193 conforming error extractor.\n     *\n     * @see {@link https://google.aip.dev/193#http11json-representation AIP-193}\n     *\n     * @internal\n     * @expiremental\n     *\n     * @param res the response object\n     * @returns the extracted error information\n     */ static extractAPIErrorFromResponse(res, defaultErrorMessage = \"The request failed\") {\n        let message = defaultErrorMessage;\n        // Use res.data as the error message\n        if (typeof res.data === \"string\") {\n            message = res.data;\n        }\n        if (res.data && typeof res.data === \"object\" && \"error\" in res.data && res.data.error && !res.ok) {\n            if (typeof res.data.error === \"string\") {\n                return {\n                    message: res.data.error,\n                    code: res.status,\n                    status: res.statusText\n                };\n            }\n            if (typeof res.data.error === \"object\") {\n                // extract status from data.message\n                message = \"message\" in res.data.error && typeof res.data.error.message === \"string\" ? res.data.error.message : message;\n                // extract status from data.error\n                const status = \"status\" in res.data.error && typeof res.data.error.status === \"string\" ? res.data.error.status : res.statusText;\n                // extract code from data.error\n                const code = \"code\" in res.data.error && typeof res.data.error.code === \"number\" ? res.data.error.code : res.status;\n                if (\"errors\" in res.data.error && Array.isArray(res.data.error.errors)) {\n                    const errorMessages = [];\n                    for (const e of res.data.error.errors){\n                        if (typeof e === \"object\" && \"message\" in e && typeof e.message === \"string\") {\n                            errorMessages.push(e.message);\n                        }\n                    }\n                    return Object.assign({\n                        message: errorMessages.join(\"\\n\") || message,\n                        code,\n                        status\n                    }, res.data.error);\n                }\n                return Object.assign({\n                    message,\n                    code,\n                    status\n                }, res.data.error);\n            }\n        }\n        return {\n            message,\n            code: res.status,\n            status: res.statusText\n        };\n    }\n}\nexports.GaxiosError = GaxiosError;\nfunction translateData(responseType, data) {\n    switch(responseType){\n        case \"stream\":\n            return data;\n        case \"json\":\n            return JSON.parse(JSON.stringify(data));\n        case \"arraybuffer\":\n            return JSON.parse(Buffer.from(data).toString(\"utf8\"));\n        case \"blob\":\n            return JSON.parse(data.text());\n        default:\n            return data;\n    }\n}\n/**\n * An experimental error redactor.\n *\n * @param config Config to potentially redact properties of\n * @param response Config to potentially redact properties of\n *\n * @experimental\n */ function defaultErrorRedactor(data) {\n    const REDACT = \"<<REDACTED> - See `errorRedactor` option in `gaxios` for configuration>.\";\n    function redactHeaders(headers) {\n        if (!headers) return;\n        headers.forEach((_, key)=>{\n            // any casing of `Authentication`\n            // any casing of `Authorization`\n            // anything containing secret, such as 'client secret'\n            if (/^authentication$/i.test(key) || /^authorization$/i.test(key) || /secret/i.test(key)) headers.set(key, REDACT);\n        });\n    }\n    function redactString(obj, key) {\n        if (typeof obj === \"object\" && obj !== null && typeof obj[key] === \"string\") {\n            const text = obj[key];\n            if (/grant_type=/i.test(text) || /assertion=/i.test(text) || /secret/i.test(text)) {\n                obj[key] = REDACT;\n            }\n        }\n    }\n    function redactObject(obj) {\n        if (!obj || typeof obj !== \"object\") {\n            return;\n        } else if (obj instanceof FormData || obj instanceof URLSearchParams || // support `node-fetch` FormData/URLSearchParams\n        \"forEach\" in obj && \"set\" in obj) {\n            obj.forEach((_, key)=>{\n                if ([\n                    \"grant_type\",\n                    \"assertion\"\n                ].includes(key) || /secret/.test(key)) {\n                    obj.set(key, REDACT);\n                }\n            });\n        } else {\n            if (\"grant_type\" in obj) {\n                obj[\"grant_type\"] = REDACT;\n            }\n            if (\"assertion\" in obj) {\n                obj[\"assertion\"] = REDACT;\n            }\n            if (\"client_secret\" in obj) {\n                obj[\"client_secret\"] = REDACT;\n            }\n        }\n    }\n    if (data.config) {\n        redactHeaders(data.config.headers);\n        redactString(data.config, \"data\");\n        redactObject(data.config.data);\n        redactString(data.config, \"body\");\n        redactObject(data.config.body);\n        if (data.config.url.searchParams.has(\"token\")) {\n            data.config.url.searchParams.set(\"token\", REDACT);\n        }\n        if (data.config.url.searchParams.has(\"client_secret\")) {\n            data.config.url.searchParams.set(\"client_secret\", REDACT);\n        }\n    }\n    if (data.response) {\n        defaultErrorRedactor({\n            config: data.response.config\n        });\n        redactHeaders(data.response.headers);\n        // workaround for `node-fetch`'s `.data` deprecation...\n        if (data.response.bodyUsed) {\n            redactString(data.response, \"data\");\n            redactObject(data.response.data);\n        }\n    }\n    return data;\n} //# sourceMappingURL=common.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/gaxios.js":
/*!*****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/gaxios.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Gaxios = void 0;\nconst extend_1 = __importDefault(__webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\"));\nconst https_1 = __webpack_require__(/*! https */ \"https\");\nconst common_js_1 = __webpack_require__(/*! ./common.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/common.js\");\nconst retry_js_1 = __webpack_require__(/*! ./retry.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/retry.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst interceptor_js_1 = __webpack_require__(/*! ./interceptor.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js\");\nconst randomUUID = async ()=>globalThis.crypto?.randomUUID() || (await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! crypto */ \"crypto\", 23))).randomUUID();\nclass Gaxios {\n    /**\n     * The Gaxios class is responsible for making HTTP requests.\n     * @param defaults The default set of options to be used for this instance.\n     */ constructor(defaults){\n        this.agentCache = new Map();\n        this.defaults = defaults || {};\n        this.interceptors = {\n            request: new interceptor_js_1.GaxiosInterceptorManager(),\n            response: new interceptor_js_1.GaxiosInterceptorManager()\n        };\n    }\n    /**\n     * A {@link fetch `fetch`} compliant API for {@link Gaxios}.\n     *\n     * @remarks\n     *\n     * This is useful as a drop-in replacement for `fetch` API usage.\n     *\n     * @example\n     *\n     * ```ts\n     * const gaxios = new Gaxios();\n     * const myFetch: typeof fetch = (...args) => gaxios.fetch(...args);\n     * await myFetch('https://example.com');\n     * ```\n     *\n     * @param args `fetch` API or `Gaxios#request` parameters\n     * @returns the {@link Response} with Gaxios-added properties\n     */ fetch(...args) {\n        // Up to 2 parameters in either overload\n        const input = args[0];\n        const init = args[1];\n        let url = undefined;\n        const headers = new Headers();\n        // prepare URL\n        if (typeof input === \"string\") {\n            url = new URL(input);\n        } else if (input instanceof URL) {\n            url = input;\n        } else if (input && input.url) {\n            url = new URL(input.url);\n        }\n        // prepare headers\n        if (input && typeof input === \"object\" && \"headers\" in input) {\n            _a.mergeHeaders(headers, input.headers);\n        }\n        if (init) {\n            _a.mergeHeaders(headers, new Headers(init.headers));\n        }\n        // prepare request\n        if (typeof input === \"object\" && !(input instanceof URL)) {\n            // input must have been a non-URL object\n            return this.request({\n                ...init,\n                ...input,\n                headers,\n                url\n            });\n        } else {\n            // input must have been a string or URL\n            return this.request({\n                ...init,\n                headers,\n                url\n            });\n        }\n    }\n    /**\n     * Perform an HTTP request with the given options.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */ async request(opts = {}) {\n        let prepared = await this.#prepareRequest(opts);\n        prepared = await this.#applyRequestInterceptors(prepared);\n        return this.#applyResponseInterceptors(this._request(prepared));\n    }\n    async _defaultAdapter(config) {\n        const fetchImpl = config.fetchImplementation || this.defaults.fetchImplementation || await _a.#getFetch();\n        // node-fetch v3 warns when `data` is present\n        // https://github.com/node-fetch/node-fetch/issues/1000\n        const preparedOpts = {\n            ...config\n        };\n        delete preparedOpts.data;\n        const res = await fetchImpl(config.url, preparedOpts);\n        const data = await this.getResponseData(config, res);\n        if (!Object.getOwnPropertyDescriptor(res, \"data\")?.configurable) {\n            // Work-around for `node-fetch` v3 as accessing `data` would otherwise throw\n            Object.defineProperties(res, {\n                data: {\n                    configurable: true,\n                    writable: true,\n                    enumerable: true,\n                    value: data\n                }\n            });\n        }\n        // Keep object as an instance of `Response`\n        return Object.assign(res, {\n            config,\n            data\n        });\n    }\n    /**\n     * Internal, retryable version of the `request` method.\n     * @param opts Set of HTTP options that will be used for this HTTP request.\n     */ async _request(opts) {\n        try {\n            let translatedResponse;\n            if (opts.adapter) {\n                translatedResponse = await opts.adapter(opts, this._defaultAdapter.bind(this));\n            } else {\n                translatedResponse = await this._defaultAdapter(opts);\n            }\n            if (!opts.validateStatus(translatedResponse.status)) {\n                if (opts.responseType === \"stream\") {\n                    const response = [];\n                    for await (const chunk of opts.data ?? []){\n                        response.push(chunk);\n                    }\n                    translatedResponse.data = response;\n                }\n                const errorInfo = common_js_1.GaxiosError.extractAPIErrorFromResponse(translatedResponse, `Request failed with status code ${translatedResponse.status}`);\n                throw new common_js_1.GaxiosError(errorInfo?.message, opts, translatedResponse, errorInfo);\n            }\n            return translatedResponse;\n        } catch (e) {\n            let err;\n            if (e instanceof common_js_1.GaxiosError) {\n                err = e;\n            } else if (e instanceof Error) {\n                err = new common_js_1.GaxiosError(e.message, opts, undefined, e);\n            } else {\n                err = new common_js_1.GaxiosError(\"Unexpected Gaxios Error\", opts, undefined, e);\n            }\n            const { shouldRetry, config } = await (0, retry_js_1.getRetryConfig)(err);\n            if (shouldRetry && config) {\n                err.config.retryConfig.currentRetryAttempt = config.retryConfig.currentRetryAttempt;\n                // The error's config could be redacted - therefore we only want to\n                // copy the retry state over to the existing config\n                opts.retryConfig = err.config?.retryConfig;\n                // re-prepare timeout for the next request\n                this.#appendTimeoutToSignal(opts);\n                return this._request(opts);\n            }\n            if (opts.errorRedactor) {\n                opts.errorRedactor(err);\n            }\n            throw err;\n        }\n    }\n    async getResponseData(opts, res) {\n        if (opts.maxContentLength && res.headers.has(\"content-length\") && opts.maxContentLength < Number.parseInt(res.headers?.get(\"content-length\") || \"\")) {\n            throw new common_js_1.GaxiosError(\"Response's `Content-Length` is over the limit.\", opts, Object.assign(res, {\n                config: opts\n            }));\n        }\n        switch(opts.responseType){\n            case \"stream\":\n                return res.body;\n            case \"json\":\n                return res.json();\n            case \"arraybuffer\":\n                return res.arrayBuffer();\n            case \"blob\":\n                return res.blob();\n            case \"text\":\n                return res.text();\n            default:\n                return this.getResponseDataFromContentType(res);\n        }\n    }\n    #urlMayUseProxy(url, noProxy = []) {\n        const candidate = new URL(url);\n        const noProxyList = [\n            ...noProxy\n        ];\n        const noProxyEnvList = (process.env.NO_PROXY ?? process.env.no_proxy)?.split(\",\") || [];\n        for (const rule of noProxyEnvList){\n            noProxyList.push(rule.trim());\n        }\n        for (const rule of noProxyList){\n            // Match regex\n            if (rule instanceof RegExp) {\n                if (rule.test(candidate.toString())) {\n                    return false;\n                }\n            } else if (rule instanceof URL) {\n                if (rule.origin === candidate.origin) {\n                    return false;\n                }\n            } else if (rule.startsWith(\"*.\") || rule.startsWith(\".\")) {\n                const cleanedRule = rule.replace(/^\\*\\./, \".\");\n                if (candidate.hostname.endsWith(cleanedRule)) {\n                    return false;\n                }\n            } else if (rule === candidate.origin || rule === candidate.hostname || rule === candidate.href) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /**\n     * Applies the request interceptors. The request interceptors are applied after the\n     * call to prepareRequest is completed.\n     *\n     * @param {GaxiosOptionsPrepared} options The current set of options.\n     *\n     * @returns {Promise<GaxiosOptionsPrepared>} Promise that resolves to the set of options or response after interceptors are applied.\n     */ async #applyRequestInterceptors(options) {\n        let promiseChain = Promise.resolve(options);\n        for (const interceptor of this.interceptors.request.values()){\n            if (interceptor) {\n                promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n            }\n        }\n        return promiseChain;\n    }\n    /**\n     * Applies the response interceptors. The response interceptors are applied after the\n     * call to request is made.\n     *\n     * @param {GaxiosOptionsPrepared} options The current set of options.\n     *\n     * @returns {Promise<GaxiosOptionsPrepared>} Promise that resolves to the set of options or response after interceptors are applied.\n     */ async #applyResponseInterceptors(response) {\n        let promiseChain = Promise.resolve(response);\n        for (const interceptor of this.interceptors.response.values()){\n            if (interceptor) {\n                promiseChain = promiseChain.then(interceptor.resolved, interceptor.rejected);\n            }\n        }\n        return promiseChain;\n    }\n    /**\n     * Validates the options, merges them with defaults, and prepare request.\n     *\n     * @param options The original options passed from the client.\n     * @returns Prepared options, ready to make a request\n     */ async #prepareRequest(options) {\n        // Prepare Headers - copy in order to not mutate the original objects\n        const preparedHeaders = new Headers(this.defaults.headers);\n        _a.mergeHeaders(preparedHeaders, options.headers);\n        // Merge options\n        const opts = (0, extend_1.default)(true, {}, this.defaults, options);\n        if (!opts.url) {\n            throw new Error(\"URL is required.\");\n        }\n        if (opts.baseURL) {\n            opts.url = new URL(opts.url, opts.baseURL);\n        }\n        // don't modify the properties of a default or provided URL\n        opts.url = new URL(opts.url);\n        if (opts.params) {\n            if (opts.paramsSerializer) {\n                let additionalQueryParams = opts.paramsSerializer(opts.params);\n                if (additionalQueryParams.startsWith(\"?\")) {\n                    additionalQueryParams = additionalQueryParams.slice(1);\n                }\n                const prefix = opts.url.toString().includes(\"?\") ? \"&\" : \"?\";\n                opts.url = opts.url + prefix + additionalQueryParams;\n            } else {\n                const url = opts.url instanceof URL ? opts.url : new URL(opts.url);\n                for (const [key, value] of new URLSearchParams(opts.params)){\n                    url.searchParams.append(key, value);\n                }\n                opts.url = url;\n            }\n        }\n        if (typeof options.maxContentLength === \"number\") {\n            opts.size = options.maxContentLength;\n        }\n        if (typeof options.maxRedirects === \"number\") {\n            opts.follow = options.maxRedirects;\n        }\n        const shouldDirectlyPassData = typeof opts.data === \"string\" || opts.data instanceof ArrayBuffer || opts.data instanceof Blob || // Node 18 does not have a global `File` object\n        globalThis.File && opts.data instanceof File || opts.data instanceof FormData || opts.data instanceof stream_1.Readable || opts.data instanceof ReadableStream || opts.data instanceof String || opts.data instanceof URLSearchParams || ArrayBuffer.isView(opts.data) || // `Buffer` (Node.js), `DataView`, `TypedArray`\n        /**\n             * @deprecated `node-fetch` or another third-party's request types\n             */ [\n            \"Blob\",\n            \"File\",\n            \"FormData\"\n        ].includes(opts.data?.constructor?.name || \"\");\n        if (opts.multipart?.length) {\n            const boundary = await randomUUID();\n            preparedHeaders.set(\"content-type\", `multipart/related; boundary=${boundary}`);\n            opts.body = stream_1.Readable.from(this.getMultipartRequest(opts.multipart, boundary));\n        } else if (shouldDirectlyPassData) {\n            opts.body = opts.data;\n        } else if (typeof opts.data === \"object\") {\n            if (preparedHeaders.get(\"Content-Type\") === \"application/x-www-form-urlencoded\") {\n                // If www-form-urlencoded content type has been set, but data is\n                // provided as an object, serialize the content\n                opts.body = opts.paramsSerializer ? opts.paramsSerializer(opts.data) : new URLSearchParams(opts.data);\n            } else {\n                if (!preparedHeaders.has(\"content-type\")) {\n                    preparedHeaders.set(\"content-type\", \"application/json\");\n                }\n                opts.body = JSON.stringify(opts.data);\n            }\n        } else if (opts.data) {\n            opts.body = opts.data;\n        }\n        opts.validateStatus = opts.validateStatus || this.validateStatus;\n        opts.responseType = opts.responseType || \"unknown\";\n        if (!preparedHeaders.has(\"accept\") && opts.responseType === \"json\") {\n            preparedHeaders.set(\"accept\", \"application/json\");\n        }\n        const proxy = opts.proxy || process?.env?.HTTPS_PROXY || process?.env?.https_proxy || process?.env?.HTTP_PROXY || process?.env?.http_proxy;\n        if (opts.agent) {\n        // don't do any of the following options - use the user-provided agent.\n        } else if (proxy && this.#urlMayUseProxy(opts.url, opts.noProxy)) {\n            const HttpsProxyAgent = await _a.#getProxyAgent();\n            if (this.agentCache.has(proxy)) {\n                opts.agent = this.agentCache.get(proxy);\n            } else {\n                opts.agent = new HttpsProxyAgent(proxy, {\n                    cert: opts.cert,\n                    key: opts.key\n                });\n                this.agentCache.set(proxy, opts.agent);\n            }\n        } else if (opts.cert && opts.key) {\n            // Configure client for mTLS\n            if (this.agentCache.has(opts.key)) {\n                opts.agent = this.agentCache.get(opts.key);\n            } else {\n                opts.agent = new https_1.Agent({\n                    cert: opts.cert,\n                    key: opts.key\n                });\n                this.agentCache.set(opts.key, opts.agent);\n            }\n        }\n        if (typeof opts.errorRedactor !== \"function\" && opts.errorRedactor !== false) {\n            opts.errorRedactor = common_js_1.defaultErrorRedactor;\n        }\n        if (opts.body && !(\"duplex\" in opts)) {\n            /**\n             * required for Node.js and the type isn't available today\n             * @link https://github.com/nodejs/node/issues/46221\n             * @link https://github.com/microsoft/TypeScript-DOM-lib-generator/issues/1483\n             */ opts.duplex = \"half\";\n        }\n        this.#appendTimeoutToSignal(opts);\n        return Object.assign(opts, {\n            headers: preparedHeaders,\n            url: opts.url instanceof URL ? opts.url : new URL(opts.url)\n        });\n    }\n    #appendTimeoutToSignal(opts) {\n        if (opts.timeout) {\n            const timeoutSignal = AbortSignal.timeout(opts.timeout);\n            if (opts.signal && !opts.signal.aborted) {\n                opts.signal = AbortSignal.any([\n                    opts.signal,\n                    timeoutSignal\n                ]);\n            } else {\n                opts.signal = timeoutSignal;\n            }\n        }\n    }\n    /**\n     * By default, throw for any non-2xx status code\n     * @param status status code from the HTTP response\n     */ validateStatus(status) {\n        return status >= 200 && status < 300;\n    }\n    /**\n     * Attempts to parse a response by looking at the Content-Type header.\n     * @param {Response} response the HTTP response.\n     * @returns a promise that resolves to the response data.\n     */ async getResponseDataFromContentType(response) {\n        let contentType = response.headers.get(\"Content-Type\");\n        if (contentType === null) {\n            // Maintain existing functionality by calling text()\n            return response.text();\n        }\n        contentType = contentType.toLowerCase();\n        if (contentType.includes(\"application/json\")) {\n            let data = await response.text();\n            try {\n                data = JSON.parse(data);\n            } catch  {\n            // continue\n            }\n            return data;\n        } else if (contentType.match(/^text\\//)) {\n            return response.text();\n        } else {\n            // If the content type is something not easily handled, just return the raw data (blob)\n            return response.blob();\n        }\n    }\n    /**\n     * Creates an async generator that yields the pieces of a multipart/related request body.\n     * This implementation follows the spec: https://www.ietf.org/rfc/rfc2387.txt. However, recursive\n     * multipart/related requests are not currently supported.\n     *\n     * @param {GaxioMultipartOptions[]} multipartOptions the pieces to turn into a multipart/related body.\n     * @param {string} boundary the boundary string to be placed between each part.\n     */ async *getMultipartRequest(multipartOptions, boundary) {\n        const finale = `--${boundary}--`;\n        for (const currentPart of multipartOptions){\n            const partContentType = currentPart.headers.get(\"Content-Type\") || \"application/octet-stream\";\n            const preamble = `--${boundary}\\r\\nContent-Type: ${partContentType}\\r\\n\\r\\n`;\n            yield preamble;\n            if (typeof currentPart.content === \"string\") {\n                yield currentPart.content;\n            } else {\n                yield* currentPart.content;\n            }\n            yield \"\\r\\n\";\n        }\n        yield finale;\n    }\n    /**\n     * A cache for the lazily-loaded proxy agent.\n     *\n     * Should use {@link Gaxios[#getProxyAgent]} to retrieve.\n     */ // using `import` to dynamically import the types here\n    static #proxyAgent;\n    /**\n     * A cache for the lazily-loaded fetch library.\n     *\n     * Should use {@link Gaxios[#getFetch]} to retrieve.\n     */ //\n    static #fetch;\n    /**\n     * Imports, caches, and returns a proxy agent - if not already imported\n     *\n     * @returns A proxy agent\n     */ static async #getProxyAgent() {\n        this.#proxyAgent ||= (await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/debug\"), __webpack_require__.e(\"vendor-chunks/https-proxy-agent\"), __webpack_require__.e(\"vendor-chunks/agent-base\"), __webpack_require__.e(\"vendor-chunks/supports-color\"), __webpack_require__.e(\"vendor-chunks/ms\"), __webpack_require__.e(\"vendor-chunks/has-flag\")]).then(__webpack_require__.bind(__webpack_require__, /*! https-proxy-agent */ \"(rsc)/./node_modules/https-proxy-agent/dist/index.js\"))).HttpsProxyAgent;\n        return this.#proxyAgent;\n    }\n    static async #getFetch() {\n        const hasWindow =  false && 0;\n        this.#fetch ||= hasWindow ? window.fetch : (await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/node-fetch\"), __webpack_require__.e(\"vendor-chunks/fetch-blob\"), __webpack_require__.e(\"vendor-chunks/formdata-polyfill\"), __webpack_require__.e(\"vendor-chunks/data-uri-to-buffer\"), __webpack_require__.e(\"vendor-chunks/web-streams-polyfill\"), __webpack_require__.e(\"vendor-chunks/node-domexception\")]).then(__webpack_require__.bind(__webpack_require__, /*! node-fetch */ \"(rsc)/./node_modules/node-fetch/src/index.js\"))).default;\n        return this.#fetch;\n    }\n    /**\n     * Merges headers.\n     * If the base headers do not exist a new `Headers` object will be returned.\n     *\n     * @remarks\n     *\n     * Using this utility can be helpful when the headers are not known to exist:\n     * - if they exist as `Headers`, that instance will be used\n     *   - it improves performance and allows users to use their existing references to their `Headers`\n     * - if they exist in another form (`HeadersInit`), they will be used to create a new `Headers` object\n     * - if the base headers do not exist a new `Headers` object will be created\n     *\n     * @param base headers to append/overwrite to\n     * @param append headers to append/overwrite with\n     * @returns the base headers instance with merged `Headers`\n     */ static mergeHeaders(base, ...append) {\n        base = base instanceof Headers ? base : new Headers(base);\n        for (const headers of append){\n            const add = headers instanceof Headers ? headers : new Headers(headers);\n            add.forEach((value, key)=>{\n                // set-cookie is the only header that would repeat.\n                // A bit of background: https://developer.mozilla.org/en-US/docs/Web/API/Headers/getSetCookie\n                key === \"set-cookie\" ? base.append(key, value) : base.set(key, value);\n            });\n        }\n        return base;\n    }\n}\nexports.Gaxios = Gaxios;\n_a = Gaxios; //# sourceMappingURL=gaxios.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/gaxios.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/index.js":
/*!****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.instance = exports.Gaxios = exports.GaxiosError = void 0;\nexports.request = request;\nconst gaxios_js_1 = __webpack_require__(/*! ./gaxios.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/gaxios.js\");\nObject.defineProperty(exports, \"Gaxios\", ({\n    enumerable: true,\n    get: function() {\n        return gaxios_js_1.Gaxios;\n    }\n}));\nvar common_js_1 = __webpack_require__(/*! ./common.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/common.js\");\nObject.defineProperty(exports, \"GaxiosError\", ({\n    enumerable: true,\n    get: function() {\n        return common_js_1.GaxiosError;\n    }\n}));\n__exportStar(__webpack_require__(/*! ./interceptor.js */ \"(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js\"), exports);\n/**\n * The default instance used when the `request` method is directly\n * invoked.\n */ exports.instance = new gaxios_js_1.Gaxios();\n/**\n * Make an HTTP request using the given options.\n * @param opts Options for the request\n */ async function request(opts) {\n    return exports.instance.request(opts);\n} //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js":
/*!**********************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/interceptor.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GaxiosInterceptorManager = void 0;\n/**\n * Class to manage collections of GaxiosInterceptors for both requests and responses.\n */ class GaxiosInterceptorManager extends Set {\n}\nexports.GaxiosInterceptorManager = GaxiosInterceptorManager; //# sourceMappingURL=interceptor.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/interceptor.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/retry.js":
/*!****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/retry.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2018 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getRetryConfig = getRetryConfig;\nasync function getRetryConfig(err) {\n    let config = getConfig(err);\n    if (!err || !err.config || !config && !err.config.retry) {\n        return {\n            shouldRetry: false\n        };\n    }\n    config = config || {};\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    config.retry = config.retry === undefined || config.retry === null ? 3 : config.retry;\n    config.httpMethodsToRetry = config.httpMethodsToRetry || [\n        \"GET\",\n        \"HEAD\",\n        \"PUT\",\n        \"OPTIONS\",\n        \"DELETE\"\n    ];\n    config.noResponseRetries = config.noResponseRetries === undefined || config.noResponseRetries === null ? 2 : config.noResponseRetries;\n    config.retryDelayMultiplier = config.retryDelayMultiplier ? config.retryDelayMultiplier : 2;\n    config.timeOfFirstRequest = config.timeOfFirstRequest ? config.timeOfFirstRequest : Date.now();\n    config.totalTimeout = config.totalTimeout ? config.totalTimeout : Number.MAX_SAFE_INTEGER;\n    config.maxRetryDelay = config.maxRetryDelay ? config.maxRetryDelay : Number.MAX_SAFE_INTEGER;\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    const retryRanges = [\n        // https://en.wikipedia.org/wiki/List_of_HTTP_status_codes\n        // 1xx - Retry (Informational, request still processing)\n        // 2xx - Do not retry (Success)\n        // 3xx - Do not retry (Redirect)\n        // 4xx - Do not retry (Client errors)\n        // 408 - Retry (\"Request Timeout\")\n        // 429 - Retry (\"Too Many Requests\")\n        // 5xx - Retry (Server errors)\n        [\n            100,\n            199\n        ],\n        [\n            408,\n            408\n        ],\n        [\n            429,\n            429\n        ],\n        [\n            500,\n            599\n        ]\n    ];\n    config.statusCodesToRetry = config.statusCodesToRetry || retryRanges;\n    // Put the config back into the err\n    err.config.retryConfig = config;\n    // Determine if we should retry the request\n    const shouldRetryFn = config.shouldRetry || shouldRetryRequest;\n    if (!await shouldRetryFn(err)) {\n        return {\n            shouldRetry: false,\n            config: err.config\n        };\n    }\n    const delay = getNextRetryDelay(config);\n    // We're going to retry!  Increment the counter.\n    err.config.retryConfig.currentRetryAttempt += 1;\n    // Create a promise that invokes the retry after the backOffDelay\n    const backoff = config.retryBackoff ? config.retryBackoff(err, delay) : new Promise((resolve)=>{\n        setTimeout(resolve, delay);\n    });\n    // Notify the user if they added an `onRetryAttempt` handler\n    if (config.onRetryAttempt) {\n        await config.onRetryAttempt(err);\n    }\n    // Return the promise in which recalls Gaxios to retry the request\n    await backoff;\n    return {\n        shouldRetry: true,\n        config: err.config\n    };\n}\n/**\n * Determine based on config if we should retry the request.\n * @param err The GaxiosError passed to the interceptor.\n */ function shouldRetryRequest(err) {\n    const config = getConfig(err);\n    if (err.config.signal?.aborted && err.code !== \"TimeoutError\" || err.code === \"AbortError\") {\n        return false;\n    }\n    // If there's no config, or retries are disabled, return.\n    if (!config || config.retry === 0) {\n        return false;\n    }\n    // Check if this error has no response (ETIMEDOUT, ENOTFOUND, etc)\n    if (!err.response && (config.currentRetryAttempt || 0) >= config.noResponseRetries) {\n        return false;\n    }\n    // Only retry with configured HttpMethods.\n    if (!config.httpMethodsToRetry || !config.httpMethodsToRetry.includes(err.config.method?.toUpperCase() || \"GET\")) {\n        return false;\n    }\n    // If this wasn't in the list of status codes where we want\n    // to automatically retry, return.\n    if (err.response && err.response.status) {\n        let isInRange = false;\n        for (const [min, max] of config.statusCodesToRetry){\n            const status = err.response.status;\n            if (status >= min && status <= max) {\n                isInRange = true;\n                break;\n            }\n        }\n        if (!isInRange) {\n            return false;\n        }\n    }\n    // If we are out of retry attempts, return\n    config.currentRetryAttempt = config.currentRetryAttempt || 0;\n    if (config.currentRetryAttempt >= config.retry) {\n        return false;\n    }\n    return true;\n}\n/**\n * Acquire the raxConfig object from an GaxiosError if available.\n * @param err The Gaxios error with a config object.\n */ function getConfig(err) {\n    if (err && err.config && err.config.retryConfig) {\n        return err.config.retryConfig;\n    }\n    return;\n}\n/**\n * Gets the delay to wait before the next retry.\n *\n * @param {RetryConfig} config The current set of retry options\n * @returns {number} the amount of ms to wait before the next retry attempt.\n */ function getNextRetryDelay(config) {\n    // Calculate time to wait with exponential backoff.\n    // If this is the first retry, look for a configured retryDelay.\n    const retryDelay = config.currentRetryAttempt ? 0 : config.retryDelay ?? 100;\n    // Formula: retryDelay + ((retryDelayMultiplier^currentRetryAttempt - 1 / 2) * 1000)\n    const calculatedDelay = retryDelay + (Math.pow(config.retryDelayMultiplier, config.currentRetryAttempt) - 1) / 2 * 1000;\n    const maxAllowableDelay = config.totalTimeout - (Date.now() - config.timeOfFirstRequest);\n    return Math.min(calculatedDelay, maxAllowableDelay, config.maxRetryDelay);\n} //# sourceMappingURL=retry.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL2Nqcy9zcmMvcmV0cnkuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw0QkFBNEI7QUFDNUIsa0VBQWtFO0FBQ2xFLG1FQUFtRTtBQUNuRSwwQ0FBMEM7QUFDMUMsRUFBRTtBQUNGLGdEQUFnRDtBQUNoRCxFQUFFO0FBQ0Ysc0VBQXNFO0FBQ3RFLG9FQUFvRTtBQUNwRSwyRUFBMkU7QUFDM0Usc0VBQXNFO0FBQ3RFLGlDQUFpQztBQUNqQ0EsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELHNCQUFzQixHQUFHRTtBQUN6QixlQUFlQSxlQUFlQyxHQUFHO0lBQzdCLElBQUlDLFNBQVNDLFVBQVVGO0lBQ3ZCLElBQUksQ0FBQ0EsT0FBTyxDQUFDQSxJQUFJQyxNQUFNLElBQUssQ0FBQ0EsVUFBVSxDQUFDRCxJQUFJQyxNQUFNLENBQUNFLEtBQUssRUFBRztRQUN2RCxPQUFPO1lBQUVDLGFBQWE7UUFBTTtJQUNoQztJQUNBSCxTQUFTQSxVQUFVLENBQUM7SUFDcEJBLE9BQU9JLG1CQUFtQixHQUFHSixPQUFPSSxtQkFBbUIsSUFBSTtJQUMzREosT0FBT0UsS0FBSyxHQUNSRixPQUFPRSxLQUFLLEtBQUtHLGFBQWFMLE9BQU9FLEtBQUssS0FBSyxPQUFPLElBQUlGLE9BQU9FLEtBQUs7SUFDMUVGLE9BQU9NLGtCQUFrQixHQUFHTixPQUFPTSxrQkFBa0IsSUFBSTtRQUNyRDtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0g7SUFDRE4sT0FBT08saUJBQWlCLEdBQ3BCUCxPQUFPTyxpQkFBaUIsS0FBS0YsYUFBYUwsT0FBT08saUJBQWlCLEtBQUssT0FDakUsSUFDQVAsT0FBT08saUJBQWlCO0lBQ2xDUCxPQUFPUSxvQkFBb0IsR0FBR1IsT0FBT1Esb0JBQW9CLEdBQ25EUixPQUFPUSxvQkFBb0IsR0FDM0I7SUFDTlIsT0FBT1Msa0JBQWtCLEdBQUdULE9BQU9TLGtCQUFrQixHQUMvQ1QsT0FBT1Msa0JBQWtCLEdBQ3pCQyxLQUFLQyxHQUFHO0lBQ2RYLE9BQU9ZLFlBQVksR0FBR1osT0FBT1ksWUFBWSxHQUNuQ1osT0FBT1ksWUFBWSxHQUNuQkMsT0FBT0MsZ0JBQWdCO0lBQzdCZCxPQUFPZSxhQUFhLEdBQUdmLE9BQU9lLGFBQWEsR0FDckNmLE9BQU9lLGFBQWEsR0FDcEJGLE9BQU9DLGdCQUFnQjtJQUM3QiwyREFBMkQ7SUFDM0Qsa0NBQWtDO0lBQ2xDLE1BQU1FLGNBQWM7UUFDaEIsMERBQTBEO1FBQzFELHdEQUF3RDtRQUN4RCwrQkFBK0I7UUFDL0IsZ0NBQWdDO1FBQ2hDLHFDQUFxQztRQUNyQyxrQ0FBa0M7UUFDbEMsb0NBQW9DO1FBQ3BDLDhCQUE4QjtRQUM5QjtZQUFDO1lBQUs7U0FBSTtRQUNWO1lBQUM7WUFBSztTQUFJO1FBQ1Y7WUFBQztZQUFLO1NBQUk7UUFDVjtZQUFDO1lBQUs7U0FBSTtLQUNiO0lBQ0RoQixPQUFPaUIsa0JBQWtCLEdBQUdqQixPQUFPaUIsa0JBQWtCLElBQUlEO0lBQ3pELG1DQUFtQztJQUNuQ2pCLElBQUlDLE1BQU0sQ0FBQ2tCLFdBQVcsR0FBR2xCO0lBQ3pCLDJDQUEyQztJQUMzQyxNQUFNbUIsZ0JBQWdCbkIsT0FBT0csV0FBVyxJQUFJaUI7SUFDNUMsSUFBSSxDQUFFLE1BQU1ELGNBQWNwQixNQUFPO1FBQzdCLE9BQU87WUFBRUksYUFBYTtZQUFPSCxRQUFRRCxJQUFJQyxNQUFNO1FBQUM7SUFDcEQ7SUFDQSxNQUFNcUIsUUFBUUMsa0JBQWtCdEI7SUFDaEMsZ0RBQWdEO0lBQ2hERCxJQUFJQyxNQUFNLENBQUNrQixXQUFXLENBQUNkLG1CQUFtQixJQUFJO0lBQzlDLGlFQUFpRTtJQUNqRSxNQUFNbUIsVUFBVXZCLE9BQU93QixZQUFZLEdBQzdCeEIsT0FBT3dCLFlBQVksQ0FBQ3pCLEtBQUtzQixTQUN6QixJQUFJSSxRQUFRQyxDQUFBQTtRQUNWQyxXQUFXRCxTQUFTTDtJQUN4QjtJQUNKLDREQUE0RDtJQUM1RCxJQUFJckIsT0FBTzRCLGNBQWMsRUFBRTtRQUN2QixNQUFNNUIsT0FBTzRCLGNBQWMsQ0FBQzdCO0lBQ2hDO0lBQ0Esa0VBQWtFO0lBQ2xFLE1BQU13QjtJQUNOLE9BQU87UUFBRXBCLGFBQWE7UUFBTUgsUUFBUUQsSUFBSUMsTUFBTTtJQUFDO0FBQ25EO0FBQ0E7OztDQUdDLEdBQ0QsU0FBU29CLG1CQUFtQnJCLEdBQUc7SUFDM0IsTUFBTUMsU0FBU0MsVUFBVUY7SUFDekIsSUFBSSxJQUFLQyxNQUFNLENBQUM2QixNQUFNLEVBQUVDLFdBQVcvQixJQUFJZ0MsSUFBSSxLQUFLLGtCQUM1Q2hDLElBQUlnQyxJQUFJLEtBQUssY0FBYztRQUMzQixPQUFPO0lBQ1g7SUFDQSx5REFBeUQ7SUFDekQsSUFBSSxDQUFDL0IsVUFBVUEsT0FBT0UsS0FBSyxLQUFLLEdBQUc7UUFDL0IsT0FBTztJQUNYO0lBQ0Esa0VBQWtFO0lBQ2xFLElBQUksQ0FBQ0gsSUFBSWlDLFFBQVEsSUFDYixDQUFDaEMsT0FBT0ksbUJBQW1CLElBQUksTUFBTUosT0FBT08saUJBQWlCLEVBQUU7UUFDL0QsT0FBTztJQUNYO0lBQ0EsMENBQTBDO0lBQzFDLElBQUksQ0FBQ1AsT0FBT00sa0JBQWtCLElBQzFCLENBQUNOLE9BQU9NLGtCQUFrQixDQUFDMkIsUUFBUSxDQUFDbEMsSUFBSUMsTUFBTSxDQUFDa0MsTUFBTSxFQUFFQyxpQkFBaUIsUUFBUTtRQUNoRixPQUFPO0lBQ1g7SUFDQSwyREFBMkQ7SUFDM0Qsa0NBQWtDO0lBQ2xDLElBQUlwQyxJQUFJaUMsUUFBUSxJQUFJakMsSUFBSWlDLFFBQVEsQ0FBQ0ksTUFBTSxFQUFFO1FBQ3JDLElBQUlDLFlBQVk7UUFDaEIsS0FBSyxNQUFNLENBQUNDLEtBQUtDLElBQUksSUFBSXZDLE9BQU9pQixrQkFBa0IsQ0FBRTtZQUNoRCxNQUFNbUIsU0FBU3JDLElBQUlpQyxRQUFRLENBQUNJLE1BQU07WUFDbEMsSUFBSUEsVUFBVUUsT0FBT0YsVUFBVUcsS0FBSztnQkFDaENGLFlBQVk7Z0JBQ1o7WUFDSjtRQUNKO1FBQ0EsSUFBSSxDQUFDQSxXQUFXO1lBQ1osT0FBTztRQUNYO0lBQ0o7SUFDQSwwQ0FBMEM7SUFDMUNyQyxPQUFPSSxtQkFBbUIsR0FBR0osT0FBT0ksbUJBQW1CLElBQUk7SUFDM0QsSUFBSUosT0FBT0ksbUJBQW1CLElBQUlKLE9BQU9FLEtBQUssRUFBRTtRQUM1QyxPQUFPO0lBQ1g7SUFDQSxPQUFPO0FBQ1g7QUFDQTs7O0NBR0MsR0FDRCxTQUFTRCxVQUFVRixHQUFHO0lBQ2xCLElBQUlBLE9BQU9BLElBQUlDLE1BQU0sSUFBSUQsSUFBSUMsTUFBTSxDQUFDa0IsV0FBVyxFQUFFO1FBQzdDLE9BQU9uQixJQUFJQyxNQUFNLENBQUNrQixXQUFXO0lBQ2pDO0lBQ0E7QUFDSjtBQUNBOzs7OztDQUtDLEdBQ0QsU0FBU0ksa0JBQWtCdEIsTUFBTTtJQUM3QixtREFBbUQ7SUFDbkQsZ0VBQWdFO0lBQ2hFLE1BQU13QyxhQUFheEMsT0FBT0ksbUJBQW1CLEdBQ3ZDLElBQ0NKLE9BQU93QyxVQUFVLElBQUk7SUFDNUIsb0ZBQW9GO0lBQ3BGLE1BQU1DLGtCQUFrQkQsYUFDcEIsQ0FBRUUsS0FBS0MsR0FBRyxDQUFDM0MsT0FBT1Esb0JBQW9CLEVBQUVSLE9BQU9JLG1CQUFtQixJQUFJLEtBQ2xFLElBQ0E7SUFDUixNQUFNd0Msb0JBQW9CNUMsT0FBT1ksWUFBWSxHQUFJRixDQUFBQSxLQUFLQyxHQUFHLEtBQUtYLE9BQU9TLGtCQUFrQjtJQUN2RixPQUFPaUMsS0FBS0osR0FBRyxDQUFDRyxpQkFBaUJHLG1CQUFtQjVDLE9BQU9lLGFBQWE7QUFDNUUsRUFDQSxpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbWFpbC1jbGllbnQvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL2Nqcy9zcmMvcmV0cnkuanM/MjkwNCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8vIENvcHlyaWdodCAyMDE4IEdvb2dsZSBMTENcbi8vIExpY2Vuc2VkIHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4vLyB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuXG4vLyBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbi8vXG4vLyAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbi8vXG4vLyBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4vLyBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4vLyBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbi8vIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbi8vIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXRSZXRyeUNvbmZpZyA9IGdldFJldHJ5Q29uZmlnO1xuYXN5bmMgZnVuY3Rpb24gZ2V0UmV0cnlDb25maWcoZXJyKSB7XG4gICAgbGV0IGNvbmZpZyA9IGdldENvbmZpZyhlcnIpO1xuICAgIGlmICghZXJyIHx8ICFlcnIuY29uZmlnIHx8ICghY29uZmlnICYmICFlcnIuY29uZmlnLnJldHJ5KSkge1xuICAgICAgICByZXR1cm4geyBzaG91bGRSZXRyeTogZmFsc2UgfTtcbiAgICB9XG4gICAgY29uZmlnID0gY29uZmlnIHx8IHt9O1xuICAgIGNvbmZpZy5jdXJyZW50UmV0cnlBdHRlbXB0ID0gY29uZmlnLmN1cnJlbnRSZXRyeUF0dGVtcHQgfHwgMDtcbiAgICBjb25maWcucmV0cnkgPVxuICAgICAgICBjb25maWcucmV0cnkgPT09IHVuZGVmaW5lZCB8fCBjb25maWcucmV0cnkgPT09IG51bGwgPyAzIDogY29uZmlnLnJldHJ5O1xuICAgIGNvbmZpZy5odHRwTWV0aG9kc1RvUmV0cnkgPSBjb25maWcuaHR0cE1ldGhvZHNUb1JldHJ5IHx8IFtcbiAgICAgICAgJ0dFVCcsXG4gICAgICAgICdIRUFEJyxcbiAgICAgICAgJ1BVVCcsXG4gICAgICAgICdPUFRJT05TJyxcbiAgICAgICAgJ0RFTEVURScsXG4gICAgXTtcbiAgICBjb25maWcubm9SZXNwb25zZVJldHJpZXMgPVxuICAgICAgICBjb25maWcubm9SZXNwb25zZVJldHJpZXMgPT09IHVuZGVmaW5lZCB8fCBjb25maWcubm9SZXNwb25zZVJldHJpZXMgPT09IG51bGxcbiAgICAgICAgICAgID8gMlxuICAgICAgICAgICAgOiBjb25maWcubm9SZXNwb25zZVJldHJpZXM7XG4gICAgY29uZmlnLnJldHJ5RGVsYXlNdWx0aXBsaWVyID0gY29uZmlnLnJldHJ5RGVsYXlNdWx0aXBsaWVyXG4gICAgICAgID8gY29uZmlnLnJldHJ5RGVsYXlNdWx0aXBsaWVyXG4gICAgICAgIDogMjtcbiAgICBjb25maWcudGltZU9mRmlyc3RSZXF1ZXN0ID0gY29uZmlnLnRpbWVPZkZpcnN0UmVxdWVzdFxuICAgICAgICA/IGNvbmZpZy50aW1lT2ZGaXJzdFJlcXVlc3RcbiAgICAgICAgOiBEYXRlLm5vdygpO1xuICAgIGNvbmZpZy50b3RhbFRpbWVvdXQgPSBjb25maWcudG90YWxUaW1lb3V0XG4gICAgICAgID8gY29uZmlnLnRvdGFsVGltZW91dFxuICAgICAgICA6IE51bWJlci5NQVhfU0FGRV9JTlRFR0VSO1xuICAgIGNvbmZpZy5tYXhSZXRyeURlbGF5ID0gY29uZmlnLm1heFJldHJ5RGVsYXlcbiAgICAgICAgPyBjb25maWcubWF4UmV0cnlEZWxheVxuICAgICAgICA6IE51bWJlci5NQVhfU0FGRV9JTlRFR0VSO1xuICAgIC8vIElmIHRoaXMgd2Fzbid0IGluIHRoZSBsaXN0IG9mIHN0YXR1cyBjb2RlcyB3aGVyZSB3ZSB3YW50XG4gICAgLy8gdG8gYXV0b21hdGljYWxseSByZXRyeSwgcmV0dXJuLlxuICAgIGNvbnN0IHJldHJ5UmFuZ2VzID0gW1xuICAgICAgICAvLyBodHRwczovL2VuLndpa2lwZWRpYS5vcmcvd2lraS9MaXN0X29mX0hUVFBfc3RhdHVzX2NvZGVzXG4gICAgICAgIC8vIDF4eCAtIFJldHJ5IChJbmZvcm1hdGlvbmFsLCByZXF1ZXN0IHN0aWxsIHByb2Nlc3NpbmcpXG4gICAgICAgIC8vIDJ4eCAtIERvIG5vdCByZXRyeSAoU3VjY2VzcylcbiAgICAgICAgLy8gM3h4IC0gRG8gbm90IHJldHJ5IChSZWRpcmVjdClcbiAgICAgICAgLy8gNHh4IC0gRG8gbm90IHJldHJ5IChDbGllbnQgZXJyb3JzKVxuICAgICAgICAvLyA0MDggLSBSZXRyeSAoXCJSZXF1ZXN0IFRpbWVvdXRcIilcbiAgICAgICAgLy8gNDI5IC0gUmV0cnkgKFwiVG9vIE1hbnkgUmVxdWVzdHNcIilcbiAgICAgICAgLy8gNXh4IC0gUmV0cnkgKFNlcnZlciBlcnJvcnMpXG4gICAgICAgIFsxMDAsIDE5OV0sXG4gICAgICAgIFs0MDgsIDQwOF0sXG4gICAgICAgIFs0MjksIDQyOV0sXG4gICAgICAgIFs1MDAsIDU5OV0sXG4gICAgXTtcbiAgICBjb25maWcuc3RhdHVzQ29kZXNUb1JldHJ5ID0gY29uZmlnLnN0YXR1c0NvZGVzVG9SZXRyeSB8fCByZXRyeVJhbmdlcztcbiAgICAvLyBQdXQgdGhlIGNvbmZpZyBiYWNrIGludG8gdGhlIGVyclxuICAgIGVyci5jb25maWcucmV0cnlDb25maWcgPSBjb25maWc7XG4gICAgLy8gRGV0ZXJtaW5lIGlmIHdlIHNob3VsZCByZXRyeSB0aGUgcmVxdWVzdFxuICAgIGNvbnN0IHNob3VsZFJldHJ5Rm4gPSBjb25maWcuc2hvdWxkUmV0cnkgfHwgc2hvdWxkUmV0cnlSZXF1ZXN0O1xuICAgIGlmICghKGF3YWl0IHNob3VsZFJldHJ5Rm4oZXJyKSkpIHtcbiAgICAgICAgcmV0dXJuIHsgc2hvdWxkUmV0cnk6IGZhbHNlLCBjb25maWc6IGVyci5jb25maWcgfTtcbiAgICB9XG4gICAgY29uc3QgZGVsYXkgPSBnZXROZXh0UmV0cnlEZWxheShjb25maWcpO1xuICAgIC8vIFdlJ3JlIGdvaW5nIHRvIHJldHJ5ISAgSW5jcmVtZW50IHRoZSBjb3VudGVyLlxuICAgIGVyci5jb25maWcucmV0cnlDb25maWcuY3VycmVudFJldHJ5QXR0ZW1wdCArPSAxO1xuICAgIC8vIENyZWF0ZSBhIHByb21pc2UgdGhhdCBpbnZva2VzIHRoZSByZXRyeSBhZnRlciB0aGUgYmFja09mZkRlbGF5XG4gICAgY29uc3QgYmFja29mZiA9IGNvbmZpZy5yZXRyeUJhY2tvZmZcbiAgICAgICAgPyBjb25maWcucmV0cnlCYWNrb2ZmKGVyciwgZGVsYXkpXG4gICAgICAgIDogbmV3IFByb21pc2UocmVzb2x2ZSA9PiB7XG4gICAgICAgICAgICBzZXRUaW1lb3V0KHJlc29sdmUsIGRlbGF5KTtcbiAgICAgICAgfSk7XG4gICAgLy8gTm90aWZ5IHRoZSB1c2VyIGlmIHRoZXkgYWRkZWQgYW4gYG9uUmV0cnlBdHRlbXB0YCBoYW5kbGVyXG4gICAgaWYgKGNvbmZpZy5vblJldHJ5QXR0ZW1wdCkge1xuICAgICAgICBhd2FpdCBjb25maWcub25SZXRyeUF0dGVtcHQoZXJyKTtcbiAgICB9XG4gICAgLy8gUmV0dXJuIHRoZSBwcm9taXNlIGluIHdoaWNoIHJlY2FsbHMgR2F4aW9zIHRvIHJldHJ5IHRoZSByZXF1ZXN0XG4gICAgYXdhaXQgYmFja29mZjtcbiAgICByZXR1cm4geyBzaG91bGRSZXRyeTogdHJ1ZSwgY29uZmlnOiBlcnIuY29uZmlnIH07XG59XG4vKipcbiAqIERldGVybWluZSBiYXNlZCBvbiBjb25maWcgaWYgd2Ugc2hvdWxkIHJldHJ5IHRoZSByZXF1ZXN0LlxuICogQHBhcmFtIGVyciBUaGUgR2F4aW9zRXJyb3IgcGFzc2VkIHRvIHRoZSBpbnRlcmNlcHRvci5cbiAqL1xuZnVuY3Rpb24gc2hvdWxkUmV0cnlSZXF1ZXN0KGVycikge1xuICAgIGNvbnN0IGNvbmZpZyA9IGdldENvbmZpZyhlcnIpO1xuICAgIGlmICgoZXJyLmNvbmZpZy5zaWduYWw/LmFib3J0ZWQgJiYgZXJyLmNvZGUgIT09ICdUaW1lb3V0RXJyb3InKSB8fFxuICAgICAgICBlcnIuY29kZSA9PT0gJ0Fib3J0RXJyb3InKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgLy8gSWYgdGhlcmUncyBubyBjb25maWcsIG9yIHJldHJpZXMgYXJlIGRpc2FibGVkLCByZXR1cm4uXG4gICAgaWYgKCFjb25maWcgfHwgY29uZmlnLnJldHJ5ID09PSAwKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgLy8gQ2hlY2sgaWYgdGhpcyBlcnJvciBoYXMgbm8gcmVzcG9uc2UgKEVUSU1FRE9VVCwgRU5PVEZPVU5ELCBldGMpXG4gICAgaWYgKCFlcnIucmVzcG9uc2UgJiZcbiAgICAgICAgKGNvbmZpZy5jdXJyZW50UmV0cnlBdHRlbXB0IHx8IDApID49IGNvbmZpZy5ub1Jlc3BvbnNlUmV0cmllcykge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIC8vIE9ubHkgcmV0cnkgd2l0aCBjb25maWd1cmVkIEh0dHBNZXRob2RzLlxuICAgIGlmICghY29uZmlnLmh0dHBNZXRob2RzVG9SZXRyeSB8fFxuICAgICAgICAhY29uZmlnLmh0dHBNZXRob2RzVG9SZXRyeS5pbmNsdWRlcyhlcnIuY29uZmlnLm1ldGhvZD8udG9VcHBlckNhc2UoKSB8fCAnR0VUJykpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICAvLyBJZiB0aGlzIHdhc24ndCBpbiB0aGUgbGlzdCBvZiBzdGF0dXMgY29kZXMgd2hlcmUgd2Ugd2FudFxuICAgIC8vIHRvIGF1dG9tYXRpY2FsbHkgcmV0cnksIHJldHVybi5cbiAgICBpZiAoZXJyLnJlc3BvbnNlICYmIGVyci5yZXNwb25zZS5zdGF0dXMpIHtcbiAgICAgICAgbGV0IGlzSW5SYW5nZSA9IGZhbHNlO1xuICAgICAgICBmb3IgKGNvbnN0IFttaW4sIG1heF0gb2YgY29uZmlnLnN0YXR1c0NvZGVzVG9SZXRyeSkge1xuICAgICAgICAgICAgY29uc3Qgc3RhdHVzID0gZXJyLnJlc3BvbnNlLnN0YXR1cztcbiAgICAgICAgICAgIGlmIChzdGF0dXMgPj0gbWluICYmIHN0YXR1cyA8PSBtYXgpIHtcbiAgICAgICAgICAgICAgICBpc0luUmFuZ2UgPSB0cnVlO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICghaXNJblJhbmdlKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gSWYgd2UgYXJlIG91dCBvZiByZXRyeSBhdHRlbXB0cywgcmV0dXJuXG4gICAgY29uZmlnLmN1cnJlbnRSZXRyeUF0dGVtcHQgPSBjb25maWcuY3VycmVudFJldHJ5QXR0ZW1wdCB8fCAwO1xuICAgIGlmIChjb25maWcuY3VycmVudFJldHJ5QXR0ZW1wdCA+PSBjb25maWcucmV0cnkpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cbi8qKlxuICogQWNxdWlyZSB0aGUgcmF4Q29uZmlnIG9iamVjdCBmcm9tIGFuIEdheGlvc0Vycm9yIGlmIGF2YWlsYWJsZS5cbiAqIEBwYXJhbSBlcnIgVGhlIEdheGlvcyBlcnJvciB3aXRoIGEgY29uZmlnIG9iamVjdC5cbiAqL1xuZnVuY3Rpb24gZ2V0Q29uZmlnKGVycikge1xuICAgIGlmIChlcnIgJiYgZXJyLmNvbmZpZyAmJiBlcnIuY29uZmlnLnJldHJ5Q29uZmlnKSB7XG4gICAgICAgIHJldHVybiBlcnIuY29uZmlnLnJldHJ5Q29uZmlnO1xuICAgIH1cbiAgICByZXR1cm47XG59XG4vKipcbiAqIEdldHMgdGhlIGRlbGF5IHRvIHdhaXQgYmVmb3JlIHRoZSBuZXh0IHJldHJ5LlxuICpcbiAqIEBwYXJhbSB7UmV0cnlDb25maWd9IGNvbmZpZyBUaGUgY3VycmVudCBzZXQgb2YgcmV0cnkgb3B0aW9uc1xuICogQHJldHVybnMge251bWJlcn0gdGhlIGFtb3VudCBvZiBtcyB0byB3YWl0IGJlZm9yZSB0aGUgbmV4dCByZXRyeSBhdHRlbXB0LlxuICovXG5mdW5jdGlvbiBnZXROZXh0UmV0cnlEZWxheShjb25maWcpIHtcbiAgICAvLyBDYWxjdWxhdGUgdGltZSB0byB3YWl0IHdpdGggZXhwb25lbnRpYWwgYmFja29mZi5cbiAgICAvLyBJZiB0aGlzIGlzIHRoZSBmaXJzdCByZXRyeSwgbG9vayBmb3IgYSBjb25maWd1cmVkIHJldHJ5RGVsYXkuXG4gICAgY29uc3QgcmV0cnlEZWxheSA9IGNvbmZpZy5jdXJyZW50UmV0cnlBdHRlbXB0XG4gICAgICAgID8gMFxuICAgICAgICA6IChjb25maWcucmV0cnlEZWxheSA/PyAxMDApO1xuICAgIC8vIEZvcm11bGE6IHJldHJ5RGVsYXkgKyAoKHJldHJ5RGVsYXlNdWx0aXBsaWVyXmN1cnJlbnRSZXRyeUF0dGVtcHQgLSAxIC8gMikgKiAxMDAwKVxuICAgIGNvbnN0IGNhbGN1bGF0ZWREZWxheSA9IHJldHJ5RGVsYXkgK1xuICAgICAgICAoKE1hdGgucG93KGNvbmZpZy5yZXRyeURlbGF5TXVsdGlwbGllciwgY29uZmlnLmN1cnJlbnRSZXRyeUF0dGVtcHQpIC0gMSkgL1xuICAgICAgICAgICAgMikgKlxuICAgICAgICAgICAgMTAwMDtcbiAgICBjb25zdCBtYXhBbGxvd2FibGVEZWxheSA9IGNvbmZpZy50b3RhbFRpbWVvdXQgLSAoRGF0ZS5ub3coKSAtIGNvbmZpZy50aW1lT2ZGaXJzdFJlcXVlc3QpO1xuICAgIHJldHVybiBNYXRoLm1pbihjYWxjdWxhdGVkRGVsYXksIG1heEFsbG93YWJsZURlbGF5LCBjb25maWcubWF4UmV0cnlEZWxheSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXRyeS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJnZXRSZXRyeUNvbmZpZyIsImVyciIsImNvbmZpZyIsImdldENvbmZpZyIsInJldHJ5Iiwic2hvdWxkUmV0cnkiLCJjdXJyZW50UmV0cnlBdHRlbXB0IiwidW5kZWZpbmVkIiwiaHR0cE1ldGhvZHNUb1JldHJ5Iiwibm9SZXNwb25zZVJldHJpZXMiLCJyZXRyeURlbGF5TXVsdGlwbGllciIsInRpbWVPZkZpcnN0UmVxdWVzdCIsIkRhdGUiLCJub3ciLCJ0b3RhbFRpbWVvdXQiLCJOdW1iZXIiLCJNQVhfU0FGRV9JTlRFR0VSIiwibWF4UmV0cnlEZWxheSIsInJldHJ5UmFuZ2VzIiwic3RhdHVzQ29kZXNUb1JldHJ5IiwicmV0cnlDb25maWciLCJzaG91bGRSZXRyeUZuIiwic2hvdWxkUmV0cnlSZXF1ZXN0IiwiZGVsYXkiLCJnZXROZXh0UmV0cnlEZWxheSIsImJhY2tvZmYiLCJyZXRyeUJhY2tvZmYiLCJQcm9taXNlIiwicmVzb2x2ZSIsInNldFRpbWVvdXQiLCJvblJldHJ5QXR0ZW1wdCIsInNpZ25hbCIsImFib3J0ZWQiLCJjb2RlIiwicmVzcG9uc2UiLCJpbmNsdWRlcyIsIm1ldGhvZCIsInRvVXBwZXJDYXNlIiwic3RhdHVzIiwiaXNJblJhbmdlIiwibWluIiwibWF4IiwicmV0cnlEZWxheSIsImNhbGN1bGF0ZWREZWxheSIsIk1hdGgiLCJwb3ciLCJtYXhBbGxvd2FibGVEZWxheSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/retry.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/build/cjs/src/util.cjs":
/*!****************************************************!*\
  !*** ./node_modules/gaxios/build/cjs/src/util.cjs ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n// Copyright 2023 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nconst pkg = __webpack_require__(/*! ../../../package.json */ \"(rsc)/./node_modules/gaxios/package.json\");\nmodule.exports = {\n    pkg\n}; //# sourceMappingURL=util.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ2F4aW9zL2J1aWxkL2Nqcy9zcmMvdXRpbC5janMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw0QkFBNEI7QUFDNUIsa0VBQWtFO0FBQ2xFLG1FQUFtRTtBQUNuRSwwQ0FBMEM7QUFDMUMsRUFBRTtBQUNGLGdEQUFnRDtBQUNoRCxFQUFFO0FBQ0Ysc0VBQXNFO0FBQ3RFLG9FQUFvRTtBQUNwRSwyRUFBMkU7QUFDM0Usc0VBQXNFO0FBQ3RFLGlDQUFpQztBQUNqQyxNQUFNQSxNQUFNQyxtQkFBT0EsQ0FBQztBQUNwQkMsT0FBT0MsT0FBTyxHQUFHO0lBQUVIO0FBQUksR0FDdkIsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZW1haWwtY2xpZW50Ly4vbm9kZV9tb2R1bGVzL2dheGlvcy9idWlsZC9janMvc3JjL3V0aWwuY2pzP2M0OTQiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBDb3B5cmlnaHQgMjAyMyBHb29nbGUgTExDXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuLy8geW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuLy8gWW91IG1heSBvYnRhaW4gYSBjb3B5IG9mIHRoZSBMaWNlbnNlIGF0XG4vL1xuLy8gICAgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4vL1xuLy8gVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZVxuLy8gZGlzdHJpYnV0ZWQgdW5kZXIgdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLFxuLy8gV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4vLyBTZWUgdGhlIExpY2Vuc2UgZm9yIHRoZSBzcGVjaWZpYyBsYW5ndWFnZSBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kXG4vLyBsaW1pdGF0aW9ucyB1bmRlciB0aGUgTGljZW5zZS5cbmNvbnN0IHBrZyA9IHJlcXVpcmUoJy4uLy4uLy4uL3BhY2thZ2UuanNvbicpO1xubW9kdWxlLmV4cG9ydHMgPSB7IHBrZyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbC5janMubWFwIl0sIm5hbWVzIjpbInBrZyIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/gaxios/build/cjs/src/util.cjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/gaxios/package.json":
/*!******************************************!*\
  !*** ./node_modules/gaxios/package.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = JSON.parse('{"name":"gaxios","version":"7.1.1","description":"A simple common HTTP client specifically for Google APIs and services.","main":"build/cjs/src/index.js","types":"build/cjs/src/index.d.ts","files":["build/"],"exports":{".":{"import":{"types":"./build/esm/src/index.d.ts","default":"./build/esm/src/index.js"},"require":{"types":"./build/cjs/src/index.d.ts","default":"./build/cjs/src/index.js"}}},"scripts":{"lint":"gts check --no-inline-config","test":"c8 mocha build/esm/test","presystem-test":"npm run compile","system-test":"mocha build/esm/system-test --timeout 80000","compile":"tsc -b ./tsconfig.json ./tsconfig.cjs.json && node utils/enable-esm.mjs","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","webpack":"webpack","prebrowser-test":"npm run compile","browser-test":"node build/browser-test/browser-test-runner.js","docs":"jsdoc -c .jsdoc.js","docs-test":"linkinator docs","predocs-test":"npm run docs","samples-test":"cd samples/ && npm link ../ && npm test && cd ../","prelint":"cd samples; npm link ../; npm install","clean":"gts clean"},"repository":"googleapis/gaxios","keywords":["google"],"engines":{"node":">=18"},"author":"Google, LLC","license":"Apache-2.0","devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@types/cors":"^2.8.6","@types/express":"^5.0.0","@types/extend":"^3.0.1","@types/mocha":"^10.0.10","@types/multiparty":"4.2.1","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/node":"^22.0.0","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","assert":"^2.0.0","browserify":"^17.0.0","c8":"^10.0.0","cors":"^2.8.5","express":"^5.0.0","gts":"^6.0.0","is-docker":"^3.0.0","jsdoc":"^4.0.0","jsdoc-fresh":"^4.0.0","jsdoc-region-tag":"^3.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"^5.0.1","linkinator":"^6.1.2","mocha":"^11.1.0","multiparty":"^4.2.1","mv":"^2.1.1","ncp":"^2.0.0","nock":"^14.0.0-beta.13","null-loader":"^4.0.0","pack-n-play":"^3.0.0","puppeteer":"^24.0.0","sinon":"^20.0.0","stream-browserify":"^3.0.0","tmp":"0.2.3","ts-loader":"^9.5.2","typescript":"^5.8.3","webpack":"^5.35.0","webpack-cli":"^6.0.1"},"dependencies":{"extend":"^3.0.2","https-proxy-agent":"^7.0.1","node-fetch":"^3.3.2"}}');

/***/ })

};
;