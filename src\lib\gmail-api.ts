import { google } from 'googleapis'
import { OAuth2Client } from 'google-auth-library'

// Gmail API 配置
const GMAIL_SCOPES = [
  'https://www.googleapis.com/auth/gmail.readonly',
  'https://www.googleapis.com/auth/gmail.send',
  'https://www.googleapis.com/auth/gmail.modify'
]

// OAuth2 客户端配置
const oauth2Client = new OAuth2Client(
  process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI
)

// Gmail API 客户端
const gmail = google.gmail({ version: 'v1', auth: oauth2Client })

export interface GmailMessage {
  id: string
  threadId: string
  snippet: string
  payload: {
    headers: Array<{ name: string; value: string }>
    body?: { data?: string }
    parts?: Array<{
      mimeType: string
      body: { data?: string }
      headers: Array<{ name: string; value: string }>
    }>
  }
  internalDate: string
  labelIds: string[]
}

export interface ParsedEmail {
  id: string
  from: string
  to: string
  subject: string
  date: string
  body: string
  snippet: string
  isUnread: boolean
  isStarred: boolean
  labels: string[]
}

export class GmailService {
  private accessToken: string | null = null

  constructor(accessToken?: string) {
    if (accessToken) {
      this.setAccessToken(accessToken)
    }
  }

  // 设置访问令牌
  setAccessToken(token: string) {
    this.accessToken = token
    oauth2Client.setCredentials({ access_token: token })
  }

  // 获取授权URL
  getAuthUrl(): string {
    return oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: GMAIL_SCOPES,
      prompt: 'consent'
    })
  }

  // 通过授权码获取访问令牌
  async getAccessToken(code: string) {
    try {
      const { tokens } = await oauth2Client.getAccessToken(code)
      if (tokens.access_token) {
        this.setAccessToken(tokens.access_token)
        return tokens
      }
      throw new Error('No access token received')
    } catch (error) {
      console.error('Error getting access token:', error)
      throw error
    }
  }

  // 获取用户信息
  async getUserProfile() {
    try {
      const response = await gmail.users.getProfile({ userId: 'me' })
      return response.data
    } catch (error) {
      console.error('Error getting user profile:', error)
      throw error
    }
  }

  // 获取邮件列表
  async getMessages(maxResults: number = 20, query?: string): Promise<GmailMessage[]> {
    try {
      const listResponse = await gmail.users.messages.list({
        userId: 'me',
        maxResults,
        q: query
      })

      if (!listResponse.data.messages) {
        return []
      }

      // 获取每封邮件的详细信息
      const messages = await Promise.all(
        listResponse.data.messages.map(async (message) => {
          const messageResponse = await gmail.users.messages.get({
            userId: 'me',
            id: message.id!,
            format: 'full'
          })
          return messageResponse.data as GmailMessage
        })
      )

      return messages
    } catch (error) {
      console.error('Error getting messages:', error)
      throw error
    }
  }

  // 解析邮件内容
  parseEmail(message: GmailMessage): ParsedEmail {
    const headers = message.payload.headers
    const getHeader = (name: string) => 
      headers.find(h => h.name.toLowerCase() === name.toLowerCase())?.value || ''

    // 提取邮件正文
    let body = ''
    if (message.payload.body?.data) {
      body = this.decodeBase64(message.payload.body.data)
    } else if (message.payload.parts) {
      // 查找文本部分
      const textPart = message.payload.parts.find(part => 
        part.mimeType === 'text/plain' || part.mimeType === 'text/html'
      )
      if (textPart?.body?.data) {
        body = this.decodeBase64(textPart.body.data)
      }
    }

    return {
      id: message.id,
      from: getHeader('From'),
      to: getHeader('To'),
      subject: getHeader('Subject'),
      date: new Date(parseInt(message.internalDate)).toISOString(),
      body,
      snippet: message.snippet,
      isUnread: message.labelIds.includes('UNREAD'),
      isStarred: message.labelIds.includes('STARRED'),
      labels: message.labelIds
    }
  }

  // Base64 解码
  private decodeBase64(data: string): string {
    try {
      // Gmail API 使用 URL-safe base64
      const base64 = data.replace(/-/g, '+').replace(/_/g, '/')
      return Buffer.from(base64, 'base64').toString('utf-8')
    } catch (error) {
      console.error('Error decoding base64:', error)
      return ''
    }
  }

  // 发送邮件
  async sendEmail(to: string, subject: string, body: string, from?: string) {
    try {
      const email = [
        `To: ${to}`,
        `Subject: ${subject}`,
        from ? `From: ${from}` : '',
        '',
        body
      ].join('\n')

      const encodedEmail = Buffer.from(email).toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=+$/, '')

      const response = await gmail.users.messages.send({
        userId: 'me',
        requestBody: {
          raw: encodedEmail
        }
      })

      return response.data
    } catch (error) {
      console.error('Error sending email:', error)
      throw error
    }
  }

  // 标记邮件为已读
  async markAsRead(messageId: string) {
    try {
      await gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          removeLabelIds: ['UNREAD']
        }
      })
    } catch (error) {
      console.error('Error marking as read:', error)
      throw error
    }
  }

  // 添加/移除星标
  async toggleStar(messageId: string, isStarred: boolean) {
    try {
      await gmail.users.messages.modify({
        userId: 'me',
        id: messageId,
        requestBody: {
          addLabelIds: isStarred ? [] : ['STARRED'],
          removeLabelIds: isStarred ? ['STARRED'] : []
        }
      })
    } catch (error) {
      console.error('Error toggling star:', error)
      throw error
    }
  }
}

export default GmailService
