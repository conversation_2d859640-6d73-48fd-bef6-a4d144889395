"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/google-logging-utils";
exports.ids = ["vendor-chunks/google-logging-utils"];
exports.modules = {

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/colours.js":
/*!****************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/colours.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Colours = void 0;\n/**\n * Handles figuring out if we can use ANSI colours and handing out the escape codes.\n *\n * This is for package-internal use only, and may change at any time.\n *\n * @private\n * @internal\n */ class Colours {\n    /**\n     * @param stream The stream (e.g. process.stderr)\n     * @returns true if the stream should have colourization enabled\n     */ static isEnabled(stream) {\n        return stream && // May happen in browsers.\n        stream.isTTY && (typeof stream.getColorDepth === \"function\" ? stream.getColorDepth() > 2 : true);\n    }\n    static refresh() {\n        Colours.enabled = Colours.isEnabled(process === null || process === void 0 ? void 0 : process.stderr);\n        if (!this.enabled) {\n            Colours.reset = \"\";\n            Colours.bright = \"\";\n            Colours.dim = \"\";\n            Colours.red = \"\";\n            Colours.green = \"\";\n            Colours.yellow = \"\";\n            Colours.blue = \"\";\n            Colours.magenta = \"\";\n            Colours.cyan = \"\";\n            Colours.white = \"\";\n            Colours.grey = \"\";\n        } else {\n            Colours.reset = \"\\x1b[0m\";\n            Colours.bright = \"\\x1b[1m\";\n            Colours.dim = \"\\x1b[2m\";\n            Colours.red = \"\\x1b[31m\";\n            Colours.green = \"\\x1b[32m\";\n            Colours.yellow = \"\\x1b[33m\";\n            Colours.blue = \"\\x1b[34m\";\n            Colours.magenta = \"\\x1b[35m\";\n            Colours.cyan = \"\\x1b[36m\";\n            Colours.white = \"\\x1b[37m\";\n            Colours.grey = \"\\x1b[90m\";\n        }\n    }\n}\nexports.Colours = Colours;\nColours.enabled = false;\nColours.reset = \"\";\nColours.bright = \"\";\nColours.dim = \"\";\nColours.red = \"\";\nColours.green = \"\";\nColours.yellow = \"\";\nColours.blue = \"\";\nColours.magenta = \"\";\nColours.cyan = \"\";\nColours.white = \"\";\nColours.grey = \"\";\nColours.refresh(); //# sourceMappingURL=colours.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/colours.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n__exportStar(__webpack_require__(/*! ./logging-utils */ \"(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\"), exports); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js":
/*!**********************************************************************!*\
  !*** ./node_modules/google-logging-utils/build/src/logging-utils.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2021-2024 Google LLC\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//     https://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function() {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function(o) {\n            var ar = [];\n            for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function(mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) {\n            for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        }\n        __setModuleDefault(result, mod);\n        return result;\n    };\n}();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.env = exports.DebugLogBackendBase = exports.placeholder = exports.AdhocDebugLogger = exports.LogSeverity = void 0;\nexports.getNodeBackend = getNodeBackend;\nexports.getDebugBackend = getDebugBackend;\nexports.getStructuredBackend = getStructuredBackend;\nexports.setBackend = setBackend;\nexports.log = log;\nconst events_1 = __webpack_require__(/*! events */ \"events\");\nconst process = __importStar(__webpack_require__(/*! process */ \"process\"));\nconst util = __importStar(__webpack_require__(/*! util */ \"util\"));\nconst colours_1 = __webpack_require__(/*! ./colours */ \"(rsc)/./node_modules/google-logging-utils/build/src/colours.js\");\n// Some functions (as noted) are based on the Node standard library, from\n// the following file:\n//\n// https://github.com/nodejs/node/blob/main/lib/internal/util/debuglog.js\n/**\n * This module defines an ad-hoc debug logger for Google Cloud Platform\n * client libraries in Node. An ad-hoc debug logger is a tool which lets\n * users use an external, unified interface (in this case, environment\n * variables) to determine what logging they want to see at runtime. This\n * isn't necessarily fed into the console, but is meant to be under the\n * control of the user. The kind of logging that will be produced by this\n * is more like \"call retry happened\", not \"events you'd want to record\n * in Cloud Logger\".\n *\n * More for Googlers implementing libraries with it:\n * go/cloud-client-logging-design\n */ /**\n * Possible log levels. These are a subset of Cloud Observability levels.\n * https://cloud.google.com/logging/docs/reference/v2/rest/v2/LogEntry#LogSeverity\n */ var LogSeverity;\n(function(LogSeverity) {\n    LogSeverity[\"DEFAULT\"] = \"DEFAULT\";\n    LogSeverity[\"DEBUG\"] = \"DEBUG\";\n    LogSeverity[\"INFO\"] = \"INFO\";\n    LogSeverity[\"WARNING\"] = \"WARNING\";\n    LogSeverity[\"ERROR\"] = \"ERROR\";\n})(LogSeverity || (exports.LogSeverity = LogSeverity = {}));\n/**\n * Our logger instance. This actually contains the meat of dealing\n * with log lines, including EventEmitter. This contains the function\n * that will be passed back to users of the package.\n */ class AdhocDebugLogger extends events_1.EventEmitter {\n    /**\n     * @param upstream The backend will pass a function that will be\n     *   called whenever our logger function is invoked.\n     */ constructor(namespace, upstream){\n        super();\n        this.namespace = namespace;\n        this.upstream = upstream;\n        this.func = Object.assign(this.invoke.bind(this), {\n            // Also add an instance pointer back to us.\n            instance: this,\n            // And pull over the EventEmitter functionality.\n            on: (event, listener)=>this.on(event, listener)\n        });\n        // Convenience methods for log levels.\n        this.func.debug = (...args)=>this.invokeSeverity(LogSeverity.DEBUG, ...args);\n        this.func.info = (...args)=>this.invokeSeverity(LogSeverity.INFO, ...args);\n        this.func.warn = (...args)=>this.invokeSeverity(LogSeverity.WARNING, ...args);\n        this.func.error = (...args)=>this.invokeSeverity(LogSeverity.ERROR, ...args);\n        this.func.sublog = (namespace)=>log(namespace, this.func);\n    }\n    invoke(fields, ...args) {\n        // Push out any upstream logger first.\n        if (this.upstream) {\n            try {\n                this.upstream(fields, ...args);\n            } catch (e) {\n            // Swallow exceptions to avoid interfering with other logging.\n            }\n        }\n        // Emit sink events.\n        try {\n            this.emit(\"log\", fields, args);\n        } catch (e) {\n        // Swallow exceptions to avoid interfering with other logging.\n        }\n    }\n    invokeSeverity(severity, ...args) {\n        this.invoke({\n            severity\n        }, ...args);\n    }\n}\nexports.AdhocDebugLogger = AdhocDebugLogger;\n/**\n * This can be used in place of a real logger while waiting for Promises or disabling logging.\n */ exports.placeholder = new AdhocDebugLogger(\"\", ()=>{}).func;\n/**\n * The base class for debug logging backends. It's possible to use this, but the\n * same non-guarantees above still apply (unstable interface, etc).\n *\n * @private\n * @internal\n */ class DebugLogBackendBase {\n    constructor(){\n        var _a;\n        this.cached = new Map();\n        this.filters = [];\n        this.filtersSet = false;\n        // Look for the Node config variable for what systems to enable. We'll store\n        // these for the log method below, which will call setFilters() once.\n        let nodeFlag = (_a = process.env[exports.env.nodeEnables]) !== null && _a !== void 0 ? _a : \"*\";\n        if (nodeFlag === \"all\") {\n            nodeFlag = \"*\";\n        }\n        this.filters = nodeFlag.split(\",\");\n    }\n    log(namespace, fields, ...args) {\n        try {\n            if (!this.filtersSet) {\n                this.setFilters();\n                this.filtersSet = true;\n            }\n            let logger = this.cached.get(namespace);\n            if (!logger) {\n                logger = this.makeLogger(namespace);\n                this.cached.set(namespace, logger);\n            }\n            logger(fields, ...args);\n        } catch (e) {\n            // Silently ignore all errors; we don't want them to interfere with\n            // the user's running app.\n            // e;\n            console.error(e);\n        }\n    }\n}\nexports.DebugLogBackendBase = DebugLogBackendBase;\n// The basic backend. This one definitely works, but it's less feature-filled.\n//\n// Rather than using util.debuglog, this implements the same basic logic directly.\n// The reason for this decision is that debuglog checks the value of the\n// NODE_DEBUG environment variable before any user code runs; we therefore\n// can't pipe our own enables into it (and util.debuglog will never print unless\n// the user duplicates it into NODE_DEBUG, which isn't reasonable).\n//\nclass NodeBackend extends DebugLogBackendBase {\n    constructor(){\n        super(...arguments);\n        // Default to allowing all systems, since we gate earlier based on whether the\n        // variable is empty.\n        this.enabledRegexp = /.*/g;\n    }\n    isEnabled(namespace) {\n        return this.enabledRegexp.test(namespace);\n    }\n    makeLogger(namespace) {\n        if (!this.enabledRegexp.test(namespace)) {\n            return ()=>{};\n        }\n        return (fields, ...args)=>{\n            var _a;\n            // TODO: `fields` needs to be turned into a string here, one way or another.\n            const nscolour = `${colours_1.Colours.green}${namespace}${colours_1.Colours.reset}`;\n            const pid = `${colours_1.Colours.yellow}${process.pid}${colours_1.Colours.reset}`;\n            let level;\n            switch(fields.severity){\n                case LogSeverity.ERROR:\n                    level = `${colours_1.Colours.red}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.INFO:\n                    level = `${colours_1.Colours.magenta}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                case LogSeverity.WARNING:\n                    level = `${colours_1.Colours.yellow}${fields.severity}${colours_1.Colours.reset}`;\n                    break;\n                default:\n                    level = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.DEFAULT;\n                    break;\n            }\n            const msg = util.formatWithOptions({\n                colors: colours_1.Colours.enabled\n            }, ...args);\n            const filteredFields = Object.assign({}, fields);\n            delete filteredFields.severity;\n            const fieldsJson = Object.getOwnPropertyNames(filteredFields).length ? JSON.stringify(filteredFields) : \"\";\n            const fieldsColour = fieldsJson ? `${colours_1.Colours.grey}${fieldsJson}${colours_1.Colours.reset}` : \"\";\n            console.error(\"%s [%s|%s] %s%s\", pid, nscolour, level, msg, fieldsJson ? ` ${fieldsColour}` : \"\");\n        };\n    }\n    // Regexp patterns below are from here:\n    // https://github.com/nodejs/node/blob/c0aebed4b3395bd65d54b18d1fd00f071002ac20/lib/internal/util/debuglog.js#L36\n    setFilters() {\n        const totalFilters = this.filters.join(\",\");\n        const regexp = totalFilters.replace(/[|\\\\{}()[\\]^$+?.]/g, \"\\\\$&\").replace(/\\*/g, \".*\").replace(/,/g, \"$|^\");\n        this.enabledRegexp = new RegExp(`^${regexp}$`, \"i\");\n    }\n}\n/**\n * @returns A backend based on Node util.debuglog; this is the default.\n */ function getNodeBackend() {\n    return new NodeBackend();\n}\nclass DebugBackend extends DebugLogBackendBase {\n    constructor(pkg){\n        super();\n        this.debugPkg = pkg;\n    }\n    makeLogger(namespace) {\n        const debugLogger = this.debugPkg(namespace);\n        return (fields, ...args)=>{\n            // TODO: `fields` needs to be turned into a string here.\n            debugLogger(args[0], ...args.slice(1));\n        };\n    }\n    setFilters() {\n        var _a;\n        const existingFilters = (_a = process.env[\"NODE_DEBUG\"]) !== null && _a !== void 0 ? _a : \"\";\n        process.env[\"NODE_DEBUG\"] = `${existingFilters}${existingFilters ? \",\" : \"\"}${this.filters.join(\",\")}`;\n    }\n}\n/**\n * Creates a \"debug\" package backend. The user must call require('debug') and pass\n * the resulting object to this function.\n *\n * ```\n *  setBackend(getDebugBackend(require('debug')))\n * ```\n *\n * https://www.npmjs.com/package/debug\n *\n * Note: Google does not explicitly endorse or recommend this package; it's just\n * being provided as an option.\n *\n * @returns A backend based on the npm \"debug\" package.\n */ function getDebugBackend(debugPkg) {\n    return new DebugBackend(debugPkg);\n}\n/**\n * This pretty much works like the Node logger, but it outputs structured\n * logging JSON matching Google Cloud's ingestion specs. Rather than handling\n * its own output, it wraps another backend. The passed backend must be a subclass\n * of `DebugLogBackendBase` (any of the backends exposed by this package will work).\n */ class StructuredBackend extends DebugLogBackendBase {\n    constructor(upstream){\n        var _a;\n        super();\n        this.upstream = (_a = upstream) !== null && _a !== void 0 ? _a : undefined;\n    }\n    makeLogger(namespace) {\n        var _a;\n        const debugLogger = (_a = this.upstream) === null || _a === void 0 ? void 0 : _a.makeLogger(namespace);\n        return (fields, ...args)=>{\n            var _a;\n            const severity = (_a = fields.severity) !== null && _a !== void 0 ? _a : LogSeverity.INFO;\n            const json = Object.assign({\n                severity,\n                message: util.format(...args)\n            }, fields);\n            const jsonString = JSON.stringify(json);\n            if (debugLogger) {\n                debugLogger(fields, jsonString);\n            } else {\n                console.log(\"%s\", jsonString);\n            }\n        };\n    }\n    setFilters() {\n        var _a;\n        (_a = this.upstream) === null || _a === void 0 ? void 0 : _a.setFilters();\n    }\n}\n/**\n * Creates a \"structured logging\" backend. This pretty much works like the\n * Node logger, but it outputs structured logging JSON matching Google\n * Cloud's ingestion specs instead of plain text.\n *\n * ```\n *  setBackend(getStructuredBackend())\n * ```\n *\n * @param upstream If you want to use something besides the Node backend to\n *   write the actual log lines into, pass that here.\n * @returns A backend based on Google Cloud structured logging.\n */ function getStructuredBackend(upstream) {\n    return new StructuredBackend(upstream);\n}\n/**\n * The environment variables that we standardized on, for all ad-hoc logging.\n */ exports.env = {\n    /**\n     * Filter wildcards specific to the Node syntax, and similar to the built-in\n     * utils.debuglog() environment variable. If missing, disables logging.\n     */ nodeEnables: \"GOOGLE_SDK_NODE_LOGGING\"\n};\n// Keep a copy of all namespaced loggers so users can reliably .on() them.\n// Note that these cached functions will need to deal with changes in the backend.\nconst loggerCache = new Map();\n// Our current global backend. This might be:\nlet cachedBackend = undefined;\n/**\n * Set the backend to use for our log output.\n * - A backend object\n * - null to disable logging\n * - undefined for \"nothing yet\", defaults to the Node backend\n *\n * @param backend Results from one of the get*Backend() functions.\n */ function setBackend(backend) {\n    cachedBackend = backend;\n    loggerCache.clear();\n}\n/**\n * Creates a logging function. Multiple calls to this with the same namespace\n * will produce the same logger, with the same event emitter hooks.\n *\n * Namespaces can be a simple string (\"system\" name), or a qualified string\n * (system:subsystem), which can be used for filtering, or for \"system:*\".\n *\n * @param namespace The namespace, a descriptive text string.\n * @returns A function you can call that works similar to console.log().\n */ function log(namespace, parent) {\n    // If the enable environment variable isn't set, do nothing. The user\n    // can still choose to set a backend of their choice using the manual\n    // `setBackend()`.\n    if (!cachedBackend) {\n        const enablesFlag = process.env[exports.env.nodeEnables];\n        if (!enablesFlag) {\n            return exports.placeholder;\n        }\n    }\n    // This might happen mostly if the typings are dropped in a user's code,\n    // or if they're calling from JavaScript.\n    if (!namespace) {\n        return exports.placeholder;\n    }\n    // Handle sub-loggers.\n    if (parent) {\n        namespace = `${parent.instance.namespace}:${namespace}`;\n    }\n    // Reuse loggers so things like event sinks are persistent.\n    const existing = loggerCache.get(namespace);\n    if (existing) {\n        return existing.func;\n    }\n    // Do we have a backend yet?\n    if (cachedBackend === null) {\n        // Explicitly disabled.\n        return exports.placeholder;\n    } else if (cachedBackend === undefined) {\n        // One hasn't been made yet, so default to Node.\n        cachedBackend = getNodeBackend();\n    }\n    // The logger is further wrapped so we can handle the backend changing out.\n    const logger = (()=>{\n        let previousBackend = undefined;\n        const newLogger = new AdhocDebugLogger(namespace, (fields, ...args)=>{\n            if (previousBackend !== cachedBackend) {\n                // Did the user pass a custom backend?\n                if (cachedBackend === null) {\n                    // Explicitly disabled.\n                    return;\n                } else if (cachedBackend === undefined) {\n                    // One hasn't been made yet, so default to Node.\n                    cachedBackend = getNodeBackend();\n                }\n                previousBackend = cachedBackend;\n            }\n            cachedBackend === null || cachedBackend === void 0 ? void 0 : cachedBackend.log(namespace, fields, ...args);\n        });\n        return newLogger;\n    })();\n    loggerCache.set(namespace, logger);\n    return logger.func;\n} //# sourceMappingURL=logging-utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/google-logging-utils/build/src/logging-utils.js\n");

/***/ })

};
;