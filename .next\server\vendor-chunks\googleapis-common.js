"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/googleapis-common";
exports.ids = ["vendor-chunks/googleapis-common"];
exports.modules = {

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apiIndex.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getAPI = getAPI;\nfunction getAPI(api, options, // eslint-disable-next-line @typescript-eslint/no-explicit-any\nversions, context) {\n    let version;\n    if (typeof options === \"string\") {\n        version = options;\n        options = {};\n    } else if (typeof options === \"object\") {\n        version = options.version;\n        delete options.version;\n    } else {\n        throw new Error(\"Argument error: Accepts only string or object\");\n    }\n    try {\n        const ctr = versions[version];\n        const ep = new ctr(options, context);\n        return Object.freeze(ep);\n    } catch (e) {\n        throw new Error(`Unable to load endpoint ${api}(\"${version}\"): ${e.message}`);\n    }\n} //# sourceMappingURL=apiIndex.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/apirequest.js":
/*!****************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/apirequest.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.createAPIRequest = createAPIRequest;\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst urlTemplate = __webpack_require__(/*! url-template */ \"(rsc)/./node_modules/url-template/lib/url-template.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst isbrowser_1 = __webpack_require__(/*! ./isbrowser */ \"(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\");\nconst h2 = __webpack_require__(/*! ./http2 */ \"(rsc)/./node_modules/googleapis-common/build/src/http2.js\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/googleapis-common/build/src/util.js\");\n// eslint-disable-next-line @typescript-eslint/no-var-requires\nconst pkg = __webpack_require__(/*! ../../package.json */ \"(rsc)/./node_modules/googleapis-common/package.json\");\nconst randomUUID = ()=>globalThis.crypto?.randomUUID() || (__webpack_require__(/*! crypto */ \"crypto\").randomUUID)();\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction isReadableStream(obj) {\n    return obj !== null && typeof obj === \"object\" && typeof obj.pipe === \"function\" && obj.readable !== false && typeof obj._read === \"function\" && typeof obj._readableState === \"object\";\n}\nfunction getMissingParams(params, required) {\n    const missing = new Array();\n    required.forEach((param)=>{\n        // Is the required param in the params object?\n        if (params[param] === undefined) {\n            missing.push(param);\n        }\n    });\n    // If there are any required params missing, return their names in array,\n    // otherwise return null\n    return missing.length > 0 ? missing : null;\n}\nfunction createAPIRequest(parameters, callback) {\n    if (callback) {\n        createAPIRequestAsync(parameters).then((r)=>callback(null, r), callback);\n    } else {\n        return createAPIRequestAsync(parameters);\n    }\n}\nasync function createAPIRequestAsync(parameters) {\n    // Combine the GaxiosOptions options passed with this specific\n    // API call with the global options configured at the API Context\n    // level, or at the global level.\n    const options = extend(true, {}, parameters.context.google?._options || {}, parameters.context._options || {}, parameters.options);\n    const params = extend(true, {}, options.params, parameters.params);\n    options.userAgentDirectives = options.userAgentDirectives || [];\n    const media = params.media || {};\n    /**\n     * In a previous version of this API, the request body was stuffed in a field\n     * named `resource`.  This caused lots of problems, because it's not uncommon\n     * to have an actual named parameter required which is also named `resource`.\n     * This meant that users would have to use `resource_` in those cases, which\n     * pretty much nobody figures out on their own. The request body is now\n     * documented as being in the `requestBody` property, but we also need to keep\n     * using `resource` for reasons of back-compat. Cases that need to be covered\n     * here:\n     * - user provides just a `resource` with a request body\n     * - user provides both a `resource` and a `resource_`\n     * - user provides just a `requestBody`\n     * - user provides both a `requestBody` and a `resource`\n     */ let resource = params.requestBody;\n    if (!params.requestBody && params.resource && (!parameters.requiredParams.includes(\"resource\") || typeof params.resource !== \"string\")) {\n        resource = params.resource;\n        delete params.resource;\n    }\n    delete params.requestBody;\n    let authClient = params.auth || options.auth;\n    const defaultMime = typeof media.body === \"string\" ? \"text/plain\" : \"application/octet-stream\";\n    delete params.media;\n    delete params.auth;\n    // Grab headers from user provided options\n    const headers = (0, util_1.headersToClassicHeaders)(params.headers || {});\n    populateAPIHeader(headers, options.apiVersion);\n    delete params.headers;\n    // Un-alias parameters that were modified due to conflicts with reserved names\n    Object.keys(params).forEach((key)=>{\n        if (key.slice(-1) === \"_\") {\n            const newKey = key.slice(0, -1);\n            params[newKey] = params[key];\n            delete params[key];\n        }\n    });\n    // Check for missing required parameters in the API request\n    const missingParams = getMissingParams(params, parameters.requiredParams);\n    if (missingParams) {\n        // Some params are missing - stop further operations and inform the\n        // developer which required params are not included in the request\n        throw new Error(\"Missing required parameters: \" + missingParams.join(\", \"));\n    }\n    // Parse urls\n    if (options.url) {\n        let url = options.url;\n        if (typeof url === \"object\") {\n            url = url.toString();\n        }\n        options.url = urlTemplate.parse(url).expand(params);\n    }\n    if (parameters.mediaUrl) {\n        parameters.mediaUrl = urlTemplate.parse(parameters.mediaUrl).expand(params);\n    }\n    // Rewrite url if rootUrl is globally set\n    if (parameters.context._options.rootUrl !== undefined && options.url !== undefined) {\n        const originalUrl = new URL(options.url);\n        const path = originalUrl.href.substr(originalUrl.origin.length);\n        options.url = new URL(path, parameters.context._options.rootUrl).href;\n    }\n    // When forming the querystring, override the serializer so that array\n    // values are serialized like this:\n    // myParams: ['one', 'two'] ---> 'myParams=one&myParams=two'\n    // This serializer also encodes spaces in the querystring as `%20`,\n    // whereas the default serializer in gaxios encodes to a `+`.\n    options.paramsSerializer = (params)=>{\n        return qs.stringify(params, {\n            arrayFormat: \"repeat\"\n        });\n    };\n    // delete path params from the params object so they do not end up in query\n    parameters.pathParams.forEach((param)=>delete params[param]);\n    // if authClient is actually a string, use it as an API KEY\n    if (typeof authClient === \"string\") {\n        params.key = params.key || authClient;\n        authClient = undefined;\n    }\n    function multipartUpload(multipart) {\n        const boundary = randomUUID();\n        const finale = `--${boundary}--`;\n        const rStream = new stream.PassThrough({\n            flush (callback) {\n                this.push(\"\\r\\n\");\n                this.push(finale);\n                callback();\n            }\n        });\n        const pStream = new ProgressStream();\n        const isStream = isReadableStream(multipart[1].body);\n        headers[\"content-type\"] = `multipart/related; boundary=${boundary}`;\n        for (const part of multipart){\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part[\"content-type\"]}\\r\\n\\r\\n`;\n            rStream.push(preamble);\n            if (typeof part.body === \"string\") {\n                rStream.push(part.body);\n                rStream.push(\"\\r\\n\");\n            } else {\n                // Gaxios does not natively support onUploadProgress in node.js.\n                // Pipe through the pStream first to read the number of bytes read\n                // for the purpose of tracking progress.\n                pStream.on(\"progress\", (bytesRead)=>{\n                    if (options.onUploadProgress) {\n                        options.onUploadProgress({\n                            bytesRead\n                        });\n                    }\n                });\n                part.body.pipe(pStream).pipe(rStream);\n            }\n        }\n        if (!isStream) {\n            rStream.push(finale);\n            rStream.push(null);\n        }\n        options.data = rStream;\n    }\n    function browserMultipartUpload(multipart) {\n        const boundary = randomUUID();\n        const finale = `--${boundary}--`;\n        headers[\"content-type\"] = `multipart/related; boundary=${boundary}`;\n        let content = \"\";\n        for (const part of multipart){\n            const preamble = `--${boundary}\\r\\ncontent-type: ${part[\"content-type\"]}\\r\\n\\r\\n`;\n            content += preamble;\n            if (typeof part.body === \"string\") {\n                content += part.body;\n                content += \"\\r\\n\";\n            }\n        }\n        content += finale;\n        options.data = content;\n    }\n    if (parameters.mediaUrl && media.body) {\n        options.url = parameters.mediaUrl;\n        if (resource) {\n            params.uploadType = \"multipart\";\n            const multipart = [\n                {\n                    \"content-type\": \"application/json\",\n                    body: JSON.stringify(resource)\n                },\n                {\n                    \"content-type\": media.mimeType || resource && resource.mimeType || defaultMime,\n                    body: media.body\n                }\n            ];\n            if (!(0, isbrowser_1.isBrowser)()) {\n                // gaxios doesn't support multipart/related uploads, so it has to\n                // be implemented here.\n                multipartUpload(multipart);\n            } else {\n                browserMultipartUpload(multipart);\n            }\n        } else {\n            params.uploadType = \"media\";\n            Object.assign(headers, {\n                \"content-type\": media.mimeType || defaultMime\n            });\n            options.data = media.body;\n        }\n    } else {\n        options.data = resource || undefined;\n    }\n    options.headers = gaxios_1.Gaxios.mergeHeaders(options.headers || {}, headers);\n    options.params = params;\n    if (!(0, isbrowser_1.isBrowser)()) {\n        options.headers.set(\"Accept-Encoding\", \"gzip\");\n        options.userAgentDirectives.push({\n            product: \"google-api-nodejs-client\",\n            version: pkg.version,\n            comment: \"gzip\"\n        });\n        const userAgent = options.userAgentDirectives.map((d)=>{\n            let line = `${d.product}/${d.version}`;\n            if (d.comment) {\n                line += ` (${d.comment})`;\n            }\n            return line;\n        }).join(\" \");\n        options.headers.set(\"User-Agent\", userAgent);\n    }\n    // By default gaxios treats any 2xx as valid, and all non 2xx status\n    // codes as errors.  This is a problem for HTTP 304s when used along\n    // with an eTag.\n    if (!options.validateStatus) {\n        options.validateStatus = (status)=>{\n            return status >= 200 && status < 300 || status === 304;\n        };\n    }\n    // Retry by default\n    options.retry = options.retry === undefined ? true : options.retry;\n    delete options.auth; // is overridden by our auth code\n    // Determine TPC universe\n    if (options.universeDomain && options.universe_domain && options.universeDomain !== options.universe_domain) {\n        throw new Error(\"Please set either universe_domain or universeDomain, but not both.\");\n    }\n    const universeDomainEnvVar = typeof process === \"object\" && typeof process.env === \"object\" ? process.env[\"GOOGLE_CLOUD_UNIVERSE_DOMAIN\"] : undefined;\n    const universeDomain = options.universeDomain ?? options.universe_domain ?? universeDomainEnvVar ?? \"googleapis.com\";\n    // Update URL to point to the given TPC universe\n    if (universeDomain !== \"googleapis.com\" && options.url) {\n        const url = new URL(options.url);\n        if (url.hostname.endsWith(\".googleapis.com\")) {\n            url.hostname = url.hostname.replace(/googleapis\\.com$/, universeDomain);\n            options.url = url.toString();\n        }\n    }\n    // An empty params would add a querystring on a spec-compliant serializer\n    if (!Object.keys(options.params).length) {\n        delete options.params;\n        delete options.paramsSerializer;\n    }\n    // Perform the HTTP request.  NOTE: this function used to return a\n    // mikeal/request object. Since the transition to Axios, the method is\n    // now void.  This may be a source of confusion for users upgrading from\n    // version 24.0 -> 25.0 or up.\n    if (authClient && typeof authClient === \"object\") {\n        // Validate TPC universe\n        const universeFromAuth = typeof authClient.getUniverseDomain === \"function\" ? await authClient.getUniverseDomain() : undefined;\n        if (universeFromAuth && universeDomain !== universeFromAuth) {\n            throw new Error(`The configured universe domain (${universeDomain}) does not match the universe domain found in the credentials (${universeFromAuth}). ` + \"If you haven't configured the universe domain explicitly, googleapis.com is the default.\");\n        }\n        if (options.http2) {\n            const authHeaders = await authClient.getRequestHeaders(options.url);\n            const mooOpts = Object.assign({}, options);\n            mooOpts.headers = gaxios_1.Gaxios.mergeHeaders(mooOpts.headers, authHeaders);\n            return h2.request(mooOpts);\n        } else {\n            const res = await authClient.request(options);\n            return (0, util_1.marshallGaxiosResponse)(res);\n        }\n    } else {\n        return new gaxios_1.Gaxios().request(options).then((res)=>(0, util_1.marshallGaxiosResponse)(res));\n    }\n}\n/**\n * Basic Passthrough Stream that records the number of bytes read\n * every time the cursor is moved.\n */ class ProgressStream extends stream.Transform {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _transform(chunk, encoding, callback) {\n        this.bytesRead += chunk.length;\n        this.emit(\"progress\", this.bytesRead);\n        this.push(chunk);\n        callback();\n    }\n    constructor(...args){\n        super(...args);\n        this.bytesRead = 0;\n    }\n}\nfunction populateAPIHeader(headers, apiVersion) {\n    // TODO: we should eventually think about adding browser support for this\n    // populating the gl-web header (web support should also be added to\n    // google-auth-library-nodejs).\n    if (!(0, isbrowser_1.isBrowser)()) {\n        headers[\"x-goog-api-client\"] = `gdcl/${pkg.version} gl-node/${process.versions.node}`;\n    }\n    if (apiVersion) {\n        headers[\"x-goog-api-version\"] = apiVersion;\n    }\n} //# sourceMappingURL=apirequest.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/authplus.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/authplus.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.AuthPlus = void 0;\nconst google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nclass AuthPlus extends google_auth_library_1.GoogleAuth {\n    /**\n     * Override getClient(), memoizing an instance of auth for\n     * subsequent calls to getProjectId().\n     */ async getClient(options) {\n        this._cachedAuth = new google_auth_library_1.GoogleAuth(options);\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return this._cachedAuth.getClient();\n    }\n    getProjectId(callback) {\n        if (callback) {\n            return this._cachedAuth ? this._cachedAuth.getProjectId(callback) : super.getProjectId(callback);\n        } else {\n            return this._cachedAuth ? this._cachedAuth.getProjectId() : super.getProjectId();\n        }\n    }\n    constructor(...args){\n        super(...args);\n        this.JWT = google_auth_library_1.JWT;\n        this.Compute = google_auth_library_1.Compute;\n        this.OAuth2 = google_auth_library_1.OAuth2Client;\n        this.GoogleAuth = google_auth_library_1.GoogleAuth;\n        this.AwsClient = google_auth_library_1.AwsClient;\n        this.IdentityPoolClient = google_auth_library_1.IdentityPoolClient;\n        this.ExternalAccountClient = google_auth_library_1.ExternalAccountClient;\n    }\n}\nexports.AuthPlus = AuthPlus; //# sourceMappingURL=authplus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/authplus.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/discovery.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/discovery.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Discovery = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nconst resolve = __webpack_require__(/*! url */ \"url\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nconst endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nconst readFile = util.promisify(fs.readFile);\nclass Discovery {\n    /**\n     * Discovery for discovering API endpoints\n     *\n     * @param options Options for discovery\n     */ constructor(options){\n        this.transporter = new gaxios_1.Gaxios();\n        this.options = options || {};\n    }\n    /**\n     * Generate and Endpoint from an endpoint schema object.\n     *\n     * @param schema The schema from which to generate the Endpoint.\n     * @return A function that creates an endpoint.\n     */ makeEndpoint(schema) {\n        return (options)=>{\n            const ep = new endpoint_1.Endpoint(options);\n            ep.applySchema(ep, schema, schema, ep);\n            return ep;\n        };\n    }\n    /**\n     * Log output of generator. Works just like console.log\n     */ log(...args) {\n        if (this.options && this.options.debug) {\n            console.log(...args);\n        }\n    }\n    /**\n     * Generate all APIs and return as in-memory object.\n     * @param discoveryUrl\n     */ async discoverAllAPIs(discoveryUrl) {\n        const headers = new Headers(this.options.includePrivate ? {} : {\n            \"X-User-Ip\": \"0.0.0.0\"\n        });\n        const res = await this.transporter.request({\n            url: discoveryUrl,\n            headers\n        });\n        const items = res.data.items;\n        const apis = await Promise.all(items.map(async (api)=>{\n            const endpointCreator = await this.discoverAPI(api.discoveryRestUrl);\n            return {\n                api,\n                endpointCreator\n            };\n        }));\n        const versionIndex = {};\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const apisIndex = {};\n        for (const set of apis){\n            if (!apisIndex[set.api.name]) {\n                versionIndex[set.api.name] = {};\n                apisIndex[set.api.name] = (options)=>{\n                    const type = typeof options;\n                    let version;\n                    if (type === \"string\") {\n                        version = options;\n                        options = {};\n                    } else if (type === \"object\") {\n                        version = options.version;\n                        delete options.version;\n                    } else {\n                        throw new Error(\"Argument error: Accepts only string or object\");\n                    }\n                    try {\n                        const ep = set.endpointCreator(options, this);\n                        return Object.freeze(ep); // create new & freeze\n                    } catch (e) {\n                        throw new Error(util.format('Unable to load endpoint %s(\"%s\"): %s', set.api.name, version, e.message));\n                    }\n                };\n            }\n            versionIndex[set.api.name][set.api.version] = set.endpointCreator;\n        }\n        return apisIndex;\n    }\n    /**\n     * Generate API file given discovery URL\n     *\n     * @param apiDiscoveryUrl URL or filename of discovery doc for API\n     * @returns A promise that resolves with a function that creates the endpoint\n     */ async discoverAPI(apiDiscoveryUrl) {\n        if (typeof apiDiscoveryUrl === \"string\") {\n            const parts = resolve.parse(apiDiscoveryUrl);\n            if (apiDiscoveryUrl && !parts.protocol) {\n                this.log(\"Reading from file \" + apiDiscoveryUrl);\n                const file = await readFile(apiDiscoveryUrl, {\n                    encoding: \"utf8\"\n                });\n                return this.makeEndpoint(JSON.parse(file));\n            } else {\n                this.log(\"Requesting \" + apiDiscoveryUrl);\n                const res = await this.transporter.request({\n                    url: apiDiscoveryUrl\n                });\n                return this.makeEndpoint(res.data);\n            }\n        } else {\n            const options = apiDiscoveryUrl;\n            this.log(\"Requesting \" + options.url);\n            const url = options.url;\n            delete options.url;\n            const parameters = {\n                options: {\n                    url,\n                    method: \"GET\"\n                },\n                requiredParams: [],\n                pathParams: [],\n                params: options,\n                context: {\n                    google: {\n                        _options: {}\n                    },\n                    _options: {}\n                }\n            };\n            const res = await (0, apirequest_1.createAPIRequest)(parameters);\n            return this.makeEndpoint(res.data);\n        }\n    }\n}\nexports.Discovery = Discovery; //# sourceMappingURL=discovery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/discovery.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/endpoint.js":
/*!**************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/endpoint.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Endpoint = void 0;\nconst apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nclass Endpoint {\n    constructor(options){\n        this._options = options || {};\n    }\n    /**\n     * Given a schema, add methods and resources to a target.\n     *\n     * @param {object} target The target to which to apply the schema.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods and\n     * resources.\n     * @param {object} context The context to add to each method.\n     */ applySchema(target, rootSchema, schema, context) {\n        this.applyMethodsFromSchema(target, rootSchema, schema, context);\n        if (schema.resources) {\n            for(const resourceName in schema.resources){\n                if (Object.prototype.hasOwnProperty.call(schema.resources, resourceName)) {\n                    const resource = schema.resources[resourceName];\n                    if (!target[resourceName]) {\n                        target[resourceName] = {};\n                    }\n                    this.applySchema(target[resourceName], rootSchema, resource, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a schema, add methods to a target.\n     *\n     * @param {object} target The target to which to apply the methods.\n     * @param {object} rootSchema The top-level schema, so we don't lose track of it\n     * during recursion.\n     * @param {object} schema The current schema from which to extract methods.\n     * @param {object} context The context to add to each method.\n     */ applyMethodsFromSchema(target, rootSchema, schema, context) {\n        if (schema.methods) {\n            for(const name in schema.methods){\n                if (Object.prototype.hasOwnProperty.call(schema.methods, name)) {\n                    const method = schema.methods[name];\n                    target[name] = this.makeMethod(rootSchema, method, context);\n                }\n            }\n        }\n    }\n    /**\n     * Given a method schema, add a method to a target.\n     *\n     * @param target The target to which to add the method.\n     * @param schema The top-level schema that contains the rootUrl, etc.\n     * @param method The method schema from which to generate the method.\n     * @param context The context to add to the method.\n     */ makeMethod(schema, method, context) {\n        return (paramsOrCallback, callback)=>{\n            const params = typeof paramsOrCallback === \"function\" ? {} : paramsOrCallback;\n            callback = typeof paramsOrCallback === \"function\" ? paramsOrCallback : callback;\n            const schemaUrl = buildurl(schema.rootUrl + schema.servicePath + method.path);\n            const parameters = {\n                options: {\n                    url: schemaUrl.substring(1, schemaUrl.length - 1),\n                    method: method.httpMethod,\n                    apiVersion: method.apiVersion\n                },\n                params,\n                requiredParams: method.parameterOrder || [],\n                pathParams: this.getPathParams(method.parameters),\n                context\n            };\n            if (method.mediaUpload && method.mediaUpload.protocols && method.mediaUpload.protocols.simple && method.mediaUpload.protocols.simple.path) {\n                const mediaUrl = buildurl(schema.rootUrl + method.mediaUpload.protocols.simple.path);\n                parameters.mediaUrl = mediaUrl.substring(1, mediaUrl.length - 1);\n            }\n            if (!callback) {\n                return (0, apirequest_1.createAPIRequest)(parameters);\n            }\n            (0, apirequest_1.createAPIRequest)(parameters, callback);\n            return;\n        };\n    }\n    getPathParams(params) {\n        const pathParams = new Array();\n        if (typeof params !== \"object\") {\n            params = {};\n        }\n        Object.keys(params).forEach((key)=>{\n            if (params[key].location === \"path\") {\n                pathParams.push(key);\n            }\n        });\n        return pathParams;\n    }\n}\nexports.Endpoint = Endpoint;\n/**\n * Build a string used to create a URL from the discovery doc provided URL.\n * replace double slashes with single slash (except in https://)\n * @private\n * @param  input URL to build from\n * @return Resulting built URL\n */ function buildurl(input) {\n    return input ? `'${input}'`.replace(/([^:]\\/)\\/+/g, \"$1\") : \"\";\n} //# sourceMappingURL=endpoint.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/http2.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/http2.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.sessions = void 0;\nexports.request = request;\nexports.closeSession = closeSession;\nconst http2 = __webpack_require__(/*! http2 */ \"http2\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst url_1 = __webpack_require__(/*! url */ \"url\");\nconst qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/qs/lib/index.js\");\nconst extend = __webpack_require__(/*! extend */ \"(rsc)/./node_modules/extend/index.js\");\nconst stream_1 = __webpack_require__(/*! stream */ \"stream\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst process = __webpack_require__(/*! process */ \"process\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(rsc)/./node_modules/googleapis-common/build/src/util.js\");\nconst { HTTP2_HEADER_CONTENT_ENCODING, HTTP2_HEADER_CONTENT_TYPE, HTTP2_HEADER_METHOD, HTTP2_HEADER_PATH, HTTP2_HEADER_STATUS } = http2.constants;\nconst DEBUG = !!process.env.HTTP2_DEBUG;\n/**\n * List of sessions current in use.\n * @private\n */ exports.sessions = {};\n/**\n * Public method to make an http2 request.\n * @param config - Request options.\n */ async function request(config) {\n    const opts = extend(true, {}, config);\n    opts.validateStatus = opts.validateStatus || validateStatus;\n    opts.responseType = opts.responseType || \"json\";\n    const url = new url_1.URL(opts.url);\n    // Check for an existing session to this host, or go create a new one.\n    const sessionData = _getClient(url.host);\n    // Since we're using this session, clear the timeout handle to ensure\n    // it stays in memory and connected for a while further.\n    if (sessionData.timeoutHandle !== undefined) {\n        clearTimeout(sessionData.timeoutHandle);\n    }\n    // Assemble the querystring based on config.params.  We're using the\n    // `qs` module to make life a little easier.\n    let pathWithQs = url.pathname;\n    if (config.params && Object.keys(config.params).length > 0) {\n        const serializer = config.paramsSerializer || qs.stringify;\n        const q = serializer(opts.params);\n        pathWithQs += `?${q}`;\n    }\n    // Assemble the headers based on basic HTTP2 primitives (path, method) and\n    // custom headers sent from the consumer. Note: the native `Headers` type does\n    // not support HTTP2 header names (e.g. ':status')\n    const headers = (0, util_1.headersToClassicHeaders)(opts.headers);\n    headers[HTTP2_HEADER_PATH] = pathWithQs;\n    headers[HTTP2_HEADER_METHOD] = config.method || \"GET\";\n    opts.headers = headers;\n    // NOTE: This is working around an upstream bug in `apirequest.ts`. The\n    // request path assumes that the `content-type` header is going to be set in\n    // the underlying HTTP Client. This hack provides bug for bug compatability\n    // with this bug in gaxios:\n    // https://github.com/googleapis/gaxios/blob/main/src/gaxios.ts#L202\n    if (!headers[HTTP2_HEADER_CONTENT_TYPE]) {\n        if (opts.responseType !== \"text\") {\n            headers[HTTP2_HEADER_CONTENT_TYPE] = \"application/json\";\n        }\n    }\n    const res = {\n        config,\n        headers: {},\n        status: 0,\n        data: {},\n        statusText: \"\"\n    };\n    const chunks = [];\n    const session = sessionData.session;\n    let req;\n    return new Promise((resolve, reject)=>{\n        try {\n            req = session.request(headers).on(\"response\", (responseHeaders)=>{\n                Object.assign(res, {\n                    headers: responseHeaders,\n                    status: responseHeaders[HTTP2_HEADER_STATUS]\n                });\n                let stream = req;\n                if (responseHeaders[HTTP2_HEADER_CONTENT_ENCODING] === \"gzip\") {\n                    stream = req.pipe(zlib.createGunzip());\n                }\n                if (opts.responseType === \"stream\") {\n                    res.data = stream;\n                    resolve(res);\n                    return;\n                }\n                stream.on(\"data\", (d)=>{\n                    chunks.push(d);\n                }).on(\"error\", (err)=>{\n                    reject(err);\n                    return;\n                }).on(\"end\", ()=>{\n                    const buf = Buffer.concat(chunks);\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    let data = buf;\n                    if (buf) {\n                        if (opts.responseType === \"json\") {\n                            try {\n                                data = JSON.parse(buf.toString(\"utf8\"));\n                            } catch  {\n                                data = buf.toString(\"utf8\");\n                            }\n                        } else if (opts.responseType === \"text\") {\n                            data = buf.toString(\"utf8\");\n                        } else if (opts.responseType === \"arraybuffer\") {\n                            data = buf.buffer;\n                        }\n                        res.data = data;\n                    }\n                    if (!opts.validateStatus(res.status)) {\n                        let message = `Request failed with status code ${res.status}. `;\n                        if (res.data && typeof res.data === \"object\") {\n                            const body = util.inspect(res.data, {\n                                depth: 5\n                            });\n                            message = `${message}\\n'${body}`;\n                        }\n                        reject(new Error(message, {\n                            cause: res\n                        }));\n                    }\n                    resolve(res);\n                    return;\n                });\n            }).on(\"error\", (e)=>{\n                reject(e);\n                return;\n            });\n        } catch (e) {\n            closeSession(url).then(()=>reject(e)).catch(reject);\n            return;\n        }\n        res.request = req;\n        // If data was provided, write it to the request in the form of\n        // a stream, string data, or a basic object.\n        if (config.data) {\n            if (config.data instanceof stream_1.Stream) {\n                config.data.pipe(req);\n            } else if (typeof config.data === \"string\") {\n                const data = Buffer.from(config.data);\n                req.end(data);\n            } else if (typeof config.data === \"object\") {\n                const data = JSON.stringify(config.data);\n                req.end(data);\n            }\n        }\n        // Create a timeout so the Http2Session will be cleaned up after\n        // a period of non-use. 500 milliseconds was chosen because it's\n        // a nice round number, and I don't know what would be a better\n        // choice. Keeping this channel open will hold a file descriptor\n        // which will prevent the process from exiting.\n        sessionData.timeoutHandle = setTimeout(()=>closeSession(url), 500);\n    });\n}\n/**\n * By default, throw for any non-2xx status code\n * @param status - status code from the HTTP response\n */ function validateStatus(status) {\n    return status >= 200 && status < 300;\n}\n/**\n * Obtain an existing h2 session or go create a new one.\n * @param host - The hostname to which the session belongs.\n */ function _getClient(host) {\n    if (!exports.sessions[host]) {\n        if (DEBUG) {\n            console.log(`Creating client for ${host}`);\n        }\n        const session = http2.connect(`https://${host}`);\n        session.on(\"error\", (e)=>{\n            console.error(`*ERROR*: ${e}`);\n            delete exports.sessions[host];\n        }).on(\"goaway\", (errorCode, lastStreamId)=>{\n            console.error(`*GOAWAY*: ${errorCode} : ${lastStreamId}`);\n            delete exports.sessions[host];\n        });\n        exports.sessions[host] = {\n            session\n        };\n    } else {\n        if (DEBUG) {\n            console.log(`Used cached client for ${host}`);\n        }\n    }\n    return exports.sessions[host];\n}\nasync function closeSession(url) {\n    const sessionData = exports.sessions[url.host];\n    if (!sessionData) {\n        return;\n    }\n    const { session } = sessionData;\n    delete exports.sessions[url.host];\n    if (DEBUG) {\n        console.error(`Closing ${url.host}`);\n    }\n    session.close(()=>{\n        if (DEBUG) {\n            console.error(`Closed ${url.host}`);\n        }\n    });\n    setTimeout(()=>{\n        if (session && !session.destroyed) {\n            if (DEBUG) {\n                console.log(`Forcing close ${url.host}`);\n            }\n            if (session) {\n                session.destroy();\n            }\n        }\n    }, 1000);\n} //# sourceMappingURL=http2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/http2.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __exportStar = (void 0) && (void 0).__exportStar || function(m, exports1) {\n    for(var p in m)if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Endpoint = exports.Discovery = exports.AuthPlus = exports.createAPIRequest = exports.getAPI = exports.GaxiosError = exports.Gaxios = exports.AwsClient = exports.IdentityPoolClient = exports.BaseExternalAccountClient = exports.ExternalAccountClient = exports.GoogleAuth = exports.UserRefreshClient = exports.Compute = exports.JWT = exports.OAuth2Client = exports.gaxios = exports.googleAuthLibrary = void 0;\n// re-exporting key dependencies\nexports.googleAuthLibrary = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nexports.gaxios = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nvar google_auth_library_1 = __webpack_require__(/*! google-auth-library */ \"(rsc)/./node_modules/google-auth-library/build/src/index.js\");\nObject.defineProperty(exports, \"OAuth2Client\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.OAuth2Client;\n    }\n}));\nObject.defineProperty(exports, \"JWT\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.JWT;\n    }\n}));\nObject.defineProperty(exports, \"Compute\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.Compute;\n    }\n}));\nObject.defineProperty(exports, \"UserRefreshClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.UserRefreshClient;\n    }\n}));\nObject.defineProperty(exports, \"GoogleAuth\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.GoogleAuth;\n    }\n}));\nObject.defineProperty(exports, \"ExternalAccountClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.ExternalAccountClient;\n    }\n}));\nObject.defineProperty(exports, \"BaseExternalAccountClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.BaseExternalAccountClient;\n    }\n}));\nObject.defineProperty(exports, \"IdentityPoolClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.IdentityPoolClient;\n    }\n}));\nObject.defineProperty(exports, \"AwsClient\", ({\n    enumerable: true,\n    get: function() {\n        return google_auth_library_1.AwsClient;\n    }\n}));\nvar gaxios_1 = __webpack_require__(/*! gaxios */ \"(rsc)/./node_modules/gaxios/build/cjs/src/index.js\");\nObject.defineProperty(exports, \"Gaxios\", ({\n    enumerable: true,\n    get: function() {\n        return gaxios_1.Gaxios;\n    }\n}));\nObject.defineProperty(exports, \"GaxiosError\", ({\n    enumerable: true,\n    get: function() {\n        return gaxios_1.GaxiosError;\n    }\n}));\nvar apiIndex_1 = __webpack_require__(/*! ./apiIndex */ \"(rsc)/./node_modules/googleapis-common/build/src/apiIndex.js\");\nObject.defineProperty(exports, \"getAPI\", ({\n    enumerable: true,\n    get: function() {\n        return apiIndex_1.getAPI;\n    }\n}));\nvar apirequest_1 = __webpack_require__(/*! ./apirequest */ \"(rsc)/./node_modules/googleapis-common/build/src/apirequest.js\");\nObject.defineProperty(exports, \"createAPIRequest\", ({\n    enumerable: true,\n    get: function() {\n        return apirequest_1.createAPIRequest;\n    }\n}));\nvar authplus_1 = __webpack_require__(/*! ./authplus */ \"(rsc)/./node_modules/googleapis-common/build/src/authplus.js\");\nObject.defineProperty(exports, \"AuthPlus\", ({\n    enumerable: true,\n    get: function() {\n        return authplus_1.AuthPlus;\n    }\n}));\nvar discovery_1 = __webpack_require__(/*! ./discovery */ \"(rsc)/./node_modules/googleapis-common/build/src/discovery.js\");\nObject.defineProperty(exports, \"Discovery\", ({\n    enumerable: true,\n    get: function() {\n        return discovery_1.Discovery;\n    }\n}));\nvar endpoint_1 = __webpack_require__(/*! ./endpoint */ \"(rsc)/./node_modules/googleapis-common/build/src/endpoint.js\");\nObject.defineProperty(exports, \"Endpoint\", ({\n    enumerable: true,\n    get: function() {\n        return endpoint_1.Endpoint;\n    }\n}));\n__exportStar(__webpack_require__(/*! ./util */ \"(rsc)/./node_modules/googleapis-common/build/src/util.js\"), exports); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js":
/*!***************************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/isbrowser.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2020 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.isBrowser = isBrowser;\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n} //# sourceMappingURL=isbrowser.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ29vZ2xlYXBpcy1jb21tb24vYnVpbGQvc3JjL2lzYnJvd3Nlci5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDRCQUE0QjtBQUM1QixrRUFBa0U7QUFDbEUsbUVBQW1FO0FBQ25FLDBDQUEwQztBQUMxQyxFQUFFO0FBQ0YsZ0RBQWdEO0FBQ2hELEVBQUU7QUFDRixzRUFBc0U7QUFDdEUsb0VBQW9FO0FBQ3BFLDJFQUEyRTtBQUMzRSxzRUFBc0U7QUFDdEUsaUNBQWlDO0FBQ2pDQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQsaUJBQWlCLEdBQUdFO0FBQ3BCLFNBQVNBO0lBQ0wsT0FBTyxnQkFBa0I7QUFDN0IsRUFDQSxxQ0FBcUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lbWFpbC1jbGllbnQvLi9ub2RlX21vZHVsZXMvZ29vZ2xlYXBpcy1jb21tb24vYnVpbGQvc3JjL2lzYnJvd3Nlci5qcz9mZTUzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuLy8gQ29weXJpZ2h0IDIwMjAgR29vZ2xlIExMQ1xuLy8gTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbi8vIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbi8vIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuLy9cbi8vICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuLy9cbi8vIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmVcbi8vIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbi8vIFdJVEhPVVQgV0FSUkFOVElFUyBPUiBDT05ESVRJT05TIE9GIEFOWSBLSU5ELCBlaXRoZXIgZXhwcmVzcyBvciBpbXBsaWVkLlxuLy8gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2UgZ292ZXJuaW5nIHBlcm1pc3Npb25zIGFuZFxuLy8gbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmlzQnJvd3NlciA9IGlzQnJvd3NlcjtcbmZ1bmN0aW9uIGlzQnJvd3NlcigpIHtcbiAgICByZXR1cm4gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCc7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pc2Jyb3dzZXIuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiaXNCcm93c2VyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/isbrowser.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/build/src/util.js":
/*!**********************************************************!*\
  !*** ./node_modules/googleapis-common/build/src/util.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2025 Google LLC\n// Licensed under the Apache License, Version 2.0 (the \"License\");\n// you may not use this file except in compliance with the License.\n// You may obtain a copy of the License at\n//\n//    http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software\n// distributed under the License is distributed on an \"AS IS\" BASIS,\n// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n// See the License for the specific language governing permissions and\n// limitations under the License.\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.headersToClassicHeaders = headersToClassicHeaders;\nexports.marshallGaxiosResponse = marshallGaxiosResponse;\n/**\n * A utility for converting potential {@link Headers `Headers`} objects to plain headers objects.\n *\n * @param headers any compatible `HeadersInit` (`Headers`, (string, string)[], {})\n * @returns the headers in `Record<string, string>` form.\n */ function headersToClassicHeaders(headers) {\n    let classicHeaders = {};\n    if (headers instanceof Headers) {\n        headers.forEach((value, key)=>{\n            classicHeaders[key] = value;\n        });\n    } else if (Array.isArray(headers)) {\n        for (const [key, value] of headers){\n            classicHeaders[key] = value;\n        }\n    } else {\n        classicHeaders = headers || {};\n    }\n    return classicHeaders;\n}\n/**\n * marshall a GaxiosResponse into a library-friendly type.\n *\n * @param res the Gaxios Response\n * @returns the GaxiosResponse with HTTP2-ready/compatible headers\n */ function marshallGaxiosResponse(res) {\n    return Object.defineProperties(res || {}, {\n        headers: {\n            configurable: true,\n            writable: true,\n            enumerable: true,\n            value: headersToClassicHeaders(res?.headers)\n        }\n    });\n} //# sourceMappingURL=util.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/googleapis-common/build/src/util.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/googleapis-common/package.json":
/*!*****************************************************!*\
  !*** ./node_modules/googleapis-common/package.json ***!
  \*****************************************************/
/***/ ((module) => {

module.exports = JSON.parse('{"name":"googleapis-common","version":"8.0.0","description":"A common tooling library used by the googleapis npm module. You probably don\'t want to use this directly.","repository":"googleapis/nodejs-googleapis-common","main":"build/src/index.js","types":"build/src/index.d.ts","files":["build/src","!build/src/**/*.map"],"scripts":{"prebenchmark":"npm run compile","benchmark":"node build/benchmark/bench.js","compile":"tsc -p .","test":"c8 mocha build/test","system-test":"c8 mocha build/system-test --timeout 600000","presystem-test":"npm run compile","fix":"gts fix","prepare":"npm run compile","pretest":"npm run compile","lint":"gts check","samples-test":"mocha build/samples-test","docs":"jsdoc -c .jsdoc.js","predocs-test":"npm run docs","docs-test":"linkinator docs","webpack":"webpack","browser-test":"karma start","prelint":"cd samples; npm link ../; npm install","clean":"gts clean","precompile":"gts clean"},"keywords":[],"author":"Google LLC","license":"Apache-2.0","dependencies":{"extend":"^3.0.2","gaxios":"^7.0.0-rc.4","google-auth-library":"^10.1.0","qs":"^6.7.0","url-template":"^2.0.8"},"devDependencies":{"@babel/plugin-proposal-private-methods":"^7.18.6","@types/extend":"^3.0.1","@types/mocha":"^10.0.10","@types/mv":"^2.1.0","@types/ncp":"^2.0.1","@types/nock":"^11.0.0","@types/proxyquire":"^1.3.28","@types/qs":"^6.5.3","@types/sinon":"^17.0.0","@types/tmp":"0.2.6","@types/url-template":"^2.0.28","c8":"^10.1.3","codecov":"^3.5.0","gts":"^6.0.2","http2spy":"^2.0.0","is-docker":"^2.0.0","jsdoc":"^4.0.0","jsdoc-fresh":"^3.0.0","jsdoc-region-tag":"^3.0.0","karma":"^6.0.0","karma-chrome-launcher":"^3.0.0","karma-coverage":"^2.0.0","karma-firefox-launcher":"^2.0.0","karma-mocha":"^2.0.0","karma-remap-coverage":"^0.1.5","karma-sourcemap-loader":"^0.4.0","karma-webpack":"^4.0.0","linkinator":"^6.1.2","mocha":"^11.1.0","mv":"^2.1.1","ncp":"^2.0.0","nock":"^14.0.1","null-loader":"^4.0.0","path-to-regexp":"^6.0.0","proxyquire":"^2.1.3","puppeteer":"^18.2.1","sinon":"^17.0.0","tmp":"^0.2.0","ts-loader":"^8.0.0","typescript":"^5.8.2","webpack":"^4.0.0","webpack-cli":"^4.0.0"},"engines":{"node":">=18.0.0"}}');

/***/ })

};
;