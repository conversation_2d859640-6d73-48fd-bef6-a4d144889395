/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { securityposture_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof securityposture_v1.Securityposture;
};
export declare function securityposture(version: 'v1'): securityposture_v1.Securityposture;
export declare function securityposture(options: securityposture_v1.Options): securityposture_v1.Securityposture;
declare const auth: AuthPlus;
export { auth };
export { securityposture_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
