{"name": "email-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "email-server": "node server/email-server.js", "dev:full": "concurrently \"npm run email-server\" \"npm run dev\""}, "dependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/ws": "^8.18.1", "autoprefixer": "^10.0.1", "clsx": "^2.0.0", "concurrently": "^9.2.0", "eslint": "^8", "eslint-config-next": "14.0.0", "google-auth-library": "^10.1.0", "googleapis": "^153.0.0", "imap": "^0.8.19", "lucide-react": "^0.292.0", "mailparser": "^3.7.4", "next": "14.0.0", "nodemailer": "^7.0.5", "postcss": "^8", "react": "^18", "react-dom": "^18", "smtp-server": "^3.14.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^3.3.0", "typescript": "^5", "ws": "^8.18.3"}}