import { NextRequest, NextResponse } from 'next/server'
import GmailService from '@/lib/gmail-api'

// 获取Gmail邮件列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const accessToken = request.headers.get('authorization')?.replace('Bearer ', '')
    const maxResults = parseInt(searchParams.get('maxResults') || '20')
    const query = searchParams.get('query') || undefined

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Access token is required' },
        { status: 401 }
      )
    }

    const gmailService = new GmailService(accessToken)
    const messages = await gmailService.getMessages(maxResults, query)
    
    // 解析邮件内容
    const parsedEmails = messages.map(message => gmailService.parseEmail(message))
    
    return NextResponse.json({
      emails: parsedEmails,
      count: parsedEmails.length
    })
  } catch (error) {
    console.error('Error fetching Gmail messages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch messages' },
      { status: 500 }
    )
  }
}

// 发送Gmail邮件
export async function POST(request: NextRequest) {
  try {
    const accessToken = request.headers.get('authorization')?.replace('Bearer ', '')
    const { to, subject, body, from } = await request.json()

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Access token is required' },
        { status: 401 }
      )
    }

    if (!to || !subject || !body) {
      return NextResponse.json(
        { error: 'To, subject, and body are required' },
        { status: 400 }
      )
    }

    const gmailService = new GmailService(accessToken)
    const result = await gmailService.sendEmail(to, subject, body, from)
    
    return NextResponse.json({
      success: true,
      messageId: result.id
    })
  } catch (error) {
    console.error('Error sending Gmail message:', error)
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    )
  }
}
