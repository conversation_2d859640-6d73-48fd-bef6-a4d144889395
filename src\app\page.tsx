'use client'

import { useState, useEffect } from 'react'
import { Mail, Plus, Settings, Search, Send, Inbox, Star, Archive, Trash2, X, Check, AlertCircle, MoreVertical, Edit, Trash, ExternalLink, Wifi, WifiOff, Bell, BellOff } from 'lucide-react'
import GmailClient from '@/lib/gmail-client'
import RealtimeEmailService, { RealtimeEmail } from '@/lib/realtime-email-service'

// 类型定义
interface EmailAccount {
  id: string
  email: string
  provider: 'gmail' | 'outlook' | '163' | 'qq' | 'other'
  name: string
  isActive: boolean
  unreadCount: number
  accessToken?: string
  refreshToken?: string
  expiresAt?: number
}

interface Email {
  id: number
  from: string
  subject: string
  preview: string
  time: string
  unread: boolean
  starred: boolean
  accountId: string
}

export default function Home() {
  const [selectedEmail, setSelectedEmail] = useState<number | null>(null)
  const [showCompose, setShowCompose] = useState(false)
  const [showAddAccount, setShowAddAccount] = useState(false)
  const [showAccountMenu, setShowAccountMenu] = useState<string | null>(null)
  const [activeAccountId, setActiveAccountId] = useState<string>('1')
  const [gmailClient] = useState(() => new GmailClient())
  const [isLoadingEmails, setIsLoadingEmails] = useState(false)
  const [realtimeService] = useState(() => new RealtimeEmailService())
  const [isServiceConnected, setIsServiceConnected] = useState(false)
  const [realtimeEmails, setRealtimeEmails] = useState<RealtimeEmail[]>([])
  const [notificationsEnabled, setNotificationsEnabled] = useState(false)

  // 邮箱账户数据
  const [emailAccounts, setEmailAccounts] = useState<EmailAccount[]>([])

  // 从localStorage加载账户
  useEffect(() => {
    const loadAccounts = () => {
      try {
        const saved = localStorage.getItem('emailAccounts')
        if (saved) {
          const accounts = JSON.parse(saved)
          setEmailAccounts(accounts)
          if (accounts.length > 0 && !activeAccountId) {
            setActiveAccountId(accounts[0].id)
          }
        } else {
          // 默认示例账户
          const defaultAccounts = [
            {
              id: '1',
              email: '<EMAIL>',
              provider: 'gmail' as const,
              name: '个人Gmail',
              isActive: true,
              unreadCount: 3
            }
          ]
          setEmailAccounts(defaultAccounts)
          setActiveAccountId('1')
        }
      } catch (error) {
        console.error('Error loading accounts:', error)
      }
    }

    loadAccounts()
  }, [])

  // 初始化实时邮件服务
  useEffect(() => {
    const initRealtimeService = async () => {
      try {
        // 连接到实时服务
        const connected = await realtimeService.connect()
        setIsServiceConnected(connected)

        // 设置事件监听
        realtimeService.on('service_status', (data: any) => {
          setIsServiceConnected(data.status === 'connected')
        })

        realtimeService.on('new_email', (email: RealtimeEmail) => {
          console.log('收到新邮件:', email.subject)
          setRealtimeEmails(prev => [email, ...prev])

          // 更新未读计数
          setEmailAccounts(prev => prev.map(acc =>
            acc.id === email.accountId
              ? { ...acc, unreadCount: acc.unreadCount + 1 }
              : acc
          ))
        })

        realtimeService.on('account_status_change', (data: any) => {
          console.log('账户状态变化:', data)
          setEmailAccounts(prev => prev.map(acc =>
            acc.id === data.accountId
              ? { ...acc, isConnected: data.status === 'connected' }
              : acc
          ))
        })

        realtimeService.on('service_error', (data: any) => {
          console.error('服务错误:', data.error)
          setIsServiceConnected(false)
        })

        // 请求通知权限
        const notificationGranted = await realtimeService.requestNotificationPermission()
        setNotificationsEnabled(notificationGranted)

      } catch (error) {
        console.error('初始化实时服务失败:', error)
        setIsServiceConnected(false)
      }
    }

    initRealtimeService()

    // 清理函数
    return () => {
      realtimeService.disconnect()
    }
  }, [])

  // 保存账户到localStorage
  const saveAccounts = (accounts: EmailAccount[]) => {
    try {
      localStorage.setItem('emailAccounts', JSON.stringify(accounts))
      setEmailAccounts(accounts)
    } catch (error) {
      console.error('Error saving accounts:', error)
    }
  }

  // 模拟邮件数据
  const mockEmails: Email[] = [
    {
      id: 1,
      from: '<EMAIL>',
      subject: '项目进度更新',
      preview: '关于本周项目进展的详细报告...',
      time: '10:30',
      unread: true,
      starred: false,
      accountId: '1'
    },
    {
      id: 2,
      from: '<EMAIL>',
      subject: '系统维护通知',
      preview: '我们将在今晚进行系统维护...',
      time: '09:15',
      unread: false,
      starred: true,
      accountId: '1'
    },
    {
      id: 3,
      from: '<EMAIL>',
      subject: '本周技术资讯',
      preview: '最新的技术趋势和开发工具推荐...',
      time: '昨天',
      unread: false,
      starred: false,
      accountId: '1'
    },
    {
      id: 4,
      from: '<EMAIL>',
      subject: '会议安排',
      preview: '明天下午2点开会讨论新项目...',
      time: '11:20',
      unread: true,
      starred: false,
      accountId: '2'
    }
  ]

  // 获取当前活跃账户
  const activeAccount = emailAccounts.find(acc => acc.id === activeAccountId)

  // 合并实时邮件和模拟邮件
  const allEmails = [
    ...realtimeEmails.map(email => ({
      id: parseInt(email.id),
      from: email.from,
      subject: email.subject,
      preview: email.snippet,
      time: new Date(email.date).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      }),
      unread: email.isUnread,
      starred: email.isStarred,
      accountId: email.accountId
    })),
    ...mockEmails
  ]

  // 获取当前账户的邮件
  const currentEmails = allEmails.filter(email => email.accountId === activeAccountId)

  // 获取邮箱提供商图标
  const getProviderIcon = (provider: EmailAccount['provider']) => {
    switch (provider) {
      case 'gmail': return '📧'
      case 'outlook': return '📮'
      case '163': return '📬'
      case 'qq': return '📭'
      default: return '✉️'
    }
  }

  // 切换邮箱账户
  const switchAccount = (accountId: string) => {
    setActiveAccountId(accountId)
    setSelectedEmail(null) // 清除选中的邮件
    setShowAccountMenu(null) // 关闭菜单
  }

  // 删除邮箱账户
  const deleteAccount = (accountId: string) => {
    if (emailAccounts.length <= 1) {
      alert('至少需要保留一个邮箱账户')
      return
    }

    if (confirm('确定要删除这个邮箱账户吗？')) {
      const updatedAccounts = emailAccounts.filter(acc => acc.id !== accountId)
      saveAccounts(updatedAccounts)

      // 如果删除的是当前活跃账户，切换到第一个可用账户
      if (accountId === activeAccountId) {
        if (updatedAccounts.length > 0) {
          setActiveAccountId(updatedAccounts[0].id)
        }
      }

      setShowAccountMenu(null)
      setSelectedEmail(null)
    }
  }

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showAccountMenu) {
        setShowAccountMenu(null)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [showAccountMenu])

  return (
    <div className="flex h-screen bg-white">
      {/* 侧边栏 */}
      <div className="w-64 bg-gray-100 border-r border-gray-200 flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Mail className="w-6 h-6 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-800">邮箱客户端</h1>
            </div>
            <div className="flex items-center gap-2">
              {/* 实时服务状态 */}
              <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                isServiceConnected
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
              }`}>
                {isServiceConnected ? (
                  <Wifi className="w-3 h-3" />
                ) : (
                  <WifiOff className="w-3 h-3" />
                )}
                <span>{isServiceConnected ? '在线' : '离线'}</span>
              </div>
              {/* 通知状态 */}
              <button
                onClick={async () => {
                  const granted = await realtimeService.requestNotificationPermission()
                  setNotificationsEnabled(granted)
                }}
                className={`p-1 rounded ${
                  notificationsEnabled
                    ? 'text-blue-600 hover:bg-blue-50'
                    : 'text-gray-400 hover:bg-gray-50'
                }`}
                title={notificationsEnabled ? '通知已开启' : '点击开启通知'}
              >
                {notificationsEnabled ? (
                  <Bell className="w-4 h-4" />
                ) : (
                  <BellOff className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
          <button
            onClick={() => setShowCompose(true)}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            写邮件
          </button>
        </div>

        {/* 邮箱账户 */}
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-sm font-medium text-gray-600 mb-2">邮箱账户</h3>
          <div className="space-y-1">
            {emailAccounts.map((account) => (
              <div
                key={account.id}
                className={`relative p-2 rounded text-sm transition-colors group ${
                  account.id === activeAccountId
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <div
                  onClick={() => switchAccount(account.id)}
                  className="flex items-center justify-between cursor-pointer"
                >
                  <div className="flex items-center gap-2">
                    <span>{getProviderIcon(account.provider)}</span>
                    <div className="flex flex-col">
                      <span className="font-medium">{account.name}</span>
                      <span className="text-xs opacity-75">{account.email}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    {account.unreadCount > 0 && (
                      <span className="bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                        {account.unreadCount}
                      </span>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setShowAccountMenu(showAccountMenu === account.id ? null : account.id)
                      }}
                      className="p-1 hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <MoreVertical className="w-3 h-3" />
                    </button>
                  </div>
                </div>

                {/* 账户菜单 */}
                {showAccountMenu === account.id && (
                  <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]">
                    <button
                      onClick={() => {
                        // TODO: 实现编辑功能
                        setShowAccountMenu(null)
                      }}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                    >
                      <Edit className="w-3 h-3" />
                      编辑
                    </button>
                    <button
                      onClick={() => deleteAccount(account.id)}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 text-red-600 flex items-center gap-2"
                    >
                      <Trash className="w-3 h-3" />
                      删除
                    </button>
                  </div>
                )}
              </div>
            ))}
            <button
              onClick={() => setShowAddAccount(true)}
              className="w-full p-2 text-left text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2 border border-dashed border-gray-300"
            >
              <Plus className="w-4 h-4" />
              添加邮箱
            </button>
          </div>
        </div>

        {/* 导航菜单 */}
        <div className="flex-1 p-4">
          <div className="mb-3">
            <div className="text-xs text-gray-500 mb-1">当前账户</div>
            <div className="text-sm font-medium text-gray-700">
              {activeAccount?.name || '未选择账户'}
            </div>
          </div>
          <nav className="space-y-1">
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg">
              <Inbox className="w-4 h-4" />
              收件箱
              {activeAccount?.unreadCount && activeAccount.unreadCount > 0 && (
                <span className="ml-auto bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                  {activeAccount.unreadCount}
                </span>
              )}
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg">
              <Star className="w-4 h-4" />
              已加星标
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg">
              <Send className="w-4 h-4" />
              已发送
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg">
              <Archive className="w-4 h-4" />
              归档
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg">
              <Trash2 className="w-4 h-4" />
              垃圾箱
            </a>
          </nav>
        </div>

        {/* 设置 */}
        <div className="p-4 border-t border-gray-200">
          <button className="flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg w-full">
            <Settings className="w-4 h-4" />
            设置
          </button>
        </div>
      </div>

      {/* 邮件列表 */}
      <div className="w-96 border-r border-gray-200 flex flex-col">
        {/* 搜索栏 */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="搜索邮件..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* 邮件列表 */}
        <div className="flex-1 overflow-y-auto">
          {currentEmails.length > 0 ? (
            currentEmails.map((email) => (
              <div
                key={email.id}
                onClick={() => setSelectedEmail(email.id)}
                className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                  selectedEmail === email.id ? 'bg-blue-50 border-blue-200' : ''
                } ${email.unread ? 'bg-white' : 'bg-gray-50'}`}
              >
                <div className="flex items-start justify-between mb-1">
                  <span className={`text-sm ${email.unread ? 'font-semibold text-gray-900' : 'text-gray-700'}`}>
                    {email.from}
                  </span>
                  <span className="text-xs text-gray-500">{email.time}</span>
                </div>
                <div className={`text-sm mb-1 ${email.unread ? 'font-medium text-gray-900' : 'text-gray-700'}`}>
                  {email.subject}
                </div>
                <div className="text-xs text-gray-500 line-clamp-2">
                  {email.preview}
                </div>
                <div className="flex items-center mt-2">
                  {email.unread && (
                    <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                  )}
                  {email.starred && (
                    <Star className="w-3 h-3 text-yellow-500 fill-current" />
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <Mail className="w-12 h-12 mb-2 text-gray-300" />
              <p className="text-sm">此账户暂无邮件</p>
            </div>
          )}
        </div>
      </div>

      {/* 邮件内容区域 */}
      <div className="flex-1 flex flex-col">
        {selectedEmail ? (
          <div className="flex-1 p-6">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                {currentEmails.find(e => e.id === selectedEmail)?.subject}
              </h2>
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-100">
                  <div>
                    <div className="font-medium text-gray-900">
                      {currentEmails.find(e => e.id === selectedEmail)?.from}
                    </div>
                    <div className="text-sm text-gray-500">
                      {currentEmails.find(e => e.id === selectedEmail)?.time}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      来自: {activeAccount?.name} ({activeAccount?.email})
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <Star className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <Archive className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className="prose max-w-none">
                  <p className="text-gray-700 leading-relaxed">
                    这里是邮件的详细内容。在实际应用中，这里会显示完整的邮件正文，
                    包括HTML格式、附件等。目前这只是一个演示界面。
                  </p>
                  <p className="text-gray-700 leading-relaxed mt-4">
                    你可以在这里看到邮件的完整内容，包括格式化的文本、图片、链接等。
                    界面设计简洁明了，便于阅读和操作。
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <Mail className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <p>选择一封邮件来查看内容</p>
              <p className="text-sm mt-2">当前账户: {activeAccount?.name}</p>
            </div>
          </div>
        )}
      </div>

      {/* 写邮件弹窗 */}
      {showCompose && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">写邮件</h3>
              <button
                onClick={() => setShowCompose(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">发件人</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                  {emailAccounts.map((account) => (
                    <option key={account.id} value={account.id}>
                      {account.name} ({account.email})
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">收件人</label>
                <input
                  type="email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入收件人邮箱地址"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">主题</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="邮件主题"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">内容</label>
                <textarea
                  rows={8}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入邮件内容..."
                />
              </div>
            </div>
            <div className="flex items-center justify-between p-4 border-t border-gray-200">
              <button className="text-sm text-gray-600 hover:text-gray-800">
                添加附件
              </button>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowCompose(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  取消
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                  发送
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 添加邮箱账户弹窗 */}
      {showAddAccount && <AddAccountModal />}
    </div>
  )

  // 添加邮箱账户弹窗组件
  function AddAccountModal() {
    const [formData, setFormData] = useState({
      name: '',
      email: '',
      provider: 'gmail' as EmailAccount['provider'],
      password: '',
      customImap: {
        host: '',
        port: 993,
        secure: true
      },
      customSmtp: {
        host: '',
        port: 587,
        secure: false
      }
    })
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [error, setError] = useState('')
    const [showAdvanced, setShowAdvanced] = useState(false)

    // 邮箱服务商配置
    const getProviderConfig = (provider: string) => {
      const configs = {
        gmail: {
          name: 'Gmail',
          imap: { host: 'imap.gmail.com', port: 993, secure: true },
          smtp: { host: 'smtp.gmail.com', port: 587, secure: false }
        },
        outlook: {
          name: 'Outlook',
          imap: { host: 'outlook.office365.com', port: 993, secure: true },
          smtp: { host: 'smtp-mail.outlook.com', port: 587, secure: false }
        },
        '163': {
          name: '163邮箱',
          imap: { host: 'imap.163.com', port: 993, secure: true },
          smtp: { host: 'smtp.163.com', port: 465, secure: true }
        },
        qq: {
          name: 'QQ邮箱',
          imap: { host: 'imap.qq.com', port: 993, secure: true },
          smtp: { host: 'smtp.qq.com', port: 587, secure: false }
        },
        '126': {
          name: '126邮箱',
          imap: { host: 'imap.126.com', port: 993, secure: true },
          smtp: { host: 'smtp.126.com', port: 465, secure: true }
        },
        sina: {
          name: '新浪邮箱',
          imap: { host: 'imap.sina.com', port: 993, secure: true },
          smtp: { host: 'smtp.sina.com', port: 587, secure: false }
        },
        '139': {
          name: '139邮箱',
          imap: { host: 'imap.139.com', port: 993, secure: true },
          smtp: { host: 'smtp.139.com', port: 587, secure: false }
        },
        'yeah': {
          name: 'Yeah邮箱',
          imap: { host: 'imap.yeah.net', port: 993, secure: true },
          smtp: { host: 'smtp.yeah.net', port: 465, secure: true }
        }
      }
      return configs[provider as keyof typeof configs] || null
    }

    // 自动检测邮箱服务商
    const detectProvider = (email: string): EmailAccount['provider'] => {
      const domain = email.split('@')[1]?.toLowerCase()
      const domainMap: Record<string, EmailAccount['provider']> = {
        'gmail.com': 'gmail',
        'googlemail.com': 'gmail',
        'outlook.com': 'outlook',
        'hotmail.com': 'outlook',
        'live.com': 'outlook',
        'msn.com': 'outlook',
        '163.com': '163',
        '126.com': '126',
        'qq.com': 'qq',
        'foxmail.com': 'qq',
        'sina.com': 'sina',
        'sina.cn': 'sina',
        '139.com': '139',
        'yeah.net': 'yeah'
      }
      return domainMap[domain] || 'other'
    }

    // 处理邮箱地址变化
    const handleEmailChange = (email: string) => {
      const provider = detectProvider(email)
      const config = getProviderConfig(provider)

      setFormData(prev => ({
        ...prev,
        email,
        provider,
        name: prev.name || `${config?.name || provider} - ${email}`,
        customImap: config?.imap || prev.customImap,
        customSmtp: config?.smtp || prev.customSmtp
      }))

      // 如果是未知邮箱，自动显示高级设置
      if (provider === 'other') {
        setShowAdvanced(true)
      }
    }

    // 获取认证错误建议
    const getAuthenticationErrorSuggestion = (provider: string, email: string) => {
      switch (provider) {
        case 'gmail':
          return `Gmail需要使用应用专用密码：
1. 开启Google账户两步验证
2. 生成应用专用密码
3. 使用16位应用专用密码，不是登录密码
详细步骤：https://support.google.com/accounts/answer/185833`

        case 'qq':
          return `QQ邮箱需要使用授权码：
1. 登录QQ邮箱网页版
2. 设置 → 账户 → 开启IMAP/SMTP服务
3. 获取授权码（通过短信验证）
4. 使用授权码，不是QQ密码`

        case '163':
          return `163邮箱需要客户端授权密码：
1. 登录163邮箱网页版
2. 设置 → POP3/SMTP/IMAP → 开启服务
3. 设置客户端授权密码
4. 使用客户端授权密码，不是登录密码`

        case '126':
          return `126邮箱需要客户端授权密码：
1. 登录126邮箱网页版
2. 设置 → POP3/SMTP/IMAP → 开启服务
3. 设置客户端授权密码
4. 使用客户端授权密码，不是登录密码`

        case 'outlook':
          return `Outlook邮箱建议：
1. 尝试使用登录密码
2. 如果失败，生成应用专用密码
3. 确保开启了IMAP访问权限
4. 检查Microsoft账户安全设置`

        case 'sina':
          return `新浪邮箱需要：
1. 登录新浪邮箱网页版
2. 设置 → 邮箱设置 → 开启IMAP/SMTP
3. 可能需要设置独立密码
4. 使用独立密码或登录密码`

        default:
          const domain = email.split('@')[1]
          return `${domain} 邮箱认证失败，请检查：
1. 是否开启了IMAP/SMTP服务
2. 是否需要使用应用专用密码或授权码
3. 密码是否正确
4. 是否需要在邮箱设置中允许第三方客户端访问`
      }
    }

    // 获取高级认证建议
    const getAdvancedAuthSuggestion = (provider: string, email: string) => {
      const domain = email.split('@')[1]
      return `${domain} 邮箱认证方法不兼容，请尝试：

🔧 服务器配置检查：
1. 确认IMAP服务器地址和端口正确
2. 检查是否需要使用SSL/TLS或STARTTLS
3. 确认服务器支持的认证方法

🔐 认证设置：
1. 尝试使用应用专用密码而不是登录密码
2. 检查邮箱是否开启了"安全性较低的应用访问"
3. 确认是否需要在邮箱设置中启用IMAP访问

🌐 企业邮箱特殊设置：
1. 联系IT管理员确认服务器配置
2. 可能需要VPN或内网访问
3. 检查是否有特殊的认证要求

💡 常见解决方案：
• 尝试不同的端口：993(SSL)、143(STARTTLS)
• 检查防火墙是否阻止连接
• 确认邮箱服务商是否支持第三方客户端`
    }

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault()
      setIsSubmitting(true)
      setError('')

      // 验证表单
      if (!formData.email || !formData.password) {
        setError('请填写邮箱地址和密码')
        setIsSubmitting(false)
        return
      }

      // 检查邮箱是否已存在
      if (emailAccounts.some(acc => acc.email === formData.email)) {
        setError('该邮箱账户已存在')
        setIsSubmitting(false)
        return
      }

      try {
        // 先测试连接
        setError('正在测试连接...')
        const testResult = await realtimeService.testEmailConnection(
          formData.email,
          formData.password,
          formData.provider,
          formData.customImap
        )

        if (!testResult.success) {
          // 提供详细的错误分析和解决建议
          const errorMessage = testResult.error || '连接测试失败'
          let suggestion = ''

          if (errorMessage.includes('Authentication failed') || errorMessage.includes('No supported authentication method')) {
            suggestion = getAuthenticationErrorSuggestion(formData.provider, formData.email)
          } else if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('ECONNREFUSED')) {
            suggestion = '服务器地址可能不正确，请检查IMAP服务器设置'
          } else if (errorMessage.includes('timeout')) {
            suggestion = '连接超时，请检查网络连接和服务器端口'
          } else if (errorMessage.includes('CERT') || errorMessage.includes('certificate')) {
            suggestion = '证书验证失败，可能是服务器证书问题。请检查服务器配置或联系邮箱服务商'
          } else if (errorMessage.includes('所有认证方法都失败')) {
            suggestion = getAdvancedAuthSuggestion(formData.provider, formData.email)
          }

          setError(`${errorMessage}${suggestion ? '\n\n💡 建议：' + suggestion : ''}`)
          setIsSubmitting(false)
          return
        }

        // 添加账户
        setError('正在添加账户...')
        const newAccount: EmailAccount = {
          id: Date.now().toString(),
          email: formData.email,
          provider: formData.provider,
          name: formData.name || `${formData.provider} - ${formData.email}`,
          isActive: false,
          unreadCount: 0,
          isConnected: false
        }

        const addResult = await realtimeService.addAccount({
          ...newAccount,
          customImap: formData.customImap,
          customSmtp: formData.customSmtp
        })

        if (!addResult.success) {
          setError(addResult.error || '添加账户失败')
          setIsSubmitting(false)
          return
        }

        // 保存到本地存储
        const updatedAccounts = [...emailAccounts, newAccount]
        saveAccounts(updatedAccounts)

        // 关闭弹窗并重置表单
        setShowAddAccount(false)
        setFormData({
          name: '',
          email: '',
          provider: 'gmail',
          password: ''
        })

        // 切换到新添加的账户
        setActiveAccountId(newAccount.id)

      } catch (err: any) {
        setError(err.message || '添加账户失败，请重试')
      } finally {
        setIsSubmitting(false)
      }
    }

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg w-full max-w-md mx-4">
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">添加邮箱账户</h3>
            <button
              onClick={() => setShowAddAccount(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="p-4 space-y-4">
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
                <div className="flex items-start gap-2">
                  <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <div className="whitespace-pre-line">{error}</div>
                    {error.includes('Authentication failed') && (
                      <div className="mt-2 pt-2 border-t border-red-200">
                        <a
                          href="/troubleshoot"
                          target="_blank"
                          className="inline-flex items-center gap-1 text-red-600 hover:text-red-800 font-medium"
                        >
                          <ExternalLink className="w-3 h-3" />
                          查看详细解决方案
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                邮箱地址 <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleEmailChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
                required
              />
              {formData.email && (
                <div className="mt-1">
                  {formData.provider !== 'other' ? (
                    <p className="text-xs text-green-600">
                      ✓ 自动识别为 {getProviderConfig(formData.provider)?.name || formData.provider}
                    </p>
                  ) : (
                    <p className="text-xs text-orange-600">
                      ⚠ 未识别的邮箱，请手动配置IMAP/SMTP设置
                    </p>
                  )}
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                账户名称 (可选)
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="自动生成或自定义名称"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                密码/应用专用密码 <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入密码或应用专用密码"
                required
              />
              <div className="mt-2 text-xs text-gray-600">
                {formData.provider === 'gmail' && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded p-2">
                    <p className="font-medium text-yellow-800">Gmail用户请注意：</p>
                    <p className="text-yellow-700">需要开启两步验证并生成"应用专用密码"</p>
                  </div>
                )}
                {formData.provider === 'qq' && (
                  <div className="bg-blue-50 border border-blue-200 rounded p-2">
                    <p className="font-medium text-blue-800">QQ邮箱用户请注意：</p>
                    <p className="text-blue-700">需要开启IMAP/SMTP服务并使用"授权码"</p>
                  </div>
                )}
                {formData.provider === '163' && (
                  <div className="bg-green-50 border border-green-200 rounded p-2">
                    <p className="font-medium text-green-800">163邮箱用户请注意：</p>
                    <p className="text-green-700">需要开启IMAP/SMTP服务并使用"客户端授权密码"</p>
                  </div>
                )}
                {formData.provider === 'outlook' && (
                  <div className="bg-purple-50 border border-purple-200 rounded p-2">
                    <p className="font-medium text-purple-800">Outlook用户请注意：</p>
                    <p className="text-purple-700">可直接使用登录密码，或使用应用专用密码</p>
                  </div>
                )}
                {!['gmail', 'qq', '163', 'outlook'].includes(formData.provider) && (
                  <p className="text-gray-500">
                    建议使用应用专用密码以提高安全性
                  </p>
                )}
              </div>
            </div>

            {/* 高级设置 */}
            <div className="border-t border-gray-200 pt-4">
              <button
                type="button"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800"
              >
                <span>{showAdvanced ? '隐藏' : '显示'}高级设置</span>
                <span className={`transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}>
                  ▼
                </span>
              </button>

              {showAdvanced && (
                <div className="mt-4 space-y-4 bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm font-medium text-gray-700 mb-3">
                    IMAP/SMTP 服务器配置
                  </div>

                  {/* IMAP配置 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="md:col-span-3">
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        IMAP服务器 (接收邮件)
                      </label>
                    </div>
                    <div>
                      <input
                        type="text"
                        value={formData.customImap.host}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          customImap: { ...prev.customImap, host: e.target.value }
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="imap.example.com"
                      />
                      <label className="block text-xs text-gray-500 mt-1">服务器地址</label>
                    </div>
                    <div>
                      <input
                        type="number"
                        value={formData.customImap.port}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          customImap: { ...prev.customImap, port: parseInt(e.target.value) || 993 }
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="993"
                      />
                      <label className="block text-xs text-gray-500 mt-1">端口</label>
                    </div>
                    <div>
                      <select
                        value={formData.customImap.secure ? 'ssl' : 'starttls'}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          customImap: { ...prev.customImap, secure: e.target.value === 'ssl' }
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="ssl">SSL/TLS</option>
                        <option value="starttls">STARTTLS</option>
                      </select>
                      <label className="block text-xs text-gray-500 mt-1">加密方式</label>
                    </div>
                  </div>

                  {/* SMTP配置 */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <div className="md:col-span-3">
                      <label className="block text-xs font-medium text-gray-600 mb-1">
                        SMTP服务器 (发送邮件)
                      </label>
                    </div>
                    <div>
                      <input
                        type="text"
                        value={formData.customSmtp.host}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          customSmtp: { ...prev.customSmtp, host: e.target.value }
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="smtp.example.com"
                      />
                      <label className="block text-xs text-gray-500 mt-1">服务器地址</label>
                    </div>
                    <div>
                      <input
                        type="number"
                        value={formData.customSmtp.port}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          customSmtp: { ...prev.customSmtp, port: parseInt(e.target.value) || 587 }
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        placeholder="587"
                      />
                      <label className="block text-xs text-gray-500 mt-1">端口</label>
                    </div>
                    <div>
                      <select
                        value={formData.customSmtp.secure ? 'ssl' : 'starttls'}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          customSmtp: { ...prev.customSmtp, secure: e.target.value === 'ssl' }
                        }))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="ssl">SSL/TLS</option>
                        <option value="starttls">STARTTLS</option>
                      </select>
                      <label className="block text-xs text-gray-500 mt-1">加密方式</label>
                    </div>
                  </div>

                  {/* 常用邮箱配置提示 */}
                  <div className="bg-blue-50 border border-blue-200 rounded p-3">
                    <div className="text-xs font-medium text-blue-800 mb-2">常用邮箱配置参考：</div>
                    <div className="text-xs text-blue-700 space-y-1">
                      <div><strong>企业邮箱:</strong> 通常使用 mail.domain.com 或 smtp.domain.com</div>
                      <div><strong>阿里云邮箱:</strong> IMAP: imap.mxhichina.com:993, SMTP: smtp.mxhichina.com:465</div>
                      <div><strong>腾讯企业邮箱:</strong> IMAP: imap.exmail.qq.com:993, SMTP: smtp.exmail.qq.com:465</div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="flex gap-2 pt-4">
              <button
                type="button"
                onClick={() => setShowAddAccount(false)}
                className="flex-1 px-4 py-2 text-gray-600 hover:text-gray-800"
                disabled={isSubmitting}
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !formData.email || !formData.password}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>连接中...</span>
                  </>
                ) : (
                  <>
                    <Check className="w-4 h-4" />
                    <span>测试连接并添加</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  }
}
